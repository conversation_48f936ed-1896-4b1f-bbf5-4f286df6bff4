/*!
  Theme: He<PERSON>ch Light
  Author: <PERSON> (<EMAIL>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#5a496e;background:#feffff}.hljs ::selection,.hljs::selection{background-color:#7b6d8b;color:#5a496e}.hljs-comment{color:#9c92a8}.hljs-tag{color:#ddd6e5}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#5a496e}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#27d9d5}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#bdb6c5}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#5ba2b6}.hljs-strong{font-weight:700;color:#5ba2b6}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#f80059}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#c33678}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#47f9f5}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#bd0152}.hljs-emphasis{color:#bd0152;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#dedae2}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
