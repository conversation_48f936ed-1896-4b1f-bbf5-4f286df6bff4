/*!
  Theme: Apprentice
  Author: romainl
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#bcbcbc;background:#262626}.hljs ::selection,.hljs::selection{background-color:#333;color:#bcbcbc}.hljs-comment{color:#6c6c6c}.hljs-tag{color:#787878}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#bcbcbc}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#5f8787}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ff8700}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#5f8787}.hljs-strong{font-weight:700;color:#5f8787}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#87af87}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#5f875f}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#ffffaf}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#87afd7}.hljs-emphasis{color:#87afd7;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#5f87af}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
