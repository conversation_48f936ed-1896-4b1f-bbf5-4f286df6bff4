/*!
  Theme: summercamp
  Author: z<PERSON> <PERSON><PERSON> (zoefiri.github.io)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#736e55;background:#1c1810}.hljs ::selection,.hljs::selection{background-color:#3a3527;color:#736e55}.hljs-comment{color:#504b38}.hljs-tag{color:#5f5b45}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#736e55}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#e35142}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#fba11b}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#f2ff27}.hljs-strong{font-weight:700;color:#f2ff27}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#5ceb5a}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#5aebbc}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#489bf0}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ff8080}.hljs-emphasis{color:#ff8080;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#f69be7}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
