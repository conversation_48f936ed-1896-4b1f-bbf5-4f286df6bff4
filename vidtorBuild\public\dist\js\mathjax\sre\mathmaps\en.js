{"en/functions/algebra.js": [{"locale": "en"}, {"category": "Algebra", "mappings": {"default": {"default": "degree"}}, "key": "deg", "names": ["deg"]}, {"category": "Algebra", "mappings": {"default": {"default": "determinant"}, "mathspeak": {"default": "det"}}, "key": "det", "names": ["det"]}, {"category": "Algebra", "mappings": {"default": {"default": "dimension"}}, "key": "dim", "names": ["dim"]}, {"category": "Algebra", "mappings": {"default": {"default": "homomorphism"}, "mathspeak": {"default": "hom"}, "clearspeak": {"default": "hom"}}, "key": "hom", "names": ["hom", "Hom"]}, {"category": "Algebra", "mappings": {"default": {"default": "kernel"}}, "key": "ker", "names": ["ker"]}, {"category": "Algebra", "mappings": {"default": {"default": "trace"}}, "key": "Tr", "names": ["Tr", "tr"]}], "en/functions/elementary.js": [{"locale": "en"}, {"category": "Logarithm", "mappings": {"default": {"default": "log"}}, "key": "log", "names": ["log"]}, {"category": "Logarithm", "mappings": {"default": {"default": "natural log"}, "mathspeak": {"default": "ln"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "natural log"}}, "key": "ln", "names": ["ln"]}, {"category": "Logarithm", "mappings": {"default": {"default": "log base 10"}}, "key": "lg", "names": ["lg"]}, {"category": "Elementary", "mappings": {"default": {"default": "exponential"}, "mathspeak": {"default": "exp"}, "clearspeak": {"default": "exp"}}, "key": "exp", "names": ["exp", "expt"]}, {"category": "Elementary", "mappings": {"default": {"default": "greatest common divisor"}, "mathspeak": {"default": "gcd"}, "clearspeak": {"default": "gcd"}}, "key": "gcd", "names": ["gcd"]}, {"category": "Elementary", "mappings": {"default": {"default": "least common multiple"}, "mathspeak": {"default": "lcm"}, "clearspeak": {"default": "lcm"}}, "key": "lcm", "names": ["lcm"]}, {"category": "Complex", "mappings": {"default": {"default": "argument"}, "mathspeak": {"default": "arg"}, "clearspeak": {"default": "arg"}}, "key": "arg", "names": ["arg"]}, {"category": "Complex", "mappings": {"default": {"default": "imaginary part"}, "mathspeak": {"default": "im"}, "clearspeak": {"default": "imaginary"}}, "key": "im", "names": ["im"]}, {"category": "Complex", "mappings": {"default": {"default": "real part"}, "mathspeak": {"default": "re"}, "clearspeak": {"default": "real"}}, "key": "re", "names": ["re"]}, {"category": "Limits", "mappings": {"default": {"default": "infimum"}, "mathspeak": {"default": "inf"}, "clearspeak": {"default": "inf"}}, "key": "inf", "names": ["inf"]}, {"category": "Limits", "mappings": {"default": {"default": "limit"}, "mathspeak": {"default": "limit"}, "clearspeak": {"default": "lim"}}, "key": "lim", "names": ["lim"]}, {"category": "Limits", "mappings": {"default": {"default": "limit inferior"}, "mathspeak": {"default": "liminf"}, "clearspeak": {"default": "liminf"}}, "key": "liminf", "names": ["lim inf", "liminf"]}, {"category": "Limits", "mappings": {"default": {"default": "limit superior"}, "mathspeak": {"default": "limsup"}, "clearspeak": {"default": "limsup"}}, "key": "limsup", "names": ["lim sup", "limsup"]}, {"category": "Limits", "mappings": {"default": {"default": "maximum"}, "mathspeak": {"default": "max"}, "clearspeak": {"default": "max"}}, "key": "max", "names": ["max"]}, {"category": "Limits", "mappings": {"default": {"default": "minimum"}, "mathspeak": {"default": "min"}, "clearspeak": {"default": "min"}}, "key": "min", "names": ["min"]}, {"category": "Limits", "mappings": {"default": {"default": "supremum"}, "mathspeak": {"default": "sup"}, "clearspeak": {"default": "sup"}}, "key": "sup", "names": ["sup"]}, {"category": "Limits", "mappings": {"default": {"default": "colimit"}}, "key": "<PERSON><PERSON><PERSON>", "names": ["<PERSON><PERSON><PERSON>", "inj lim"]}, {"category": "Limits", "mappings": {"default": {"default": "projective limit"}}, "key": "proj<PERSON>", "names": ["proj<PERSON>", "proj lim"]}, {"category": "Elementary", "mappings": {"default": {"default": "modulo"}, "mathspeak": {"default": "mod"}, "clearspeak": {"default": "mod"}}, "key": "mod", "names": ["mod"]}, {"category": "Probability", "mappings": {"default": {"default": "probability"}}, "key": "Pr", "names": ["Pr"]}], "en/functions/hyperbolic.js": [{"locale": "en"}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic cosine"}}, "key": "cosh", "names": ["cosh"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic cotangent"}}, "key": "coth", "names": ["coth"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic cosecant"}}, "key": "csch", "names": ["csch"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic secant"}}, "key": "sech", "names": ["sech"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic sine"}}, "key": "sinh", "names": ["sinh"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "hyperbolic tangent"}}, "key": "tanh", "names": ["tanh"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic cosine"}}, "key": "arcosh", "names": ["arcosh", "arccosh"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic cotangent"}}, "key": "arcoth", "names": ["arcoth", "arccoth"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic cosecant"}}, "key": "<PERSON><PERSON>", "names": ["<PERSON><PERSON>", "arc<PERSON>ch"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic secant"}}, "key": "arsech", "names": ["arsech", "arcsech"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic sine"}}, "key": "a<PERSON><PERSON><PERSON>", "names": ["a<PERSON><PERSON><PERSON>", "arcsinh"]}, {"category": "Area", "mappings": {"default": {"default": "area hyperbolic tangent"}}, "key": "artanh", "names": ["artanh", "arctanh"]}], "en/functions/trigonometry.js": [{"locale": "en"}, {"category": "Trigonometric", "mappings": {"default": {"default": "cosine"}}, "key": "cos", "names": ["cos", "cosine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "cotangent"}}, "key": "cot", "names": ["cot"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "cosecant"}}, "key": "csc", "names": ["csc"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "secant"}}, "key": "sec", "names": ["sec"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "sine"}}, "key": "sin", "names": ["sin", "sine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "tangent"}}, "key": "tan", "names": ["tan"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc cosine"}, "clearspeak": {"Trig_TrigInverse": "cosine inverse"}}, "key": "arccos", "names": ["arccos"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc cotangent"}, "clearspeak": {"Trig_TrigInverse": "cotangent inverse"}}, "key": "<PERSON><PERSON>", "names": ["<PERSON><PERSON>"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc cosecant"}, "clearspeak": {"Trig_TrigInverse": "cosecant inverse"}}, "key": "arccsc", "names": ["arccsc"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc secant"}, "clearspeak": {"Trig_TrigInverse": "secant inverse"}}, "key": "arcsec", "names": ["arcsec"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc sine"}, "clearspeak": {"Trig_TrigInverse": "sine inverse"}}, "key": "arcsin", "names": ["arcsin"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "arc tangent"}, "clearspeak": {"Trig_TrigInverse": "tangent inverse"}}, "key": "arctan", "names": ["arctan"]}], "en/symbols/digits_rest.js": [{"locale": "en"}, {"category": "No", "mappings": {"default": {"default": "squared"}, "mathspeak": {"default": "squared"}, "clearspeak": {"default": "squared"}}, "key": "00B2"}, {"category": "No", "mappings": {"default": {"default": "cubed"}, "mathspeak": {"default": "cubed"}, "clearspeak": {"default": "cubed"}}, "key": "00B3"}, {"category": "No", "mappings": {"default": {"default": "one quarter"}}, "key": "00BC"}, {"category": "No", "mappings": {"default": {"default": "one half"}}, "key": "00BD"}, {"category": "No", "mappings": {"default": {"default": "three quarters"}}, "key": "00BE"}, {"category": "No", "mappings": {"default": {"default": "one seventh"}}, "key": "2150"}, {"category": "No", "mappings": {"default": {"default": "one ninth"}}, "key": "2151"}, {"category": "No", "mappings": {"default": {"default": "one tenth"}}, "key": "2152"}, {"category": "No", "mappings": {"default": {"default": "one third"}}, "key": "2153"}, {"category": "No", "mappings": {"default": {"default": "two thirds"}}, "key": "2154"}, {"category": "No", "mappings": {"default": {"default": "one fifth"}}, "key": "2155"}, {"category": "No", "mappings": {"default": {"default": "two fifths"}}, "key": "2156"}, {"category": "No", "mappings": {"default": {"default": "three fifths"}}, "key": "2157"}, {"category": "No", "mappings": {"default": {"default": "four fifths"}}, "key": "2158"}, {"category": "No", "mappings": {"default": {"default": "one sixth"}}, "key": "2159"}, {"category": "No", "mappings": {"default": {"default": "five sixths"}}, "key": "215A"}, {"category": "No", "mappings": {"default": {"default": "one eighth"}}, "key": "215B"}, {"category": "No", "mappings": {"default": {"default": "three eighths"}}, "key": "215C"}, {"category": "No", "mappings": {"default": {"default": "five eighths"}}, "key": "215D"}, {"category": "No", "mappings": {"default": {"default": "seven eighths"}}, "key": "215E"}, {"category": "No", "mappings": {"default": {"default": "numerator one"}}, "key": "215F"}, {"category": "No", "mappings": {"default": {"default": "zero thirds"}}, "key": "2189"}, {"category": "No", "mappings": {"default": {"default": "circled ten on black square"}}, "key": "3248"}, {"category": "No", "mappings": {"default": {"default": "circled twenty on black square"}}, "key": "3249"}, {"category": "No", "mappings": {"default": {"default": "circled thirty on black square"}}, "key": "324A"}, {"category": "No", "mappings": {"default": {"default": "circled forty on black square"}}, "key": "324B"}, {"category": "No", "mappings": {"default": {"default": "circled fifty on black square"}}, "key": "324C"}, {"category": "No", "mappings": {"default": {"default": "circled sixty on black square"}}, "key": "324D"}, {"category": "No", "mappings": {"default": {"default": "circled seventy on black square"}}, "key": "324E"}, {"category": "No", "mappings": {"default": {"default": "circled eighty on black square"}}, "key": "324F"}], "en/symbols/greek-rest.js": [{"locale": "en"}, {"category": "<PERSON>", "key": "0394", "mappings": {"clearspeak": {"default": "triangle", "TriangleSymbol_Delta": "cap Delta"}}}], "en/symbols/greek-scripts.js": [{"locale": "en"}, {"category": "Ll", "key": "1D26", "mappings": {"default": {"default": "small cap Gamma"}, "mathspeak": {"default": "small upper Gamma"}}}, {"category": "Ll", "key": "1D27", "mappings": {"default": {"default": "small cap Lamda"}, "mathspeak": {"default": "small upper Lamda"}}}, {"category": "Ll", "key": "1D28", "mappings": {"default": {"default": "small cap Pi"}, "mathspeak": {"default": "small upper Pi"}}}, {"category": "Ll", "key": "1D29", "mappings": {"default": {"default": "small cap Rho"}, "mathspeak": {"default": "small upper Rho"}}}, {"category": "Ll", "key": "1D2A", "mappings": {"default": {"default": "small cap Psi"}, "mathspeak": {"default": "small upper Psi"}}}, {"category": "Lm", "key": "1D5E", "mappings": {"default": {"default": "superscript gamma"}}}, {"category": "Lm", "key": "1D60", "mappings": {"default": {"default": "superscript phi"}}}, {"category": "Lm", "key": "1D66", "mappings": {"default": {"default": "subscript beta"}}}, {"category": "Lm", "key": "1D67", "mappings": {"default": {"default": "subscript gamma"}}}, {"category": "Lm", "key": "1D68", "mappings": {"default": {"default": "subscript rho"}}}, {"category": "Lm", "key": "1D69", "mappings": {"default": {"default": "subscript phi"}}}, {"category": "Lm", "key": "1D6A", "mappings": {"default": {"default": "subscript chi"}}}], "en/symbols/greek-symbols.js": [{"locale": "en"}, {"category": "Ll", "mappings": {"default": {"default": "beta"}}, "key": "03D0"}, {"category": "Ll", "mappings": {"default": {"default": "kai"}}, "key": "03D7"}, {"category": "Sm", "mappings": {"default": {"default": "reversed epsilon"}}, "key": "03F6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "bold cap Digamma"}, "mathspeak": {"default": "bold upper Digamma"}}, "key": "1D7CA"}, {"category": "Ll", "mappings": {"default": {"default": "bold digamma"}}, "key": "1D7CB"}], "en/symbols/hebrew_letters.js": [{"locale": "en"}, {"category": "Lo", "mappings": {"default": {"default": "first transfinite cardinal", "alternative": "alef"}}, "key": "2135"}, {"category": "Lo", "mappings": {"default": {"default": "second transfinite cardinal", "alternative": "bet"}}, "key": "2136"}, {"category": "Lo", "mappings": {"default": {"default": "third transfinite cardinal", "alternative": "gimel"}}, "key": "2137"}, {"category": "Lo", "mappings": {"default": {"default": "fourth transfinite cardinal", "alternative": "dalet"}}, "key": "2138"}], "en/symbols/latin-lower-double-accent.js": [{"locale": "en"}, {"category": "Ll", "key": "01D6", "mappings": {"default": {"default": "u double overdot overbar"}}}, {"category": "Ll", "key": "01D8", "mappings": {"default": {"default": "u double overdot acute"}}}, {"category": "Ll", "key": "01DA", "mappings": {"default": {"default": "u double overdot caron"}}}, {"category": "Ll", "key": "01DC", "mappings": {"default": {"default": "u double overdot grave"}}}, {"category": "Ll", "key": "01DF", "mappings": {"default": {"default": "a double overdot overbar"}}}, {"category": "Ll", "key": "01E1", "mappings": {"default": {"default": "a overdot overbar"}}}, {"category": "Ll", "key": "01ED", "mappings": {"default": {"default": "o ogonek overbar"}}}, {"category": "Ll", "key": "01FB", "mappings": {"default": {"default": "a ring above acute"}}}, {"category": "Ll", "key": "022B", "mappings": {"default": {"default": "o double overdot overbar"}}}, {"category": "Ll", "key": "022D", "mappings": {"default": {"default": "o tilde overbar"}}}, {"category": "Ll", "key": "0231", "mappings": {"default": {"default": "o overdot overbar"}}}, {"category": "Ll", "key": "1E09", "mappings": {"default": {"default": "c cedilla acute"}}}, {"category": "Ll", "key": "1E15", "mappings": {"default": {"default": "e overbar grave"}}}, {"category": "Ll", "key": "1E17", "mappings": {"default": {"default": "e overbar acute"}}}, {"category": "Ll", "key": "1E1D", "mappings": {"default": {"default": "e cedilla breve"}}}, {"category": "Ll", "key": "1E2F", "mappings": {"default": {"default": "i double overdot acute"}}}, {"category": "Ll", "key": "1E39", "mappings": {"default": {"default": "l underdot overbar"}}}, {"category": "Ll", "key": "1E4D", "mappings": {"default": {"default": "o tilde acute"}}}, {"category": "Ll", "key": "1E4F", "mappings": {"default": {"default": "o tilde double overdot"}}}, {"category": "Ll", "key": "1E51", "mappings": {"default": {"default": "o overbar grave"}}}, {"category": "Ll", "key": "1E53", "mappings": {"default": {"default": "o overbar acute"}}}, {"category": "Ll", "key": "1E5D", "mappings": {"default": {"default": "r underdot overbar"}}}, {"category": "Ll", "key": "1E65", "mappings": {"default": {"default": "s acute overdot"}}}, {"category": "Ll", "key": "1E67", "mappings": {"default": {"default": "s caron overdot"}}}, {"category": "Ll", "key": "1E69", "mappings": {"default": {"default": "s underdot overdot"}}}, {"category": "Ll", "key": "1E79", "mappings": {"default": {"default": "u tilde acute"}}}, {"category": "Ll", "key": "1E7B", "mappings": {"default": {"default": "u overbar double overdot"}}}, {"category": "Ll", "key": "1EA5", "mappings": {"default": {"default": "a hat acute"}}}, {"category": "Ll", "key": "1EA7", "mappings": {"default": {"default": "a hat grave"}}}, {"category": "Ll", "key": "1EA9", "mappings": {"default": {"default": "a hat hook above"}}}, {"category": "Ll", "key": "1EAB", "mappings": {"default": {"default": "a hat tilde"}}}, {"category": "Ll", "key": "1EAD", "mappings": {"default": {"default": "a hat underdot"}}}, {"category": "Ll", "key": "1EAF", "mappings": {"default": {"default": "a breve acute"}}}, {"category": "Ll", "key": "1EB1", "mappings": {"default": {"default": "a breve grave"}}}, {"category": "Ll", "key": "1EB3", "mappings": {"default": {"default": "a breve hook above"}}}, {"category": "Ll", "key": "1EB5", "mappings": {"default": {"default": "a breve tilde"}}}, {"category": "Ll", "key": "1EB7", "mappings": {"default": {"default": "a breve underdot"}}}, {"category": "Ll", "key": "1EBF", "mappings": {"default": {"default": "e hat acute"}}}, {"category": "Ll", "key": "1EC1", "mappings": {"default": {"default": "e hat grave"}}}, {"category": "Ll", "key": "1EC3", "mappings": {"default": {"default": "e hat hook above"}}}, {"category": "Ll", "key": "1EC5", "mappings": {"default": {"default": "e hat tilde"}}}, {"category": "Ll", "key": "1EC7", "mappings": {"default": {"default": "e hat underdot"}}}, {"category": "Ll", "key": "1ED1", "mappings": {"default": {"default": "o hat acute"}}}, {"category": "Ll", "key": "1ED3", "mappings": {"default": {"default": "o hat grave"}}}, {"category": "Ll", "key": "1ED5", "mappings": {"default": {"default": "o hat hook above"}}}, {"category": "Ll", "key": "1ED7", "mappings": {"default": {"default": "o hat tilde"}}}, {"category": "Ll", "key": "1ED9", "mappings": {"default": {"default": "o hat underdot"}}}, {"category": "Ll", "key": "1EDB", "mappings": {"default": {"default": "o acute prime"}}}, {"category": "Ll", "key": "1EDD", "mappings": {"default": {"default": "o grave prime"}}}, {"category": "Ll", "key": "1EDF", "mappings": {"default": {"default": "o hook above prime"}}}, {"category": "Ll", "key": "1EE1", "mappings": {"default": {"default": "o tilde prime"}}}, {"category": "Ll", "key": "1EE3", "mappings": {"default": {"default": "o underdot prime"}}}, {"category": "Ll", "key": "1EE9", "mappings": {"default": {"default": "u acute prime"}}}, {"category": "Ll", "key": "1EEB", "mappings": {"default": {"default": "u grave prime"}}}, {"category": "Ll", "key": "1EED", "mappings": {"default": {"default": "u hook above prime"}}}, {"category": "Ll", "key": "1EEF", "mappings": {"default": {"default": "u tilde prime"}}}, {"category": "Ll", "key": "1EF1", "mappings": {"default": {"default": "u underdot prime"}}}], "en/symbols/latin-lower-phonetic.js": [{"locale": "en"}, {"category": "Ll", "key": "00F8", "mappings": {"default": {"default": "o with stroke"}}}, {"category": "Ll", "key": "0111", "mappings": {"default": {"default": "d with stroke"}}}, {"category": "Ll", "key": "0127", "mappings": {"default": {"default": "h with stroke"}}}, {"category": "Ll", "key": "0142", "mappings": {"default": {"default": "l with stroke"}}}, {"category": "Ll", "key": "0167", "mappings": {"default": {"default": "t with stroke"}}}, {"category": "Ll", "key": "0180", "mappings": {"default": {"default": "b with stroke"}}}, {"category": "Ll", "key": "019B", "mappings": {"default": {"default": "lambda with stroke"}}}, {"category": "Ll", "key": "01B6", "mappings": {"default": {"default": "z with stroke"}}}, {"category": "Ll", "key": "01BE", "mappings": {"default": {"default": "latin letter inverted glottal stop with stroke"}}}, {"category": "Ll", "key": "01E5", "mappings": {"default": {"default": "g with stroke"}}}, {"category": "Ll", "key": "01FF", "mappings": {"default": {"default": "o with stroke and acute"}}}, {"category": "Ll", "key": "023C", "mappings": {"default": {"default": "c with stroke"}}}, {"category": "Ll", "key": "0247", "mappings": {"default": {"default": "e with stroke"}}}, {"category": "Ll", "key": "0249", "mappings": {"default": {"default": "j with stroke"}}}, {"category": "Ll", "key": "024D", "mappings": {"default": {"default": "r with stroke"}}}, {"category": "Ll", "key": "024F", "mappings": {"default": {"default": "y with stroke"}}}, {"category": "Ll", "key": "025F", "mappings": {"default": {"default": "dotless j with stroke"}}}, {"category": "Ll", "key": "0268", "mappings": {"default": {"default": "i with stroke"}}}, {"category": "Ll", "key": "0284", "mappings": {"default": {"default": "dotless j with stroke and hook"}}}, {"category": "Ll", "key": "02A1", "mappings": {"default": {"default": "latin letter glottal stop with stroke"}}}, {"category": "Ll", "key": "02A2", "mappings": {"default": {"default": "latin letter reversed glottal stop with stroke"}}}, {"category": "Ll", "key": "1D13", "mappings": {"default": {"default": "sideways o with stroke"}}}, {"category": "Ll", "key": "1D7C", "mappings": {"default": {"default": "iota with stroke"}}}, {"category": "Ll", "key": "1D7D", "mappings": {"default": {"default": "p with stroke"}}}, {"category": "Ll", "key": "1D7F", "mappings": {"default": {"default": "upsilon with stroke"}}}, {"category": "Ll", "key": "1E9C", "mappings": {"default": {"default": "long s with diagonal stroke"}}}, {"category": "Ll", "key": "1E9D", "mappings": {"default": {"default": "long s with high stroke"}}}, {"category": "Ll", "key": "018D", "mappings": {"default": {"default": "turned delta"}}}, {"category": "Ll", "key": "1E9B", "mappings": {"default": {"default": "long s with dot above"}}}, {"category": "Ll", "key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"category": "Ll", "key": "0138", "mappings": {"default": {"default": "kra"}}}, {"category": "Ll", "key": "017F", "mappings": {"default": {"default": "long s"}}}, {"category": "Ll", "key": "0183", "mappings": {"default": {"default": "b with topbar"}}}, {"category": "Ll", "key": "0185", "mappings": {"default": {"default": "tone six"}}}, {"category": "Ll", "key": "0188", "mappings": {"default": {"default": "c with hook"}}}, {"category": "Ll", "key": "018C", "mappings": {"default": {"default": "d with topbar"}}}, {"category": "Ll", "key": "0192", "mappings": {"default": {"default": "f with hook"}}}, {"category": "Ll", "key": "0195", "mappings": {"default": {"default": "hv"}}}, {"category": "Ll", "key": "0199", "mappings": {"default": {"default": "k with hook"}}}, {"category": "Ll", "key": "019A", "mappings": {"default": {"default": "l with bar"}}}, {"category": "Ll", "key": "019E", "mappings": {"default": {"default": "n with long right leg"}}}, {"category": "Ll", "key": "01A1", "mappings": {"default": {"default": "o with horn"}}}, {"category": "Ll", "key": "01A3", "mappings": {"default": {"default": "oi"}}}, {"category": "Ll", "key": "01A5", "mappings": {"default": {"default": "p with hook"}}}, {"category": "Ll", "key": "01A8", "mappings": {"default": {"default": "tone two"}}}, {"category": "Ll", "key": "01AA", "mappings": {"default": {"default": "latin letter reversed esh loop"}}}, {"category": "Ll", "key": "01AB", "mappings": {"default": {"default": "t with palatal hook"}}}, {"category": "Ll", "key": "01AD", "mappings": {"default": {"default": "t with hook"}}}, {"category": "Ll", "key": "01B0", "mappings": {"default": {"default": "u with horn"}}}, {"category": "Ll", "key": "01B4", "mappings": {"default": {"default": "y with hook"}}}, {"category": "Ll", "key": "01B9", "mappings": {"default": {"default": "ezh reversed"}}}, {"category": "Ll", "key": "01BA", "mappings": {"default": {"default": "ezh with tail"}}}, {"category": "Ll", "key": "01BD", "mappings": {"default": {"default": "tone five"}}}, {"category": "Ll", "key": "01BF", "mappings": {"default": {"default": "latin letter wynn"}}}, {"category": "Ll", "key": "01C6", "mappings": {"default": {"default": "dz with caron"}}}, {"category": "Ll", "key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"category": "Ll", "key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"category": "Ll", "key": "01E3", "mappings": {"default": {"default": "ae with macron"}}}, {"category": "Ll", "key": "01EF", "mappings": {"default": {"default": "ezh with caron"}}}, {"category": "Ll", "key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"category": "Ll", "key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"category": "Ll", "key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"category": "Ll", "key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"category": "Ll", "key": "0293", "mappings": {"default": {"default": "ezh with curl"}}}, {"category": "Ll", "key": "02A4", "mappings": {"default": {"default": "dezh digraph"}}}, {"category": "Ll", "key": "01DD", "mappings": {"default": {"default": "turned e"}}}, {"category": "Ll", "key": "01FD", "mappings": {"default": {"default": "ae with acute"}}}, {"category": "Ll", "key": "0221", "mappings": {"default": {"default": "d with curl"}}}, {"category": "Ll", "key": "0223", "mappings": {"default": {"default": "ou"}}}, {"category": "Ll", "key": "0225", "mappings": {"default": {"default": "z with hook"}}}, {"category": "Ll", "key": "0234", "mappings": {"default": {"default": "l with curl"}}}, {"category": "Ll", "key": "0235", "mappings": {"default": {"default": "n with curl"}}}, {"category": "Ll", "key": "0236", "mappings": {"default": {"default": "t with curl"}}}, {"category": "Ll", "key": "0238", "mappings": {"default": {"default": "db digraph"}}}, {"category": "Ll", "key": "0239", "mappings": {"default": {"default": "qp digraph"}}}, {"category": "Ll", "key": "023F", "mappings": {"default": {"default": "s with swash tail"}}}, {"category": "Ll", "key": "0240", "mappings": {"default": {"default": "z with swash tail"}}}, {"category": "Ll", "key": "0242", "mappings": {"default": {"default": "glottal stop"}}}, {"category": "Ll", "key": "024B", "mappings": {"default": {"default": "q with hook tail"}}}, {"category": "Ll", "key": "0250", "mappings": {"default": {"default": "turned a"}}}, {"category": "Ll", "key": "0251", "mappings": {"default": {"default": "alpha"}}}, {"category": "Ll", "key": "0252", "mappings": {"default": {"default": "turned alpha"}}}, {"category": "Ll", "key": "0253", "mappings": {"default": {"default": "b with hook"}}}, {"category": "Ll", "key": "0254", "mappings": {"default": {"default": "open o"}}}, {"category": "Ll", "key": "0255", "mappings": {"default": {"default": "c with curl"}}}, {"category": "Ll", "key": "0256", "mappings": {"default": {"default": "d with tail"}}}, {"category": "Ll", "key": "0257", "mappings": {"default": {"default": "d with hook"}}}, {"category": "Ll", "key": "0258", "mappings": {"default": {"default": "reversed e"}}}, {"category": "Ll", "key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"category": "Ll", "key": "025A", "mappings": {"default": {"default": "schwa with hook"}}}, {"category": "Ll", "key": "025B", "mappings": {"default": {"default": "open e"}}}, {"category": "Ll", "key": "025C", "mappings": {"default": {"default": "reversed open e"}}}, {"category": "Ll", "key": "025D", "mappings": {"default": {"default": "reversed open e with hook"}}}, {"category": "Ll", "key": "025E", "mappings": {"default": {"default": "closed reversed open e"}}}, {"category": "Ll", "key": "0260", "mappings": {"default": {"default": "g with hook"}}}, {"category": "Ll", "key": "0261", "mappings": {"default": {"default": "script g"}}}, {"category": "Ll", "key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"category": "Ll", "key": "0264", "mappings": {"default": {"default": "rams horn"}}}, {"category": "Ll", "key": "0265", "mappings": {"default": {"default": "turned h"}}}, {"category": "Ll", "key": "0266", "mappings": {"default": {"default": "h with hook"}}}, {"category": "Ll", "key": "0267", "mappings": {"default": {"default": "heng with hook"}}}, {"category": "Ll", "key": "0269", "mappings": {"default": {"default": "iota"}}}, {"category": "Ll", "key": "026B", "mappings": {"default": {"default": "l with middle tilde"}}}, {"category": "Ll", "key": "026C", "mappings": {"default": {"default": "l with belt"}}}, {"category": "Ll", "key": "026D", "mappings": {"default": {"default": "l with retroflex hook"}}}, {"category": "Ll", "key": "026F", "mappings": {"default": {"default": "turned m"}}}, {"category": "Ll", "key": "0270", "mappings": {"default": {"default": "turned m with long leg"}}}, {"category": "Ll", "key": "0271", "mappings": {"default": {"default": "m with hook"}}}, {"category": "Ll", "key": "0272", "mappings": {"default": {"default": "n with left hook"}}}, {"category": "Ll", "key": "0273", "mappings": {"default": {"default": "n with retroflex hook"}}}, {"category": "Ll", "key": "0275", "mappings": {"default": {"default": "barred o"}}}, {"category": "Ll", "key": "0277", "mappings": {"default": {"default": "closed omega"}}}, {"category": "Ll", "key": "0278", "mappings": {"default": {"default": "phi"}}}, {"category": "Ll", "key": "0279", "mappings": {"default": {"default": "turned r"}}}, {"category": "Ll", "key": "027A", "mappings": {"default": {"default": "turned r with long leg"}}}, {"category": "Ll", "key": "027B", "mappings": {"default": {"default": "turned r with hook"}}}, {"category": "Ll", "key": "027C", "mappings": {"default": {"default": "r with long leg"}}}, {"category": "Ll", "key": "027D", "mappings": {"default": {"default": "r with tail"}}}, {"category": "Ll", "key": "027E", "mappings": {"default": {"default": "r with fishhook"}}}, {"category": "Ll", "key": "027F", "mappings": {"default": {"default": "reversed r with fishhook"}}}, {"category": "Ll", "key": "0282", "mappings": {"default": {"default": "s with hook"}}}, {"category": "Ll", "key": "0283", "mappings": {"default": {"default": "esh"}}}, {"category": "Ll", "key": "0285", "mappings": {"default": {"default": "squat reversed esh"}}}, {"category": "Ll", "key": "0286", "mappings": {"default": {"default": "esh with curl"}}}, {"category": "Ll", "key": "0287", "mappings": {"default": {"default": "turned t"}}}, {"category": "Ll", "key": "0288", "mappings": {"default": {"default": "t with retroflex hook"}}}, {"category": "Ll", "key": "0289", "mappings": {"default": {"default": "u bar"}}}, {"category": "Ll", "key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"category": "Ll", "key": "028B", "mappings": {"default": {"default": "v with hook"}}}, {"category": "Ll", "key": "028C", "mappings": {"default": {"default": "turned v"}}}, {"category": "Ll", "key": "028D", "mappings": {"default": {"default": "turned w"}}}, {"category": "Ll", "key": "028E", "mappings": {"default": {"default": "turned y"}}}, {"category": "Ll", "key": "0290", "mappings": {"default": {"default": "z with retroflex hook"}}}, {"category": "Ll", "key": "0291", "mappings": {"default": {"default": "z with curl"}}}, {"category": "Ll", "key": "0295", "mappings": {"default": {"default": "latin letter pharyngeal voiced fricative"}}}, {"category": "Ll", "key": "0296", "mappings": {"default": {"default": "latin letter inverted glottal stop"}}}, {"category": "Ll", "key": "0297", "mappings": {"default": {"default": "latin letter stretched c"}}}, {"category": "Ll", "key": "0298", "mappings": {"default": {"default": "latin letter bilabial click"}}}, {"category": "Ll", "key": "029A", "mappings": {"default": {"default": "closed open e"}}}, {"category": "Ll", "key": "029E", "mappings": {"default": {"default": "turned k"}}}, {"category": "Ll", "key": "02A0", "mappings": {"default": {"default": "q with hook"}}}, {"category": "Ll", "key": "02A3", "mappings": {"default": {"default": "dz digraph"}}}, {"category": "Ll", "key": "02A5", "mappings": {"default": {"default": "dz digraph with curl"}}}, {"category": "Ll", "key": "02A6", "mappings": {"default": {"default": "ts digraph"}}}, {"category": "Ll", "key": "02A7", "mappings": {"default": {"default": "tesh digraph"}}}, {"category": "Ll", "key": "02A8", "mappings": {"default": {"default": "tc digraph with curl"}}}, {"category": "Ll", "key": "02A9", "mappings": {"default": {"default": "feng digraph"}}}, {"category": "Ll", "key": "02AA", "mappings": {"default": {"default": "ls digraph"}}}, {"category": "Ll", "key": "02AB", "mappings": {"default": {"default": "lz digraph"}}}, {"category": "Ll", "key": "02AC", "mappings": {"default": {"default": "latin letter bilabial percussive"}}}, {"category": "Ll", "key": "02AD", "mappings": {"default": {"default": "latin letter bidental percussive"}}}, {"category": "Ll", "key": "02AE", "mappings": {"default": {"default": "turned h with fishhook"}}}, {"category": "Ll", "key": "02AF", "mappings": {"default": {"default": "turned h with fishhook and tail"}}}, {"category": "Ll", "key": "1D02", "mappings": {"default": {"default": "turned ae"}}}, {"category": "Ll", "key": "1D08", "mappings": {"default": {"default": "turned open e"}}}, {"category": "Ll", "key": "1D09", "mappings": {"default": {"default": "turned i"}}}, {"category": "Ll", "key": "1D11", "mappings": {"default": {"default": "sideways o"}}}, {"category": "Ll", "key": "1D12", "mappings": {"default": {"default": "sideways open o"}}}, {"category": "Ll", "key": "1D14", "mappings": {"default": {"default": "turned oe"}}}, {"category": "Ll", "key": "1D16", "mappings": {"default": {"default": "top half o"}}}, {"category": "Ll", "key": "1D17", "mappings": {"default": {"default": "bottom half o"}}}, {"category": "Ll", "key": "1D1D", "mappings": {"default": {"default": "sideways u"}}}, {"category": "Ll", "key": "1D1E", "mappings": {"default": {"default": "sideways diaeresized u"}}}, {"category": "Ll", "key": "1D1F", "mappings": {"default": {"default": "sideways turned m"}}}, {"category": "Ll", "key": "1D24", "mappings": {"default": {"default": "latin letter voiced laryngeal spirant"}}}, {"category": "Ll", "key": "1D25", "mappings": {"default": {"default": "latin letter ain"}}}, {"category": "Ll", "key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"category": "Ll", "key": "1D6C", "mappings": {"default": {"default": "b with middle tilde"}}}, {"category": "Ll", "key": "1D6D", "mappings": {"default": {"default": "d with middle tilde"}}}, {"category": "Ll", "key": "1D6E", "mappings": {"default": {"default": "f with middle tilde"}}}, {"category": "Ll", "key": "1D6F", "mappings": {"default": {"default": "m with middle tilde"}}}, {"category": "Ll", "key": "1D70", "mappings": {"default": {"default": "n with middle tilde"}}}, {"category": "Ll", "key": "1D71", "mappings": {"default": {"default": "p with middle tilde"}}}, {"category": "Ll", "key": "1D72", "mappings": {"default": {"default": "r with middle tilde"}}}, {"category": "Ll", "key": "1D73", "mappings": {"default": {"default": "r with fishhook and middle tilde"}}}, {"category": "Ll", "key": "1D74", "mappings": {"default": {"default": "s with middle tilde"}}}, {"category": "Ll", "key": "1D75", "mappings": {"default": {"default": "t with middle tilde"}}}, {"category": "Ll", "key": "1D76", "mappings": {"default": {"default": "z with middle tilde"}}}, {"category": "Ll", "key": "1D77", "mappings": {"default": {"default": "turned g"}}}, {"category": "Ll", "key": "1D79", "mappings": {"default": {"default": "insular g"}}}, {"category": "Ll", "key": "1D7A", "mappings": {"default": {"default": "th with strikethrough"}}}, {"category": "Ll", "key": "1D80", "mappings": {"default": {"default": "b with palatal hook"}}}, {"category": "Ll", "key": "1D81", "mappings": {"default": {"default": "d with palatal hook"}}}, {"category": "Ll", "key": "1D82", "mappings": {"default": {"default": "f with palatal hook"}}}, {"category": "Ll", "key": "1D83", "mappings": {"default": {"default": "g with palatal hook"}}}, {"category": "Ll", "key": "1D84", "mappings": {"default": {"default": "k with palatal hook"}}}, {"category": "Ll", "key": "1D85", "mappings": {"default": {"default": "l with palatal hook"}}}, {"category": "Ll", "key": "1D86", "mappings": {"default": {"default": "m with palatal hook"}}}, {"category": "Ll", "key": "1D87", "mappings": {"default": {"default": "n with palatal hook"}}}, {"category": "Ll", "key": "1D88", "mappings": {"default": {"default": "p with palatal hook"}}}, {"category": "Ll", "key": "1D89", "mappings": {"default": {"default": "r with palatal hook"}}}, {"category": "Ll", "key": "1D8A", "mappings": {"default": {"default": "s with palatal hook"}}}, {"category": "Ll", "key": "1D8B", "mappings": {"default": {"default": "esh with palatal hook"}}}, {"category": "Ll", "key": "1D8C", "mappings": {"default": {"default": "v with palatal hook"}}}, {"category": "Ll", "key": "1D8D", "mappings": {"default": {"default": "x with palatal hook"}}}, {"category": "Ll", "key": "1D8E", "mappings": {"default": {"default": "z with palatal hook"}}}, {"category": "Ll", "key": "1D8F", "mappings": {"default": {"default": "a with retroflex hook"}}}, {"category": "Ll", "key": "1D90", "mappings": {"default": {"default": "alpha with retroflex hook"}}}, {"category": "Ll", "key": "1D91", "mappings": {"default": {"default": "d with hook and tail"}}}, {"category": "Ll", "key": "1D92", "mappings": {"default": {"default": "e with retroflex hook"}}}, {"category": "Ll", "key": "1D93", "mappings": {"default": {"default": "open e with retroflex hook"}}}, {"category": "Ll", "key": "1D94", "mappings": {"default": {"default": "reversed open e with retroflex hook"}}}, {"category": "Ll", "key": "1D95", "mappings": {"default": {"default": "schwa with retroflex hook"}}}, {"category": "Ll", "key": "1D96", "mappings": {"default": {"default": "i with retroflex hook"}}}, {"category": "Ll", "key": "1D97", "mappings": {"default": {"default": "open o with retroflex hook"}}}, {"category": "Ll", "key": "1D98", "mappings": {"default": {"default": "esh with retroflex hook"}}}, {"category": "Ll", "key": "1D99", "mappings": {"default": {"default": "u with retroflex hook"}}}, {"category": "Ll", "key": "1D9A", "mappings": {"default": {"default": "ezh with retroflex hook"}}}, {"category": "Ll", "key": "0149", "mappings": {"default": {"default": "n preceded by apostrophe"}}}, {"category": "Ll", "key": "014B", "mappings": {"default": {"default": "eng"}}}], "en/symbols/latin-lower-single-accent.js": [{"locale": "en"}, {"category": "Ll", "key": "00E0", "mappings": {"default": {"default": "a grave"}, "mathspeak": {"default": "modifying above a with grave", "brief": "mod above a with grave", "sbrief": "mod above a with grave"}}}, {"category": "Ll", "key": "00E1", "mappings": {"default": {"default": "a acute"}, "mathspeak": {"default": "modifying above a with acute", "brief": "mod above a with acute", "sbrief": "mod above a with acute"}}}, {"category": "Ll", "key": "00E2", "mappings": {"default": {"default": "a hat"}, "mathspeak": {"default": "modifying above a with caret", "brief": "mod above a with caret", "sbrief": "mod above a with caret"}}}, {"category": "Ll", "key": "00E3", "mappings": {"default": {"default": "a tilde"}, "mathspeak": {"default": "a overtilde", "brief": "a overtilde", "sbrief": "a overtilde"}}}, {"category": "Ll", "key": "00E4", "mappings": {"default": {"default": "a double overdot"}, "mathspeak": {"default": "modifying above a with double dot", "brief": "mod above a with double dot", "sbrief": "mod above a with double dot"}}}, {"category": "Ll", "key": "00E5", "mappings": {"default": {"default": "a ring"}, "mathspeak": {"default": "modifying above a with ring", "brief": "mod above a with ring", "sbrief": "mod above a with ring"}}}, {"category": "Ll", "key": "00E7", "mappings": {"default": {"default": "c cedilla"}, "mathspeak": {"default": "modifying above c with cedilla", "brief": "mod above c with cedilla", "sbrief": "mod above c with cedilla"}}}, {"category": "Ll", "key": "00E8", "mappings": {"default": {"default": "e grave"}, "mathspeak": {"default": "modifying above e with grave", "brief": "mod above e with grave", "sbrief": "mod above e with grave"}}}, {"category": "Ll", "key": "00E9", "mappings": {"default": {"default": "e acute"}, "mathspeak": {"default": "modifying above e with acute", "brief": "mod above e with acute", "sbrief": "mod above e with acute"}}}, {"category": "Ll", "key": "00EA", "mappings": {"default": {"default": "e hat"}, "mathspeak": {"default": "modifying above e with caret", "brief": "mod above e with caret", "sbrief": "mod above e with caret"}}}, {"category": "Ll", "key": "00EB", "mappings": {"default": {"default": "e double overdot"}, "mathspeak": {"default": "modifying above e with double dot", "brief": "mod above e with double dot", "sbrief": "mod above e with double dot"}}}, {"category": "Ll", "key": "00EC", "mappings": {"default": {"default": "i grave"}, "mathspeak": {"default": "modifying above i with grave", "brief": "mod above i with grave", "sbrief": "mod above i with grave"}}}, {"category": "Ll", "key": "00ED", "mappings": {"default": {"default": "i acute"}, "mathspeak": {"default": "modifying above i with acute", "brief": "mod above i with acute", "sbrief": "mod above i with acute"}}}, {"category": "Ll", "key": "00EE", "mappings": {"default": {"default": "i hat"}, "mathspeak": {"default": "modifying above i with caret", "brief": "mod above i with caret", "sbrief": "mod above i with caret"}}}, {"category": "Ll", "key": "00EF", "mappings": {"default": {"default": "i double overdot"}, "mathspeak": {"default": "modifying above i with double dot", "brief": "mod above i with double dot", "sbrief": "mod above i with double dot"}}}, {"category": "Ll", "key": "00F1", "mappings": {"default": {"default": "n tilde"}, "mathspeak": {"default": "n overtilde", "brief": "n overtilde", "sbrief": "n overtilde"}}}, {"category": "Ll", "key": "00F2", "mappings": {"default": {"default": "o grave"}, "mathspeak": {"default": "modifying above o with grave", "brief": "mod above o with grave", "sbrief": "mod above o with grave"}}}, {"category": "Ll", "key": "00F3", "mappings": {"default": {"default": "o acute"}, "mathspeak": {"default": "modifying above o with acute", "brief": "mod above o with acute", "sbrief": "mod above o with acute"}}}, {"category": "Ll", "key": "00F4", "mappings": {"default": {"default": "o hat"}, "mathspeak": {"default": "modifying above o with caret", "brief": "mod above o with caret", "sbrief": "mod above o with caret"}}}, {"category": "Ll", "key": "00F5", "mappings": {"default": {"default": "o tilde"}, "mathspeak": {"default": "o overtilde", "brief": "o overtilde", "sbrief": "o overtilde"}}}, {"category": "Ll", "key": "00F6", "mappings": {"default": {"default": "o double overdot"}, "mathspeak": {"default": "modifying above o with double dot", "brief": "mod above o with double dot", "sbrief": "mod above o with double dot"}}}, {"category": "Ll", "key": "00F9", "mappings": {"default": {"default": "u grave"}, "mathspeak": {"default": "modifying above u with grave", "brief": "mod above u with grave", "sbrief": "mod above u with grave"}}}, {"category": "Ll", "key": "00FA", "mappings": {"default": {"default": "u acute"}, "mathspeak": {"default": "modifying above u with acute", "brief": "mod above u with acute", "sbrief": "mod above u with acute"}}}, {"category": "Ll", "key": "00FB", "mappings": {"default": {"default": "u hat"}, "mathspeak": {"default": "modifying above u with caret", "brief": "mod above u with caret", "sbrief": "mod above u with caret"}}}, {"category": "Ll", "key": "00FC", "mappings": {"default": {"default": "u double overdot"}, "mathspeak": {"default": "modifying above u with double dot", "brief": "mod above u with double dot", "sbrief": "mod above u with double dot"}}}, {"category": "Ll", "key": "00FD", "mappings": {"default": {"default": "y acute"}, "mathspeak": {"default": "modifying above y with acute", "brief": "mod above y with acute", "sbrief": "mod above y with acute"}}}, {"category": "Ll", "key": "00FF", "mappings": {"default": {"default": "y double overdot"}, "mathspeak": {"default": "modifying above y with double dot", "brief": "mod above y with double dot", "sbrief": "mod above y with double dot"}}}, {"category": "Ll", "key": "0101", "mappings": {"default": {"default": "a overbar"}, "mathspeak": {"default": "a overbar", "brief": "a overbar", "sbrief": "a overbar"}}}, {"category": "Ll", "key": "0103", "mappings": {"default": {"default": "a breve"}, "mathspeak": {"default": "modifying above a with breve", "brief": "mod above a with breve", "sbrief": "mod above a with breve"}}}, {"category": "Ll", "key": "0105", "mappings": {"default": {"default": "a ogonek"}, "mathspeak": {"default": "modifying above a with ogonek", "brief": "mod above a with ogonek", "sbrief": "mod above a with ogonek"}}}, {"category": "Ll", "key": "0107", "mappings": {"default": {"default": "c acute"}, "mathspeak": {"default": "modifying above c with acute", "brief": "mod above c with acute", "sbrief": "mod above c with acute"}}}, {"category": "Ll", "key": "0109", "mappings": {"default": {"default": "c hat"}, "mathspeak": {"default": "modifying above c with caret", "brief": "mod above c with caret", "sbrief": "mod above c with caret"}}}, {"category": "Ll", "key": "010B", "mappings": {"default": {"default": "c overdot"}, "mathspeak": {"default": "modifying above c with dot", "brief": "mod above c with dot", "sbrief": "mod above c with dot"}}}, {"category": "Ll", "key": "010D", "mappings": {"default": {"default": "c caron"}, "mathspeak": {"default": "modifying above c with caron", "brief": "mod above c with caron", "sbrief": "mod above c with caron"}}}, {"category": "Ll", "key": "010F", "mappings": {"default": {"default": "d caron"}, "mathspeak": {"default": "modifying above d with caron", "brief": "mod above d with caron", "sbrief": "mod above d with caron"}}}, {"category": "Ll", "key": "0113", "mappings": {"default": {"default": "e overbar"}, "mathspeak": {"default": "e overbar", "brief": "e overbar", "sbrief": "e overbar"}}}, {"category": "Ll", "key": "0115", "mappings": {"default": {"default": "e breve"}, "mathspeak": {"default": "modifying above e with breve", "brief": "mod above e with breve", "sbrief": "mod above e with breve"}}}, {"category": "Ll", "key": "0117", "mappings": {"default": {"default": "e overdot"}, "mathspeak": {"default": "modifying above e with dot", "brief": "mod above e with dot", "sbrief": "mod above e with dot"}}}, {"category": "Ll", "key": "0119", "mappings": {"default": {"default": "e ogonek"}, "mathspeak": {"default": "modifying above e with ogonek", "brief": "mod above e with ogonek", "sbrief": "mod above e with ogonek"}}}, {"category": "Ll", "key": "011B", "mappings": {"default": {"default": "e caron"}, "mathspeak": {"default": "modifying above e with caron", "brief": "mod above e with caron", "sbrief": "mod above e with caron"}}}, {"category": "Ll", "key": "011D", "mappings": {"default": {"default": "g hat"}, "mathspeak": {"default": "modifying above g with caret", "brief": "mod above g with caret", "sbrief": "mod above g with caret"}}}, {"category": "Ll", "key": "011F", "mappings": {"default": {"default": "g breve"}, "mathspeak": {"default": "modifying above g with breve", "brief": "mod above g with breve", "sbrief": "mod above g with breve"}}}, {"category": "Ll", "key": "0121", "mappings": {"default": {"default": "g overdot"}, "mathspeak": {"default": "modifying above g with dot", "brief": "mod above g with dot", "sbrief": "mod above g with dot"}}}, {"category": "Ll", "key": "0123", "mappings": {"default": {"default": "g cedilla"}, "mathspeak": {"default": "modifying above g with cedilla", "brief": "mod above g with cedilla", "sbrief": "mod above g with cedilla"}}}, {"category": "Ll", "key": "0125", "mappings": {"default": {"default": "h hat"}, "mathspeak": {"default": "modifying above h with caret", "brief": "mod above h with caret", "sbrief": "mod above h with caret"}}}, {"category": "Ll", "key": "0129", "mappings": {"default": {"default": "i tilde"}, "mathspeak": {"default": "i overtilde", "brief": "i overtilde", "sbrief": "i overtilde"}}}, {"category": "Ll", "key": "012B", "mappings": {"default": {"default": "i overbar"}, "mathspeak": {"default": "i overbar", "brief": "i overbar", "sbrief": "i overbar"}}}, {"category": "Ll", "key": "012D", "mappings": {"default": {"default": "i breve"}, "mathspeak": {"default": "modifying above i with breve", "brief": "mod above i with breve", "sbrief": "mod above i with breve"}}}, {"category": "Ll", "key": "012F", "mappings": {"default": {"default": "i ogonek"}, "mathspeak": {"default": "modifying above i with ogonek", "brief": "mod above i with ogonek", "sbrief": "mod above i with ogonek"}}}, {"category": "Ll", "key": "0131", "mappings": {"default": {"default": "dotless i"}, "mathspeak": {"default": "modifying above dotless i", "brief": "mod above dotless i", "sbrief": "mod above dotless i"}}}, {"category": "Ll", "key": "0135", "mappings": {"default": {"default": "j hat"}, "mathspeak": {"default": "modifying above j with caret", "brief": "mod above j with caret", "sbrief": "mod above j with caret"}}}, {"category": "Ll", "key": "0137", "mappings": {"default": {"default": "k cedilla"}, "mathspeak": {"default": "modifying above k with cedilla", "brief": "mod above k with cedilla", "sbrief": "mod above k with cedilla"}}}, {"category": "Ll", "key": "013A", "mappings": {"default": {"default": "l acute"}, "mathspeak": {"default": "modifying above l with acute", "brief": "mod above l with acute", "sbrief": "mod above l with acute"}}}, {"category": "Ll", "key": "013C", "mappings": {"default": {"default": "l cedilla"}, "mathspeak": {"default": "modifying above l with cedilla", "brief": "mod above l with cedilla", "sbrief": "mod above l with cedilla"}}}, {"category": "Ll", "key": "013E", "mappings": {"default": {"default": "l caron"}, "mathspeak": {"default": "modifying above l with caron", "brief": "mod above l with caron", "sbrief": "mod above l with caron"}}}, {"category": "Ll", "key": "0140", "mappings": {"default": {"default": "l middle dot"}, "mathspeak": {"default": "modifying above l with middle dot", "brief": "mod above l with middle dot", "sbrief": "mod above l with middle dot"}}}, {"category": "Ll", "key": "0144", "mappings": {"default": {"default": "n acute"}, "mathspeak": {"default": "modifying above n with acute", "brief": "mod above n with acute", "sbrief": "mod above n with acute"}}}, {"category": "Ll", "key": "0146", "mappings": {"default": {"default": "n cedilla"}, "mathspeak": {"default": "modifying above n with cedilla", "brief": "mod above n with cedilla", "sbrief": "mod above n with cedilla"}}}, {"category": "Ll", "key": "0148", "mappings": {"default": {"default": "n caron"}, "mathspeak": {"default": "modifying above n with caron", "brief": "mod above n with caron", "sbrief": "mod above n with caron"}}}, {"category": "Ll", "key": "014D", "mappings": {"default": {"default": "o overbar"}, "mathspeak": {"default": "o overbar", "brief": "o overbar", "sbrief": "o overbar"}}}, {"category": "Ll", "key": "014F", "mappings": {"default": {"default": "o breve"}, "mathspeak": {"default": "modifying above o with breve", "brief": "mod above o with breve", "sbrief": "mod above o with breve"}}}, {"category": "Ll", "key": "0151", "mappings": {"default": {"default": "o double acute"}, "mathspeak": {"default": "modifying above o with double acute", "brief": "mod above o with double acute", "sbrief": "mod above o with double acute"}}}, {"category": "Ll", "key": "0155", "mappings": {"default": {"default": "r acute"}, "mathspeak": {"default": "modifying above r with acute", "brief": "mod above r with acute", "sbrief": "mod above r with acute"}}}, {"category": "Ll", "key": "0157", "mappings": {"default": {"default": "r cedilla"}, "mathspeak": {"default": "modifying above r with cedilla", "brief": "mod above r with cedilla", "sbrief": "mod above r with cedilla"}}}, {"category": "Ll", "key": "0159", "mappings": {"default": {"default": "r caron"}, "mathspeak": {"default": "modifying above r with caron", "brief": "mod above r with caron", "sbrief": "mod above r with caron"}}}, {"category": "Ll", "key": "015B", "mappings": {"default": {"default": "s acute"}, "mathspeak": {"default": "modifying above s with acute", "brief": "mod above s with acute", "sbrief": "mod above s with acute"}}}, {"category": "Ll", "key": "015D", "mappings": {"default": {"default": "s hat"}, "mathspeak": {"default": "modifying above s with caret", "brief": "mod above s with caret", "sbrief": "mod above s with caret"}}}, {"category": "Ll", "key": "015F", "mappings": {"default": {"default": "s cedilla"}, "mathspeak": {"default": "modifying above s with cedilla", "brief": "mod above s with cedilla", "sbrief": "mod above s with cedilla"}}}, {"category": "Ll", "key": "0161", "mappings": {"default": {"default": "s caron"}, "mathspeak": {"default": "modifying above s with caron", "brief": "mod above s with caron", "sbrief": "mod above s with caron"}}}, {"category": "Ll", "key": "0163", "mappings": {"default": {"default": "t cedilla"}, "mathspeak": {"default": "modifying above t with cedilla", "brief": "mod above t with cedilla", "sbrief": "mod above t with cedilla"}}}, {"category": "Ll", "key": "0165", "mappings": {"default": {"default": "t caron"}, "mathspeak": {"default": "modifying above t with caron", "brief": "mod above t with caron", "sbrief": "mod above t with caron"}}}, {"category": "Ll", "key": "0169", "mappings": {"default": {"default": "u tilde"}, "mathspeak": {"default": "u overtilde", "brief": "u overtilde", "sbrief": "u overtilde"}}}, {"category": "Ll", "key": "016B", "mappings": {"default": {"default": "u overbar"}, "mathspeak": {"default": "u overbar", "brief": "u overbar", "sbrief": "u overbar"}}}, {"category": "Ll", "key": "016D", "mappings": {"default": {"default": "u breve"}, "mathspeak": {"default": "modifying above u with breve", "brief": "mod above u with breve", "sbrief": "mod above u with breve"}}}, {"category": "Ll", "key": "016F", "mappings": {"default": {"default": "u ring"}, "mathspeak": {"default": "modifying above u with ring", "brief": "mod above u with ring", "sbrief": "mod above u with ring"}}}, {"category": "Ll", "key": "0171", "mappings": {"default": {"default": "u double acute"}, "mathspeak": {"default": "modifying above u with double acute", "brief": "mod above u with double acute", "sbrief": "mod above u with double acute"}}}, {"category": "Ll", "key": "0173", "mappings": {"default": {"default": "u ogonek"}, "mathspeak": {"default": "modifying above u with ogonek", "brief": "mod above u with ogonek", "sbrief": "mod above u with ogonek"}}}, {"category": "Ll", "key": "0175", "mappings": {"default": {"default": "w hat"}, "mathspeak": {"default": "modifying above w with caret", "brief": "mod above w with caret", "sbrief": "mod above w with caret"}}}, {"category": "Ll", "key": "0177", "mappings": {"default": {"default": "y hat"}, "mathspeak": {"default": "modifying above y with caret", "brief": "mod above y with caret", "sbrief": "mod above y with caret"}}}, {"category": "Ll", "key": "017A", "mappings": {"default": {"default": "z acute"}, "mathspeak": {"default": "modifying above z with acute", "brief": "mod above z with acute", "sbrief": "mod above z with acute"}}}, {"category": "Ll", "key": "017C", "mappings": {"default": {"default": "z overdot"}, "mathspeak": {"default": "modifying above z with dot", "brief": "mod above z with dot", "sbrief": "mod above z with dot"}}}, {"category": "Ll", "key": "017E", "mappings": {"default": {"default": "z caron"}, "mathspeak": {"default": "modifying above z with caron", "brief": "mod above z with caron", "sbrief": "mod above z with caron"}}}, {"category": "Ll", "key": "01CE", "mappings": {"default": {"default": "a caron"}, "mathspeak": {"default": "modifying above a with caron", "brief": "mod above a with caron", "sbrief": "mod above a with caron"}}}, {"category": "Ll", "key": "01D0", "mappings": {"default": {"default": "i caron"}, "mathspeak": {"default": "modifying above i with caron", "brief": "mod above i with caron", "sbrief": "mod above i with caron"}}}, {"category": "Ll", "key": "01D2", "mappings": {"default": {"default": "o caron"}, "mathspeak": {"default": "modifying above o with caron", "brief": "mod above o with caron", "sbrief": "mod above o with caron"}}}, {"category": "Ll", "key": "01D4", "mappings": {"default": {"default": "u caron"}, "mathspeak": {"default": "modifying above u with caron", "brief": "mod above u with caron", "sbrief": "mod above u with caron"}}}, {"category": "Ll", "key": "01E7", "mappings": {"default": {"default": "g caron"}, "mathspeak": {"default": "modifying above g with caron", "brief": "mod above g with caron", "sbrief": "mod above g with caron"}}}, {"category": "Ll", "key": "01E9", "mappings": {"default": {"default": "k caron"}, "mathspeak": {"default": "modifying above k with caron", "brief": "mod above k with caron", "sbrief": "mod above k with caron"}}}, {"category": "Ll", "key": "01EB", "mappings": {"default": {"default": "o ogonek"}, "mathspeak": {"default": "modifying above o with ogonek", "brief": "mod above o with ogonek", "sbrief": "mod above o with ogonek"}}}, {"category": "Ll", "key": "01F0", "mappings": {"default": {"default": "j caron"}, "mathspeak": {"default": "modifying above j with caron", "brief": "mod above j with caron", "sbrief": "mod above j with caron"}}}, {"category": "Ll", "key": "01F5", "mappings": {"default": {"default": "g acute"}, "mathspeak": {"default": "modifying above g with acute", "brief": "mod above g with acute", "sbrief": "mod above g with acute"}}}, {"category": "Ll", "key": "01F9", "mappings": {"default": {"default": "n grave"}, "mathspeak": {"default": "modifying above n with grave", "brief": "mod above n with grave", "sbrief": "mod above n with grave"}}}, {"category": "Ll", "key": "0201", "mappings": {"default": {"default": "a double grave"}, "mathspeak": {"default": "modifying above a with double grave", "brief": "mod above a with double grave", "sbrief": "mod above a with double grave"}}}, {"category": "Ll", "key": "0203", "mappings": {"default": {"default": "a inverted breve"}, "mathspeak": {"default": "modifying above a with inverted breve", "brief": "mod above a with inverted breve", "sbrief": "mod above a with inverted breve"}}}, {"category": "Ll", "key": "0205", "mappings": {"default": {"default": "e double grave"}, "mathspeak": {"default": "modifying above e with double grave", "brief": "mod above e with double grave", "sbrief": "mod above e with double grave"}}}, {"category": "Ll", "key": "0207", "mappings": {"default": {"default": "e inverted breve"}, "mathspeak": {"default": "modifying above e with inverted breve", "brief": "mod above e with inverted breve", "sbrief": "mod above e with inverted breve"}}}, {"category": "Ll", "key": "0209", "mappings": {"default": {"default": "i double grave"}, "mathspeak": {"default": "modifying above i with double grave", "brief": "mod above i with double grave", "sbrief": "mod above i with double grave"}}}, {"category": "Ll", "key": "020B", "mappings": {"default": {"default": "i inverted breve"}, "mathspeak": {"default": "modifying above i with inverted breve", "brief": "mod above i with inverted breve", "sbrief": "mod above i with inverted breve"}}}, {"category": "Ll", "key": "020D", "mappings": {"default": {"default": "o double grave"}, "mathspeak": {"default": "modifying above o with double grave", "brief": "mod above o with double grave", "sbrief": "mod above o with double grave"}}}, {"category": "Ll", "key": "020F", "mappings": {"default": {"default": "o inverted breve"}, "mathspeak": {"default": "modifying above o with inverted breve", "brief": "mod above o with inverted breve", "sbrief": "mod above o with inverted breve"}}}, {"category": "Ll", "key": "0211", "mappings": {"default": {"default": "r double grave"}, "mathspeak": {"default": "modifying above r with double grave", "brief": "mod above r with double grave", "sbrief": "mod above r with double grave"}}}, {"category": "Ll", "key": "0213", "mappings": {"default": {"default": "r inverted breve"}, "mathspeak": {"default": "modifying above r with inverted breve", "brief": "mod above r with inverted breve", "sbrief": "mod above r with inverted breve"}}}, {"category": "Ll", "key": "0215", "mappings": {"default": {"default": "u double grave"}, "mathspeak": {"default": "modifying above u with double grave", "brief": "mod above u with double grave", "sbrief": "mod above u with double grave"}}}, {"category": "Ll", "key": "0217", "mappings": {"default": {"default": "u inverted breve"}, "mathspeak": {"default": "modifying above u with inverted breve", "brief": "mod above u with inverted breve", "sbrief": "mod above u with inverted breve"}}}, {"category": "Ll", "key": "0219", "mappings": {"default": {"default": "s comma below"}, "mathspeak": {"default": "modifying below s with comma below", "brief": "mod below s with comma below", "sbrief": "mod below s with comma below"}}}, {"category": "Ll", "key": "021B", "mappings": {"default": {"default": "t comma below"}, "mathspeak": {"default": "modifying below t with comma below", "brief": "mod below t with comma below", "sbrief": "mod below t with comma below"}}}, {"category": "Ll", "key": "021F", "mappings": {"default": {"default": "h caron"}, "mathspeak": {"default": "modifying above h with caron", "brief": "mod above h with caron", "sbrief": "mod above h with caron"}}}, {"category": "Ll", "key": "0227", "mappings": {"default": {"default": "a overdot"}, "mathspeak": {"default": "modifying above a with dot", "brief": "mod above a with dot", "sbrief": "mod above a with dot"}}}, {"category": "Ll", "key": "0229", "mappings": {"default": {"default": "e cedilla"}, "mathspeak": {"default": "modifying above e with cedilla", "brief": "mod above e with cedilla", "sbrief": "mod above e with cedilla"}}}, {"category": "Ll", "key": "022F", "mappings": {"default": {"default": "o overdot"}, "mathspeak": {"default": "modifying above o with dot", "brief": "mod above o with dot", "sbrief": "mod above o with dot"}}}, {"category": "Ll", "key": "0233", "mappings": {"default": {"default": "y overbar"}, "mathspeak": {"default": "y overbar", "brief": "y overbar", "sbrief": "y overbar"}}}, {"category": "Ll", "key": "0237", "mappings": {"default": {"default": "dotless j"}, "mathspeak": {"default": "modifying above dotless j", "brief": "mod above dotless j", "sbrief": "mod above dotless j"}}}, {"category": "Ll", "key": "1E01", "mappings": {"default": {"default": "a ring below"}, "mathspeak": {"default": "modifying below a with ring below", "brief": "mod below a with ring below", "sbrief": "mod below a with ring below"}}}, {"category": "Ll", "key": "1E03", "mappings": {"default": {"default": "b overdot"}, "mathspeak": {"default": "modifying above b with dot", "brief": "mod above b with dot", "sbrief": "mod above b with dot"}}}, {"category": "Ll", "key": "1E05", "mappings": {"default": {"default": "b underdot"}, "mathspeak": {"default": "modifying below b with dot", "brief": "mod below b with dot", "sbrief": "mod below b with dot"}}}, {"category": "Ll", "key": "1E07", "mappings": {"default": {"default": "b underbar"}, "mathspeak": {"default": "b underbar", "brief": "b underbar", "sbrief": "b underbar"}}}, {"category": "Ll", "key": "1E0B", "mappings": {"default": {"default": "d overdot"}, "mathspeak": {"default": "modifying above d with dot", "brief": "mod above d with dot", "sbrief": "mod above d with dot"}}}, {"category": "Ll", "key": "1E0D", "mappings": {"default": {"default": "d underdot"}, "mathspeak": {"default": "modifying below d with dot", "brief": "mod below d with dot", "sbrief": "mod below d with dot"}}}, {"category": "Ll", "key": "1E0F", "mappings": {"default": {"default": "d underbar"}, "mathspeak": {"default": "d underbar", "brief": "d underbar", "sbrief": "d underbar"}}}, {"category": "Ll", "key": "1E11", "mappings": {"default": {"default": "d cedilla"}, "mathspeak": {"default": "modifying above d with cedilla", "brief": "mod above d with cedilla", "sbrief": "mod above d with cedilla"}}}, {"category": "Ll", "key": "1E13", "mappings": {"default": {"default": "d underhat"}, "mathspeak": {"default": "modifying below d with caret", "brief": "mod below d with caret", "sbrief": "mod below d with caret"}}}, {"category": "Ll", "key": "1E19", "mappings": {"default": {"default": "e underhat"}, "mathspeak": {"default": "modifying below e with caret", "brief": "mod below e with caret", "sbrief": "mod below e with caret"}}}, {"category": "Ll", "key": "1E1B", "mappings": {"default": {"default": "e tilde below"}, "mathspeak": {"default": "e undertilde", "brief": "e undertilde", "sbrief": "e undertilde"}}}, {"category": "Ll", "key": "1E1F", "mappings": {"default": {"default": "f overdot"}, "mathspeak": {"default": "modifying above f with dot", "brief": "mod above f with dot", "sbrief": "mod above f with dot"}}}, {"category": "Ll", "key": "1E21", "mappings": {"default": {"default": "g overbar"}, "mathspeak": {"default": "g overbar", "brief": "g overbar", "sbrief": "g overbar"}}}, {"category": "Ll", "key": "1E23", "mappings": {"default": {"default": "h overdot"}, "mathspeak": {"default": "modifying above h with dot", "brief": "mod above h with dot", "sbrief": "mod above h with dot"}}}, {"category": "Ll", "key": "1E25", "mappings": {"default": {"default": "h underdot"}, "mathspeak": {"default": "modifying below h with dot", "brief": "mod below h with dot", "sbrief": "mod below h with dot"}}}, {"category": "Ll", "key": "1E27", "mappings": {"default": {"default": "h double overdot"}, "mathspeak": {"default": "modifying above h with double dot", "brief": "mod above h with double dot", "sbrief": "mod above h with double dot"}}}, {"category": "Ll", "key": "1E29", "mappings": {"default": {"default": "h cedilla"}, "mathspeak": {"default": "modifying above h with cedilla", "brief": "mod above h with cedilla", "sbrief": "mod above h with cedilla"}}}, {"category": "Ll", "key": "1E2B", "mappings": {"default": {"default": "h breve below"}, "mathspeak": {"default": "modifying below h with breve below", "brief": "mod below h with breve below", "sbrief": "mod below h with breve below"}}}, {"category": "Ll", "key": "1E2D", "mappings": {"default": {"default": "i tilde below"}, "mathspeak": {"default": "i undertilde", "brief": "i undertilde", "sbrief": "i undertilde"}}}, {"category": "Ll", "key": "1E31", "mappings": {"default": {"default": "k acute"}, "mathspeak": {"default": "modifying above k with acute", "brief": "mod above k with acute", "sbrief": "mod above k with acute"}}}, {"category": "Ll", "key": "1E33", "mappings": {"default": {"default": "k underdot"}, "mathspeak": {"default": "modifying below k with dot", "brief": "mod below k with dot", "sbrief": "mod below k with dot"}}}, {"category": "Ll", "key": "1E35", "mappings": {"default": {"default": "k underbar"}, "mathspeak": {"default": "k underbar", "brief": "k underbar", "sbrief": "k underbar"}}}, {"category": "Ll", "key": "1E37", "mappings": {"default": {"default": "l underdot"}, "mathspeak": {"default": "modifying below l with dot", "brief": "mod below l with dot", "sbrief": "mod below l with dot"}}}, {"category": "Ll", "key": "1E3B", "mappings": {"default": {"default": "l underbar"}, "mathspeak": {"default": "l underbar", "brief": "l underbar", "sbrief": "l underbar"}}}, {"category": "Ll", "key": "1E3D", "mappings": {"default": {"default": "l underhat"}, "mathspeak": {"default": "modifying below l with caret", "brief": "mod below l with caret", "sbrief": "mod below l with caret"}}}, {"category": "Ll", "key": "1E3F", "mappings": {"default": {"default": "m acute"}, "mathspeak": {"default": "modifying above m with acute", "brief": "mod above m with acute", "sbrief": "mod above m with acute"}}}, {"category": "Ll", "key": "1E41", "mappings": {"default": {"default": "m overdot"}, "mathspeak": {"default": "modifying above m with dot", "brief": "mod above m with dot", "sbrief": "mod above m with dot"}}}, {"category": "Ll", "key": "1E43", "mappings": {"default": {"default": "m underdot"}, "mathspeak": {"default": "modifying below m with dot", "brief": "mod below m with dot", "sbrief": "mod below m with dot"}}}, {"category": "Ll", "key": "1E45", "mappings": {"default": {"default": "n overdot"}, "mathspeak": {"default": "modifying above n with dot", "brief": "mod above n with dot", "sbrief": "mod above n with dot"}}}, {"category": "Ll", "key": "1E47", "mappings": {"default": {"default": "n underdot"}, "mathspeak": {"default": "modifying below n with dot", "brief": "mod below n with dot", "sbrief": "mod below n with dot"}}}, {"category": "Ll", "key": "1E49", "mappings": {"default": {"default": "n underbar"}, "mathspeak": {"default": "n underbar", "brief": "n underbar", "sbrief": "n underbar"}}}, {"category": "Ll", "key": "1E4B", "mappings": {"default": {"default": "n underhat"}, "mathspeak": {"default": "modifying below n with caret", "brief": "mod below n with caret", "sbrief": "mod below n with caret"}}}, {"category": "Ll", "key": "1E55", "mappings": {"default": {"default": "p acute"}, "mathspeak": {"default": "modifying above p with acute", "brief": "mod above p with acute", "sbrief": "mod above p with acute"}}}, {"category": "Ll", "key": "1E57", "mappings": {"default": {"default": "p overdot"}, "mathspeak": {"default": "modifying above p with dot", "brief": "mod above p with dot", "sbrief": "mod above p with dot"}}}, {"category": "Ll", "key": "1E59", "mappings": {"default": {"default": "r overdot"}, "mathspeak": {"default": "modifying above r with dot", "brief": "mod above r with dot", "sbrief": "mod above r with dot"}}}, {"category": "Ll", "key": "1E5B", "mappings": {"default": {"default": "r underdot"}, "mathspeak": {"default": "modifying below r with dot", "brief": "mod below r with dot", "sbrief": "mod below r with dot"}}}, {"category": "Ll", "key": "1E5F", "mappings": {"default": {"default": "r underbar"}, "mathspeak": {"default": "r underbar", "brief": "r underbar", "sbrief": "r underbar"}}}, {"category": "Ll", "key": "1E61", "mappings": {"default": {"default": "s overdot"}, "mathspeak": {"default": "modifying above s with dot", "brief": "mod above s with dot", "sbrief": "mod above s with dot"}}}, {"category": "Ll", "key": "1E63", "mappings": {"default": {"default": "s underdot"}, "mathspeak": {"default": "modifying below s with dot", "brief": "mod below s with dot", "sbrief": "mod below s with dot"}}}, {"category": "Ll", "key": "1E6B", "mappings": {"default": {"default": "t overdot"}, "mathspeak": {"default": "modifying above t with dot", "brief": "mod above t with dot", "sbrief": "mod above t with dot"}}}, {"category": "Ll", "key": "1E6D", "mappings": {"default": {"default": "t underdot"}, "mathspeak": {"default": "modifying below t with dot", "brief": "mod below t with dot", "sbrief": "mod below t with dot"}}}, {"category": "Ll", "key": "1E6F", "mappings": {"default": {"default": "t underbar"}, "mathspeak": {"default": "t underbar", "brief": "t underbar", "sbrief": "t underbar"}}}, {"category": "Ll", "key": "1E71", "mappings": {"default": {"default": "t underhat"}, "mathspeak": {"default": "modifying below t with caret", "brief": "mod below t with caret", "sbrief": "mod below t with caret"}}}, {"category": "Ll", "key": "1E73", "mappings": {"default": {"default": "u double underdot"}, "mathspeak": {"default": "modifying below u with double dot", "brief": "mod below u with double dot", "sbrief": "mod below u with double dot"}}}, {"category": "Ll", "key": "1E75", "mappings": {"default": {"default": "u tilde below"}, "mathspeak": {"default": "u undertilde", "brief": "u undertilde", "sbrief": "u undertilde"}}}, {"category": "Ll", "key": "1E77", "mappings": {"default": {"default": "u underhat"}, "mathspeak": {"default": "modifying below u with caret", "brief": "mod below u with caret", "sbrief": "mod below u with caret"}}}, {"category": "Ll", "key": "1E7D", "mappings": {"default": {"default": "v tilde"}, "mathspeak": {"default": "v overtilde", "brief": "v overtilde", "sbrief": "v overtilde"}}}, {"category": "Ll", "key": "1E7F", "mappings": {"default": {"default": "v underdot"}, "mathspeak": {"default": "modifying below v with dot", "brief": "mod below v with dot", "sbrief": "mod below v with dot"}}}, {"category": "Ll", "key": "1E81", "mappings": {"default": {"default": "w grave"}, "mathspeak": {"default": "modifying above w with grave", "brief": "mod above w with grave", "sbrief": "mod above w with grave"}}}, {"category": "Ll", "key": "1E83", "mappings": {"default": {"default": "w acute"}, "mathspeak": {"default": "modifying above w with acute", "brief": "mod above w with acute", "sbrief": "mod above w with acute"}}}, {"category": "Ll", "key": "1E85", "mappings": {"default": {"default": "w double overdot"}, "mathspeak": {"default": "modifying above w with double dot", "brief": "mod above w with double dot", "sbrief": "mod above w with double dot"}}}, {"category": "Ll", "key": "1E87", "mappings": {"default": {"default": "w overdot"}, "mathspeak": {"default": "modifying above w with dot", "brief": "mod above w with dot", "sbrief": "mod above w with dot"}}}, {"category": "Ll", "key": "1E89", "mappings": {"default": {"default": "w underdot"}, "mathspeak": {"default": "modifying below w with dot", "brief": "mod below w with dot", "sbrief": "mod below w with dot"}}}, {"category": "Ll", "key": "1E8B", "mappings": {"default": {"default": "x overdot"}, "mathspeak": {"default": "modifying above x with dot", "brief": "mod above x with dot", "sbrief": "mod above x with dot"}}}, {"category": "Ll", "key": "1E8D", "mappings": {"default": {"default": "x double overdot"}, "mathspeak": {"default": "modifying above x with double dot", "brief": "mod above x with double dot", "sbrief": "mod above x with double dot"}}}, {"category": "Ll", "key": "1E8F", "mappings": {"default": {"default": "y overdot"}, "mathspeak": {"default": "modifying above y with dot", "brief": "mod above y with dot", "sbrief": "mod above y with dot"}}}, {"category": "Ll", "key": "1E91", "mappings": {"default": {"default": "z hat"}, "mathspeak": {"default": "modifying above z with caret", "brief": "mod above z with caret", "sbrief": "mod above z with caret"}}}, {"category": "Ll", "key": "1E93", "mappings": {"default": {"default": "z underdot"}, "mathspeak": {"default": "modifying below z with dot", "brief": "mod below z with dot", "sbrief": "mod below z with dot"}}}, {"category": "Ll", "key": "1E95", "mappings": {"default": {"default": "z underbar"}, "mathspeak": {"default": "z underbar", "brief": "z underbar", "sbrief": "z underbar"}}}, {"category": "Ll", "key": "1E96", "mappings": {"default": {"default": "h underbar"}, "mathspeak": {"default": "h underbar", "brief": "h underbar", "sbrief": "h underbar"}}}, {"category": "Ll", "key": "1E97", "mappings": {"default": {"default": "t double overdot"}, "mathspeak": {"default": "modifying above t with double dot", "brief": "mod above t with double dot", "sbrief": "mod above t with double dot"}}}, {"category": "Ll", "key": "1E98", "mappings": {"default": {"default": "w ring"}, "mathspeak": {"default": "modifying above w with ring", "brief": "mod above w with ring", "sbrief": "mod above w with ring"}}}, {"category": "Ll", "key": "1E99", "mappings": {"default": {"default": "y ring"}, "mathspeak": {"default": "modifying above y with ring", "brief": "mod above y with ring", "sbrief": "mod above y with ring"}}}, {"category": "Ll", "key": "1E9A", "mappings": {"default": {"default": "a right half ring"}, "mathspeak": {"default": "modifying above a with right half ring", "brief": "mod above a with right half ring", "sbrief": "mod above a with right half ring"}}}, {"category": "Ll", "key": "1EA1", "mappings": {"default": {"default": "a underdot"}, "mathspeak": {"default": "modifying below a with dot", "brief": "mod below a with dot", "sbrief": "mod below a with dot"}}}, {"category": "Ll", "key": "1EA3", "mappings": {"default": {"default": "a hook"}, "mathspeak": {"default": "modifying above a with hook", "brief": "mod above a with hook", "sbrief": "mod above a with hook"}}}, {"category": "Ll", "key": "1EB9", "mappings": {"default": {"default": "e underdot"}, "mathspeak": {"default": "modifying below e with dot", "brief": "mod below e with dot", "sbrief": "mod below e with dot"}}}, {"category": "Ll", "key": "1EBB", "mappings": {"default": {"default": "e hook"}, "mathspeak": {"default": "modifying above e with hook", "brief": "mod above e with hook", "sbrief": "mod above e with hook"}}}, {"category": "Ll", "key": "1EBD", "mappings": {"default": {"default": "e tilde"}, "mathspeak": {"default": "e overtilde", "brief": "e overtilde", "sbrief": "e overtilde"}}}, {"category": "Ll", "key": "1EC9", "mappings": {"default": {"default": "i hook"}, "mathspeak": {"default": "modifying above i with hook", "brief": "mod above i with hook", "sbrief": "mod above i with hook"}}}, {"category": "Ll", "key": "1ECB", "mappings": {"default": {"default": "i underdot"}, "mathspeak": {"default": "modifying below i with dot", "brief": "mod below i with dot", "sbrief": "mod below i with dot"}}}, {"category": "Ll", "key": "1ECD", "mappings": {"default": {"default": "o underdot"}, "mathspeak": {"default": "modifying below o with dot", "brief": "mod below o with dot", "sbrief": "mod below o with dot"}}}, {"category": "Ll", "key": "1ECF", "mappings": {"default": {"default": "o hook"}, "mathspeak": {"default": "modifying above o with hook", "brief": "mod above o with hook", "sbrief": "mod above o with hook"}}}, {"category": "Ll", "key": "1EE5", "mappings": {"default": {"default": "u underdot"}, "mathspeak": {"default": "modifying below u with dot", "brief": "mod below u with dot", "sbrief": "mod below u with dot"}}}, {"category": "Ll", "key": "1EE7", "mappings": {"default": {"default": "u hook"}, "mathspeak": {"default": "modifying above u with hook", "brief": "mod above u with hook", "sbrief": "mod above u with hook"}}}, {"category": "Ll", "key": "1EF3", "mappings": {"default": {"default": "y grave"}, "mathspeak": {"default": "modifying above y with grave", "brief": "mod above y with grave", "sbrief": "mod above y with grave"}}}, {"category": "Ll", "key": "1EF5", "mappings": {"default": {"default": "y underdot"}, "mathspeak": {"default": "modifying below y with dot", "brief": "mod below y with dot", "sbrief": "mod below y with dot"}}}, {"category": "Ll", "key": "1EF7", "mappings": {"default": {"default": "y hook"}, "mathspeak": {"default": "modifying above y with hook", "brief": "mod above y with hook", "sbrief": "mod above y with hook"}}}, {"category": "Ll", "key": "1EF9", "mappings": {"default": {"default": "y tilde"}, "mathspeak": {"default": "y overtilde", "brief": "y overtilde", "sbrief": "y overtilde"}}}], "en/symbols/latin-rest.js": [{"locale": "en"}, {"category": "Ll", "mappings": {"default": {"default": "italic h", "physics": "planck constant"}}, "key": "210E"}, {"category": "Mn", "key": "0363", "mappings": {"default": {"default": "combining a"}}}, {"category": "Mn", "key": "0364", "mappings": {"default": {"default": "combining e"}}}, {"category": "Mn", "key": "0365", "mappings": {"default": {"default": "combining i"}}}, {"category": "Mn", "key": "0366", "mappings": {"default": {"default": "combining o"}}}, {"category": "Mn", "key": "0367", "mappings": {"default": {"default": "combining u"}}}, {"category": "Mn", "key": "0368", "mappings": {"default": {"default": "combining c"}}}, {"category": "Mn", "key": "0369", "mappings": {"default": {"default": "combining d"}}}, {"category": "Mn", "key": "036A", "mappings": {"default": {"default": "combining h"}}}, {"category": "Mn", "key": "036B", "mappings": {"default": {"default": "combining m"}}}, {"category": "Mn", "key": "036C", "mappings": {"default": {"default": "combining r"}}}, {"category": "Mn", "key": "036D", "mappings": {"default": {"default": "combining t"}}}, {"category": "Mn", "key": "036E", "mappings": {"default": {"default": "combining v"}}}, {"category": "Mn", "key": "036F", "mappings": {"default": {"default": "combining x"}}}, {"category": "Lm", "key": "1D62", "mappings": {"default": {"default": "subscript i"}}}, {"category": "Lm", "key": "1D63", "mappings": {"default": {"default": "subscript r"}}}, {"category": "Lm", "key": "1D64", "mappings": {"default": {"default": "subscript u"}}}, {"category": "Lm", "key": "1D65", "mappings": {"default": {"default": "subscript v"}}}, {"category": "Mn", "key": "1DCA", "mappings": {"default": {"default": "combining r below"}}}, {"category": "Mn", "key": "1DD3", "mappings": {"default": {"default": "combining flattened open a above"}}}, {"category": "Mn", "key": "1DD4", "mappings": {"default": {"default": "combining ae"}}}, {"category": "Mn", "key": "1DD5", "mappings": {"default": {"default": "combining ao"}}}, {"category": "Mn", "key": "1DD6", "mappings": {"default": {"default": "combining av"}}}, {"category": "Mn", "key": "1DD7", "mappings": {"default": {"default": "combining c cedilla"}}}, {"category": "Mn", "key": "1DD8", "mappings": {"default": {"default": "combining insular d"}}}, {"category": "Mn", "key": "1DD9", "mappings": {"default": {"default": "combining eth"}}}, {"category": "Mn", "key": "1DDA", "mappings": {"default": {"default": "combining g"}}}, {"category": "Mn", "key": "1DDB", "mappings": {"default": {"default": "combining small cap G"}, "mathspeak": {"default": "combining small upper G"}}}, {"category": "Mn", "key": "1DDC", "mappings": {"default": {"default": "combining k"}}}, {"category": "Mn", "key": "1DDD", "mappings": {"default": {"default": "combining l"}}}, {"category": "Mn", "key": "1DDE", "mappings": {"default": {"default": "combining small cap L"}, "mathspeak": {"default": "combining small upper L"}}}, {"category": "Mn", "key": "1DDF", "mappings": {"default": {"default": "combining small cap M"}, "mathspeak": {"default": "combining small upper M"}}}, {"category": "Mn", "key": "1DE0", "mappings": {"default": {"default": "combining n"}}}, {"category": "Mn", "key": "1DE1", "mappings": {"default": {"default": "combining small cap N"}, "mathspeak": {"default": "combining small upper N"}}}, {"category": "Mn", "key": "1DE2", "mappings": {"default": {"default": "combining small cap R"}, "mathspeak": {"default": "combining small upper R"}}}, {"category": "Mn", "key": "1DE3", "mappings": {"default": {"default": "combining r rotunda"}}}, {"category": "Mn", "key": "1DE4", "mappings": {"default": {"default": "combining s"}}}, {"category": "Mn", "key": "1DE5", "mappings": {"default": {"default": "combining long s"}}}, {"category": "Mn", "key": "1DE6", "mappings": {"default": {"default": "combining z"}}}, {"category": "Lm", "key": "2071", "mappings": {"default": {"default": "superscript i"}}}, {"category": "Lm", "key": "207F", "mappings": {"default": {"default": "superscript n"}}}, {"category": "Lm", "key": "2090", "mappings": {"default": {"default": "subscript a"}}}, {"category": "Lm", "key": "2091", "mappings": {"default": {"default": "subscript e"}}}, {"category": "Lm", "key": "2092", "mappings": {"default": {"default": "subscript o"}}}, {"category": "Lm", "key": "2093", "mappings": {"default": {"default": "subscript x"}}}, {"category": "Lm", "key": "2094", "mappings": {"default": {"default": "subscript schwa"}}}, {"category": "Lm", "key": "2095", "mappings": {"default": {"default": "subscript h"}}}, {"category": "Lm", "key": "2096", "mappings": {"default": {"default": "subscript k"}}}, {"category": "Lm", "key": "2097", "mappings": {"default": {"default": "subscript l"}}}, {"category": "Lm", "key": "2098", "mappings": {"default": {"default": "subscript m"}}}, {"category": "Lm", "key": "2099", "mappings": {"default": {"default": "subscript n"}}}, {"category": "Lm", "key": "209A", "mappings": {"default": {"default": "subscript p"}}}, {"category": "Lm", "key": "209B", "mappings": {"default": {"default": "subscript s"}}}, {"category": "Lm", "key": "209C", "mappings": {"default": {"default": "subscript t"}}}, {"category": "Lm", "key": "2C7C", "mappings": {"default": {"default": "subscript j"}}}, {"category": "So", "key": "1F12A", "mappings": {"default": {"default": "tortoise shell bracketed cap S"}, "mathspeak": {"default": "tortoise shell bracketed upper S"}}}, {"category": "So", "key": "1F12B", "mappings": {"default": {"default": "circled italic cap C"}, "mathspeak": {"default": "circled italic upper C"}}}, {"category": "So", "key": "1F12C", "mappings": {"default": {"default": "circled italic cap R"}, "mathspeak": {"default": "circled italic upper R"}}}, {"category": "So", "key": "1F18A", "mappings": {"default": {"default": "crossed negative squared cap P"}, "mathspeak": {"default": "crossed negative squared upper P"}}}], "en/symbols/latin-upper-double-accent.js": [{"locale": "en"}, {"category": "<PERSON>", "key": "01D5", "mappings": {"default": {"default": "cap U double overdot overbar"}, "mathspeak": {"default": "upper U double overdot overbar"}}}, {"category": "<PERSON>", "key": "01D7", "mappings": {"default": {"default": "cap U double overdot acute"}, "mathspeak": {"default": "upper U double overdot acute"}}}, {"category": "<PERSON>", "key": "01D9", "mappings": {"default": {"default": "cap U double overdot caron"}, "mathspeak": {"default": "upper U double overdot caron"}}}, {"category": "<PERSON>", "key": "01DB", "mappings": {"default": {"default": "cap U double overdot grave"}, "mathspeak": {"default": "upper U double overdot grave"}}}, {"category": "<PERSON>", "key": "01DE", "mappings": {"default": {"default": "cap A double overdot overbar"}, "mathspeak": {"default": "upper A double overdot overbar"}}}, {"category": "<PERSON>", "key": "01E0", "mappings": {"default": {"default": "cap A overdot overbar"}, "mathspeak": {"default": "upper A overdot overbar"}}}, {"category": "<PERSON>", "key": "01EC", "mappings": {"default": {"default": "cap O ogonek overbar"}, "mathspeak": {"default": "upper O ogonek overbar"}}}, {"category": "<PERSON>", "key": "01FA", "mappings": {"default": {"default": "cap A ring acute"}, "mathspeak": {"default": "upper A ring acute"}}}, {"category": "<PERSON>", "key": "022A", "mappings": {"default": {"default": "cap O double overdot overbar"}, "mathspeak": {"default": "upper O double overdot overbar"}}}, {"category": "<PERSON>", "key": "022C", "mappings": {"default": {"default": "cap O tilde overbar"}, "mathspeak": {"default": "upper O tilde overbar"}}}, {"category": "<PERSON>", "key": "0230", "mappings": {"default": {"default": "cap O overdot overbar"}, "mathspeak": {"default": "upper O overdot overbar"}}}, {"category": "<PERSON>", "key": "1E08", "mappings": {"default": {"default": "cap C cedilla acute"}, "mathspeak": {"default": "upper C cedilla acute"}}}, {"category": "<PERSON>", "key": "1E14", "mappings": {"default": {"default": "cap E overbar grave"}, "mathspeak": {"default": "upper E overbar grave"}}}, {"category": "<PERSON>", "key": "1E16", "mappings": {"default": {"default": "cap E overbar acute"}, "mathspeak": {"default": "upper E overbar acute"}}}, {"category": "<PERSON>", "key": "1E1C", "mappings": {"default": {"default": "cap E cedilla breve"}, "mathspeak": {"default": "upper E cedilla breve"}}}, {"category": "<PERSON>", "key": "1E2E", "mappings": {"default": {"default": "cap I double overdot acute"}, "mathspeak": {"default": "upper I double overdot acute"}}}, {"category": "<PERSON>", "key": "1E38", "mappings": {"default": {"default": "cap L underdot overbar"}, "mathspeak": {"default": "upper L underdot overbar"}}}, {"category": "<PERSON>", "key": "1E4C", "mappings": {"default": {"default": "cap O tilde acute"}, "mathspeak": {"default": "upper O tilde acute"}}}, {"category": "<PERSON>", "key": "1E4E", "mappings": {"default": {"default": "cap O tilde double overdot"}, "mathspeak": {"default": "upper O tilde double overdot"}}}, {"category": "<PERSON>", "key": "1E50", "mappings": {"default": {"default": "cap O overbar grave"}, "mathspeak": {"default": "upper O overbar grave"}}}, {"category": "<PERSON>", "key": "1E52", "mappings": {"default": {"default": "cap O overbar acute"}, "mathspeak": {"default": "upper O overbar acute"}}}, {"category": "<PERSON>", "key": "1E5C", "mappings": {"default": {"default": "cap R overbar underdot"}, "mathspeak": {"default": "upper R overbar underdot"}}}, {"category": "<PERSON>", "key": "1E64", "mappings": {"default": {"default": "cap S acute overdot"}, "mathspeak": {"default": "upper S acute overdot"}}}, {"category": "<PERSON>", "key": "1E66", "mappings": {"default": {"default": "cap S caron overdot"}, "mathspeak": {"default": "upper S caron overdot"}}}, {"category": "<PERSON>", "key": "1E68", "mappings": {"default": {"default": "cap S underdot overdot"}, "mathspeak": {"default": "upper S underdot overdot"}}}, {"category": "<PERSON>", "key": "1E78", "mappings": {"default": {"default": "cap U tilde acute"}, "mathspeak": {"default": "upper U tilde acute"}}}, {"category": "<PERSON>", "key": "1E7A", "mappings": {"default": {"default": "cap U overbar double overdot"}, "mathspeak": {"default": "upper U overbar double overdot"}}}, {"category": "<PERSON>", "key": "1EA4", "mappings": {"default": {"default": "cap A hat acute"}, "mathspeak": {"default": "upper A hat acute"}}}, {"category": "<PERSON>", "key": "1EA6", "mappings": {"default": {"default": "cap A hat grave"}, "mathspeak": {"default": "upper A hat grave"}}}, {"category": "<PERSON>", "key": "1EA8", "mappings": {"default": {"default": "cap A hat hook"}, "mathspeak": {"default": "upper A hat hook"}}}, {"category": "<PERSON>", "key": "1EAA", "mappings": {"default": {"default": "cap A hat tilde"}, "mathspeak": {"default": "upper A hat tilde"}}}, {"category": "<PERSON>", "key": "1EAC", "mappings": {"default": {"default": "cap A hat underdot"}, "mathspeak": {"default": "upper A hat underdot"}}}, {"category": "<PERSON>", "key": "1EAE", "mappings": {"default": {"default": "cap A breve acute"}, "mathspeak": {"default": "upper A breve acute"}}}, {"category": "<PERSON>", "key": "1EB0", "mappings": {"default": {"default": "cap A breve grave"}, "mathspeak": {"default": "upper A breve grave"}}}, {"category": "<PERSON>", "key": "1EB2", "mappings": {"default": {"default": "cap A breve hook"}, "mathspeak": {"default": "upper A breve hook"}}}, {"category": "<PERSON>", "key": "1EB4", "mappings": {"default": {"default": "cap A breve tilde"}, "mathspeak": {"default": "upper A breve tilde"}}}, {"category": "<PERSON>", "key": "1EB6", "mappings": {"default": {"default": "cap A breve underdot"}, "mathspeak": {"default": "upper A breve underdot"}}}, {"category": "<PERSON>", "key": "1EBE", "mappings": {"default": {"default": "cap E hat acute"}, "mathspeak": {"default": "upper E hat acute"}}}, {"category": "<PERSON>", "key": "1EC0", "mappings": {"default": {"default": "cap E hat grave"}, "mathspeak": {"default": "upper E hat grave"}}}, {"category": "<PERSON>", "key": "1EC2", "mappings": {"default": {"default": "cap E hat hook"}, "mathspeak": {"default": "upper E hat hook"}}}, {"category": "<PERSON>", "key": "1EC4", "mappings": {"default": {"default": "cap E hat tilde"}, "mathspeak": {"default": "upper E hat tilde"}}}, {"category": "<PERSON>", "key": "1EC6", "mappings": {"default": {"default": "cap E hat underdot"}, "mathspeak": {"default": "upper E hat underdot"}}}, {"category": "<PERSON>", "key": "1ED0", "mappings": {"default": {"default": "cap O hat acute"}, "mathspeak": {"default": "upper O hat acute"}}}, {"category": "<PERSON>", "key": "1ED2", "mappings": {"default": {"default": "cap O hat grave"}, "mathspeak": {"default": "upper O hat grave"}}}, {"category": "<PERSON>", "key": "1ED4", "mappings": {"default": {"default": "cap O hat hook"}, "mathspeak": {"default": "upper O hat hook"}}}, {"category": "<PERSON>", "key": "1ED6", "mappings": {"default": {"default": "cap O hat tilde"}, "mathspeak": {"default": "upper O hat tilde"}}}, {"category": "<PERSON>", "key": "1ED8", "mappings": {"default": {"default": "cap O hat underdot"}, "mathspeak": {"default": "upper O hat underdot"}}}, {"category": "<PERSON>", "key": "1EDA", "mappings": {"default": {"default": "cap O acute prime"}, "mathspeak": {"default": "upper O acute prime"}}}, {"category": "<PERSON>", "key": "1EDC", "mappings": {"default": {"default": "cap O grave prime"}, "mathspeak": {"default": "upper O grave prime"}}}, {"category": "<PERSON>", "key": "1EDE", "mappings": {"default": {"default": "cap O hook prime"}, "mathspeak": {"default": "upper O hook prime"}}}, {"category": "<PERSON>", "key": "1EE0", "mappings": {"default": {"default": "cap O tilde prime"}, "mathspeak": {"default": "upper O tilde prime"}}}, {"category": "<PERSON>", "key": "1EE2", "mappings": {"default": {"default": "cap O underdot prime"}, "mathspeak": {"default": "upper O underdot prime"}}}, {"category": "<PERSON>", "key": "1EE8", "mappings": {"default": {"default": "cap U acute prime"}, "mathspeak": {"default": "upper U acute prime"}}}, {"category": "<PERSON>", "key": "1EEA", "mappings": {"default": {"default": "cap U grave prime"}, "mathspeak": {"default": "upper U grave prime"}}}, {"category": "<PERSON>", "key": "1EEC", "mappings": {"default": {"default": "cap U hook prime"}, "mathspeak": {"default": "upper U hook prime"}}}, {"category": "<PERSON>", "key": "1EEE", "mappings": {"default": {"default": "cap U tilde prime"}, "mathspeak": {"default": "upper U tilde prime"}}}, {"category": "<PERSON>", "key": "1EF0", "mappings": {"default": {"default": "cap U underdot prime"}, "mathspeak": {"default": "upper U underdot prime"}}}], "en/symbols/latin-upper-single-accent.js": [{"locale": "en"}, {"category": "<PERSON>", "key": "00C0", "mappings": {"default": {"default": "cap A grave"}, "mathspeak": {"default": "modifying above upper A with grave", "brief": "mod above upper A with grave", "sbrief": "mod above upper A with grave"}}}, {"category": "<PERSON>", "key": "00C1", "mappings": {"default": {"default": "cap A acute"}, "mathspeak": {"default": "modifying above upper A with acute", "brief": "mod above upper A with acute", "sbrief": "mod above upper A with acute"}}}, {"category": "<PERSON>", "key": "00C2", "mappings": {"default": {"default": "cap A hat"}, "mathspeak": {"default": "modifying above upper A with caret", "brief": "mod above upper A with caret", "sbrief": "mod above upper A with caret"}}}, {"category": "<PERSON>", "key": "00C3", "mappings": {"default": {"default": "cap A tilde"}, "mathspeak": {"default": "upper A overtilde", "brief": "upper A overtilde", "sbrief": "upper A overtilde"}}}, {"category": "<PERSON>", "key": "00C4", "mappings": {"default": {"default": "cap A double overdot"}, "mathspeak": {"default": "modifying above upper A with double dot", "brief": "mod above upper A with double dot", "sbrief": "mod above upper A with double dot"}}}, {"category": "<PERSON>", "key": "00C5", "mappings": {"default": {"default": "cap A ring"}, "mathspeak": {"default": "modifying above upper A with ring", "brief": "mod above upper A with ring", "sbrief": "mod above upper A with ring"}}}, {"category": "<PERSON>", "key": "00C7", "mappings": {"default": {"default": "cap C cedilla"}, "mathspeak": {"default": "modifying above upper C with cedilla", "brief": "mod above upper C with cedilla", "sbrief": "mod above upper C with cedilla"}}}, {"category": "<PERSON>", "key": "00C8", "mappings": {"default": {"default": "cap E grave"}, "mathspeak": {"default": "modifying above upper E with grave", "brief": "mod above upper E with grave", "sbrief": "mod above upper E with grave"}}}, {"category": "<PERSON>", "key": "00C9", "mappings": {"default": {"default": "cap E acute"}, "mathspeak": {"default": "modifying above upper E with acute", "brief": "mod above upper E with acute", "sbrief": "mod above upper E with acute"}}}, {"category": "<PERSON>", "key": "00CA", "mappings": {"default": {"default": "cap E hat"}, "mathspeak": {"default": "modifying above upper E with caret", "brief": "mod above upper E with caret", "sbrief": "mod above upper E with caret"}}}, {"category": "<PERSON>", "key": "00CB", "mappings": {"default": {"default": "cap E double overdot"}, "mathspeak": {"default": "modifying above upper E with double dot", "brief": "mod above upper E with double dot", "sbrief": "mod above upper E with double dot"}}}, {"category": "<PERSON>", "key": "00CC", "mappings": {"default": {"default": "cap I grave"}, "mathspeak": {"default": "modifying above upper I with grave", "brief": "mod above upper I with grave", "sbrief": "mod above upper I with grave"}}}, {"category": "<PERSON>", "key": "00CD", "mappings": {"default": {"default": "cap I acute"}, "mathspeak": {"default": "modifying above upper I with acute", "brief": "mod above upper I with acute", "sbrief": "mod above upper I with acute"}}}, {"category": "<PERSON>", "key": "00CE", "mappings": {"default": {"default": "cap I hat"}, "mathspeak": {"default": "modifying above upper I with caret", "brief": "mod above upper I with caret", "sbrief": "mod above upper I with caret"}}}, {"category": "<PERSON>", "key": "00CF", "mappings": {"default": {"default": "cap I double overdot"}, "mathspeak": {"default": "modifying above upper I with double dot", "brief": "mod above upper I with double dot", "sbrief": "mod above upper I with double dot"}}}, {"category": "<PERSON>", "key": "00D1", "mappings": {"default": {"default": "cap N tilde"}, "mathspeak": {"default": "upper N overtilde", "brief": "upper N overtilde", "sbrief": "upper N overtilde"}}}, {"category": "<PERSON>", "key": "00D2", "mappings": {"default": {"default": "cap O grave"}, "mathspeak": {"default": "modifying above upper O with grave", "brief": "mod above upper O with grave", "sbrief": "mod above upper O with grave"}}}, {"category": "<PERSON>", "key": "00D3", "mappings": {"default": {"default": "cap O acute"}, "mathspeak": {"default": "modifying above upper O with acute", "brief": "mod above upper O with acute", "sbrief": "mod above upper O with acute"}}}, {"category": "<PERSON>", "key": "00D4", "mappings": {"default": {"default": "cap O hat"}, "mathspeak": {"default": "modifying above upper O with caret", "brief": "mod above upper O with caret", "sbrief": "mod above upper O with caret"}}}, {"category": "<PERSON>", "key": "00D5", "mappings": {"default": {"default": "cap O tilde"}, "mathspeak": {"default": "upper O overtilde", "brief": "upper O overtilde", "sbrief": "upper O overtilde"}}}, {"category": "<PERSON>", "key": "00D6", "mappings": {"default": {"default": "cap O double overdot"}, "mathspeak": {"default": "modifying above upper O with double dot", "brief": "mod above upper O with double dot", "sbrief": "mod above upper O with double dot"}}}, {"category": "<PERSON>", "key": "00D9", "mappings": {"default": {"default": "cap U grave"}, "mathspeak": {"default": "modifying above upper U with grave", "brief": "mod above upper U with grave", "sbrief": "mod above upper U with grave"}}}, {"category": "<PERSON>", "key": "00DA", "mappings": {"default": {"default": "cap U acute"}, "mathspeak": {"default": "modifying above upper U with acute", "brief": "mod above upper U with acute", "sbrief": "mod above upper U with acute"}}}, {"category": "<PERSON>", "key": "00DB", "mappings": {"default": {"default": "cap U hat"}, "mathspeak": {"default": "modifying above upper U with caret", "brief": "mod above upper U with caret", "sbrief": "mod above upper U with caret"}}}, {"category": "<PERSON>", "key": "00DC", "mappings": {"default": {"default": "cap U double overdot"}, "mathspeak": {"default": "modifying above upper U with double dot", "brief": "mod above upper U with double dot", "sbrief": "mod above upper U with double dot"}}}, {"category": "<PERSON>", "key": "00DD", "mappings": {"default": {"default": "cap Y acute"}, "mathspeak": {"default": "modifying above upper Y with acute", "brief": "mod above upper Y with acute", "sbrief": "mod above upper Y with acute"}}}, {"category": "<PERSON>", "key": "0100", "mappings": {"default": {"default": "cap A overbar"}, "mathspeak": {"default": "upper A overbar", "brief": "upper A overbar", "sbrief": "upper A overbar"}}}, {"category": "<PERSON>", "key": "0102", "mappings": {"default": {"default": "cap A breve"}, "mathspeak": {"default": "modifying above upper A with breve", "brief": "mod above upper A with breve", "sbrief": "mod above upper A with breve"}}}, {"category": "<PERSON>", "key": "0104", "mappings": {"default": {"default": "cap A ogonek"}, "mathspeak": {"default": "modifying above upper A with ogonek", "brief": "mod above upper A with ogonek", "sbrief": "mod above upper A with ogonek"}}}, {"category": "<PERSON>", "key": "0106", "mappings": {"default": {"default": "cap C acute"}, "mathspeak": {"default": "modifying above upper C with acute", "brief": "mod above upper C with acute", "sbrief": "mod above upper C with acute"}}}, {"category": "<PERSON>", "key": "0108", "mappings": {"default": {"default": "cap C hat"}, "mathspeak": {"default": "modifying above upper C with caret", "brief": "mod above upper C with caret", "sbrief": "mod above upper C with caret"}}}, {"category": "<PERSON>", "key": "010A", "mappings": {"default": {"default": "cap C overdot"}, "mathspeak": {"default": "modifying above upper C with dot", "brief": "mod above upper C with dot", "sbrief": "mod above upper C with dot"}}}, {"category": "<PERSON>", "key": "010C", "mappings": {"default": {"default": "cap C caron"}, "mathspeak": {"default": "modifying above upper C with caron", "brief": "mod above upper C with caron", "sbrief": "mod above upper C with caron"}}}, {"category": "<PERSON>", "key": "010E", "mappings": {"default": {"default": "cap D caron"}, "mathspeak": {"default": "modifying above upper D with caron", "brief": "mod above upper D with caron", "sbrief": "mod above upper D with caron"}}}, {"category": "<PERSON>", "key": "0112", "mappings": {"default": {"default": "cap E overbar"}, "mathspeak": {"default": "upper E overbar", "brief": "upper E overbar", "sbrief": "upper E overbar"}}}, {"category": "<PERSON>", "key": "0114", "mappings": {"default": {"default": "cap E breve"}, "mathspeak": {"default": "modifying above upper E with breve", "brief": "mod above upper E with breve", "sbrief": "mod above upper E with breve"}}}, {"category": "<PERSON>", "key": "0116", "mappings": {"default": {"default": "cap E overdot"}, "mathspeak": {"default": "modifying above upper E with dot", "brief": "mod above upper E with dot", "sbrief": "mod above upper E with dot"}}}, {"category": "<PERSON>", "key": "0118", "mappings": {"default": {"default": "cap E ogonek"}, "mathspeak": {"default": "modifying above upper E with ogonek", "brief": "mod above upper E with ogonek", "sbrief": "mod above upper E with ogonek"}}}, {"category": "<PERSON>", "key": "011A", "mappings": {"default": {"default": "cap E caron"}, "mathspeak": {"default": "modifying above upper E with caron", "brief": "mod above upper E with caron", "sbrief": "mod above upper E with caron"}}}, {"category": "<PERSON>", "key": "011C", "mappings": {"default": {"default": "cap G hat"}, "mathspeak": {"default": "modifying above upper G with caret", "brief": "mod above upper G with caret", "sbrief": "mod above upper G with caret"}}}, {"category": "<PERSON>", "key": "011E", "mappings": {"default": {"default": "cap G breve"}, "mathspeak": {"default": "modifying above upper G with breve", "brief": "mod above upper G with breve", "sbrief": "mod above upper G with breve"}}}, {"category": "<PERSON>", "key": "0120", "mappings": {"default": {"default": "cap G overdot"}, "mathspeak": {"default": "modifying above upper G with dot", "brief": "mod above upper G with dot", "sbrief": "mod above upper G with dot"}}}, {"category": "<PERSON>", "key": "0122", "mappings": {"default": {"default": "cap G cedilla"}, "mathspeak": {"default": "modifying above upper G with cedilla", "brief": "mod above upper G with cedilla", "sbrief": "mod above upper G with cedilla"}}}, {"category": "<PERSON>", "key": "0124", "mappings": {"default": {"default": "cap H hat"}, "mathspeak": {"default": "modifying above upper H with caret", "brief": "mod above upper H with caret", "sbrief": "mod above upper H with caret"}}}, {"category": "<PERSON>", "key": "0128", "mappings": {"default": {"default": "cap I tilde"}, "mathspeak": {"default": "upper I overtilde", "brief": "upper I overtilde", "sbrief": "upper I overtilde"}}}, {"category": "<PERSON>", "key": "012A", "mappings": {"default": {"default": "cap I overbar"}, "mathspeak": {"default": "upper I overbar", "brief": "upper I overbar", "sbrief": "upper I overbar"}}}, {"category": "<PERSON>", "key": "012C", "mappings": {"default": {"default": "cap I breve"}, "mathspeak": {"default": "modifying above upper I with breve", "brief": "mod above upper I with breve", "sbrief": "mod above upper I with breve"}}}, {"category": "<PERSON>", "key": "012E", "mappings": {"default": {"default": "cap I ogonek"}, "mathspeak": {"default": "modifying above upper I with ogonek", "brief": "mod above upper I with ogonek", "sbrief": "mod above upper I with ogonek"}}}, {"category": "<PERSON>", "key": "0130", "mappings": {"default": {"default": "cap I overdot"}, "mathspeak": {"default": "modifying above upper I with dot", "brief": "mod above upper I with dot", "sbrief": "mod above upper I with dot"}}}, {"category": "<PERSON>", "key": "0134", "mappings": {"default": {"default": "cap J hat"}, "mathspeak": {"default": "modifying above upper J with caret", "brief": "mod above upper J with caret", "sbrief": "mod above upper J with caret"}}}, {"category": "<PERSON>", "key": "0136", "mappings": {"default": {"default": "cap K cedilla"}, "mathspeak": {"default": "modifying above upper K with cedilla", "brief": "mod above upper K with cedilla", "sbrief": "mod above upper K with cedilla"}}}, {"category": "<PERSON>", "key": "0139", "mappings": {"default": {"default": "cap L acute"}, "mathspeak": {"default": "modifying above upper L with acute", "brief": "mod above upper L with acute", "sbrief": "mod above upper L with acute"}}}, {"category": "<PERSON>", "key": "013B", "mappings": {"default": {"default": "cap L cedilla"}, "mathspeak": {"default": "modifying above upper L with cedilla", "brief": "mod above upper L with cedilla", "sbrief": "mod above upper L with cedilla"}}}, {"category": "<PERSON>", "key": "013D", "mappings": {"default": {"default": "cap L caron"}, "mathspeak": {"default": "modifying above upper L with caron", "brief": "mod above upper L with caron", "sbrief": "mod above upper L with caron"}}}, {"category": "<PERSON>", "key": "013F", "mappings": {"default": {"default": "cap L middle dot"}, "mathspeak": {"default": "modifying above upper L with middle dot", "brief": "mod above upper L with middle dot", "sbrief": "mod above upper L with middle dot"}}}, {"category": "<PERSON>", "key": "0143", "mappings": {"default": {"default": "cap N acute"}, "mathspeak": {"default": "modifying above upper N with acute", "brief": "mod above upper N with acute", "sbrief": "mod above upper N with acute"}}}, {"category": "<PERSON>", "key": "0145", "mappings": {"default": {"default": "cap N cedilla"}, "mathspeak": {"default": "modifying above upper N with cedilla", "brief": "mod above upper N with cedilla", "sbrief": "mod above upper N with cedilla"}}}, {"category": "<PERSON>", "key": "0147", "mappings": {"default": {"default": "cap N caron"}, "mathspeak": {"default": "modifying above upper N with caron", "brief": "mod above upper N with caron", "sbrief": "mod above upper N with caron"}}}, {"category": "<PERSON>", "key": "014C", "mappings": {"default": {"default": "cap O overbar"}, "mathspeak": {"default": "upper O overbar", "brief": "upper O overbar", "sbrief": "upper O overbar"}}}, {"category": "<PERSON>", "key": "014E", "mappings": {"default": {"default": "cap O breve"}, "mathspeak": {"default": "modifying above upper O with breve", "brief": "mod above upper O with breve", "sbrief": "mod above upper O with breve"}}}, {"category": "<PERSON>", "key": "0150", "mappings": {"default": {"default": "cap O double acute"}, "mathspeak": {"default": "modifying above upper O with double acute", "brief": "mod above upper O with double acute", "sbrief": "mod above upper O with double acute"}}}, {"category": "<PERSON>", "key": "0154", "mappings": {"default": {"default": "cap R acute"}, "mathspeak": {"default": "modifying above upper R with acute", "brief": "mod above upper R with acute", "sbrief": "mod above upper R with acute"}}}, {"category": "<PERSON>", "key": "0156", "mappings": {"default": {"default": "cap R cedilla"}, "mathspeak": {"default": "modifying above upper R with cedilla", "brief": "mod above upper R with cedilla", "sbrief": "mod above upper R with cedilla"}}}, {"category": "<PERSON>", "key": "0158", "mappings": {"default": {"default": "cap R caron"}, "mathspeak": {"default": "modifying above upper R with caron", "brief": "mod above upper R with caron", "sbrief": "mod above upper R with caron"}}}, {"category": "<PERSON>", "key": "015A", "mappings": {"default": {"default": "cap S acute"}, "mathspeak": {"default": "modifying above upper S with acute", "brief": "mod above upper S with acute", "sbrief": "mod above upper S with acute"}}}, {"category": "<PERSON>", "key": "015C", "mappings": {"default": {"default": "cap S hat"}, "mathspeak": {"default": "modifying above upper S with caret", "brief": "mod above upper S with caret", "sbrief": "mod above upper S with caret"}}}, {"category": "<PERSON>", "key": "015E", "mappings": {"default": {"default": "cap S cedilla"}, "mathspeak": {"default": "modifying above upper S with cedilla", "brief": "mod above upper S with cedilla", "sbrief": "mod above upper S with cedilla"}}}, {"category": "<PERSON>", "key": "0160", "mappings": {"default": {"default": "cap S caron"}, "mathspeak": {"default": "modifying above upper S with caron", "brief": "mod above upper S with caron", "sbrief": "mod above upper S with caron"}}}, {"category": "<PERSON>", "key": "0162", "mappings": {"default": {"default": "cap T cedilla"}, "mathspeak": {"default": "modifying above upper T with cedilla", "brief": "mod above upper T with cedilla", "sbrief": "mod above upper T with cedilla"}}}, {"category": "<PERSON>", "key": "0164", "mappings": {"default": {"default": "cap T caron"}, "mathspeak": {"default": "modifying above upper T with caron", "brief": "mod above upper T with caron", "sbrief": "mod above upper T with caron"}}}, {"category": "<PERSON>", "key": "0168", "mappings": {"default": {"default": "cap U tilde"}, "mathspeak": {"default": "upper U overtilde", "brief": "upper U overtilde", "sbrief": "upper U overtilde"}}}, {"category": "<PERSON>", "key": "016A", "mappings": {"default": {"default": "cap U overbar"}, "mathspeak": {"default": "upper U overbar", "brief": "upper U overbar", "sbrief": "upper U overbar"}}}, {"category": "<PERSON>", "key": "016C", "mappings": {"default": {"default": "cap U breve"}, "mathspeak": {"default": "modifying above upper U with breve", "brief": "mod above upper U with breve", "sbrief": "mod above upper U with breve"}}}, {"category": "<PERSON>", "key": "016E", "mappings": {"default": {"default": "cap U ring"}, "mathspeak": {"default": "modifying above upper U with ring", "brief": "mod above upper U with ring", "sbrief": "mod above upper U with ring"}}}, {"category": "<PERSON>", "key": "0170", "mappings": {"default": {"default": "cap U double acute"}, "mathspeak": {"default": "modifying above upper U with double acute", "brief": "mod above upper U with double acute", "sbrief": "mod above upper U with double acute"}}}, {"category": "<PERSON>", "key": "0172", "mappings": {"default": {"default": "cap U ogonek"}, "mathspeak": {"default": "modifying above upper U with ogonek", "brief": "mod above upper U with ogonek", "sbrief": "mod above upper U with ogonek"}}}, {"category": "<PERSON>", "key": "0174", "mappings": {"default": {"default": "cap W hat"}, "mathspeak": {"default": "modifying above upper W with caret", "brief": "mod above upper W with caret", "sbrief": "mod above upper W with caret"}}}, {"category": "<PERSON>", "key": "0176", "mappings": {"default": {"default": "cap Y hat"}, "mathspeak": {"default": "modifying above upper Y with caret", "brief": "mod above upper Y with caret", "sbrief": "mod above upper Y with caret"}}}, {"category": "<PERSON>", "key": "0178", "mappings": {"default": {"default": "cap Y double overdot"}, "mathspeak": {"default": "modifying above upper Y with double dot", "brief": "mod above upper Y with double dot", "sbrief": "mod above upper Y with double dot"}}}, {"category": "<PERSON>", "key": "0179", "mappings": {"default": {"default": "cap Z acute"}, "mathspeak": {"default": "modifying above upper Z with acute", "brief": "mod above upper Z with acute", "sbrief": "mod above upper Z with acute"}}}, {"category": "<PERSON>", "key": "017B", "mappings": {"default": {"default": "cap Z overdot"}, "mathspeak": {"default": "modifying above upper Z with dot", "brief": "mod above upper Z with dot", "sbrief": "mod above upper Z with dot"}}}, {"category": "<PERSON>", "key": "017D", "mappings": {"default": {"default": "cap Z caron"}, "mathspeak": {"default": "modifying above upper Z with caron", "brief": "mod above upper Z with caron", "sbrief": "mod above upper Z with caron"}}}, {"category": "<PERSON>", "key": "01CD", "mappings": {"default": {"default": "cap A caron"}, "mathspeak": {"default": "modifying above upper A with caron", "brief": "mod above upper A with caron", "sbrief": "mod above upper A with caron"}}}, {"category": "<PERSON>", "key": "01CF", "mappings": {"default": {"default": "cap I caron"}, "mathspeak": {"default": "modifying above upper I with caron", "brief": "mod above upper I with caron", "sbrief": "mod above upper I with caron"}}}, {"category": "<PERSON>", "key": "01D1", "mappings": {"default": {"default": "cap O caron"}, "mathspeak": {"default": "modifying above upper O with caron", "brief": "mod above upper O with caron", "sbrief": "mod above upper O with caron"}}}, {"category": "<PERSON>", "key": "01D3", "mappings": {"default": {"default": "cap U caron"}, "mathspeak": {"default": "modifying above upper U with caron", "brief": "mod above upper U with caron", "sbrief": "mod above upper U with caron"}}}, {"category": "<PERSON>", "key": "01E6", "mappings": {"default": {"default": "cap G caron"}, "mathspeak": {"default": "modifying above upper G with caron", "brief": "mod above upper G with caron", "sbrief": "mod above upper G with caron"}}}, {"category": "<PERSON>", "key": "01E8", "mappings": {"default": {"default": "cap K caron"}, "mathspeak": {"default": "modifying above upper K with caron", "brief": "mod above upper K with caron", "sbrief": "mod above upper K with caron"}}}, {"category": "<PERSON>", "key": "01EA", "mappings": {"default": {"default": "cap O ogonek"}, "mathspeak": {"default": "modifying above upper O with ogonek", "brief": "mod above upper O with ogonek", "sbrief": "mod above upper O with ogonek"}}}, {"category": "<PERSON>", "key": "01F4", "mappings": {"default": {"default": "cap G acute"}, "mathspeak": {"default": "modifying above upper G with acute", "brief": "mod above upper G with acute", "sbrief": "mod above upper G with acute"}}}, {"category": "<PERSON>", "key": "01F8", "mappings": {"default": {"default": "cap N grave"}, "mathspeak": {"default": "modifying above upper N with grave", "brief": "mod above upper N with grave", "sbrief": "mod above upper N with grave"}}}, {"category": "<PERSON>", "key": "0200", "mappings": {"default": {"default": "cap A double grave"}, "mathspeak": {"default": "modifying above upper A with double grave", "brief": "mod above upper A with double grave", "sbrief": "mod above upper A with double grave"}}}, {"category": "<PERSON>", "key": "0202", "mappings": {"default": {"default": "cap A inverted breve"}, "mathspeak": {"default": "modifying above upper A with inverted breve", "brief": "mod above upper A with inverted breve", "sbrief": "mod above upper A with inverted breve"}}}, {"category": "<PERSON>", "key": "0204", "mappings": {"default": {"default": "cap E double grave"}, "mathspeak": {"default": "modifying above upper E with double grave", "brief": "mod above upper E with double grave", "sbrief": "mod above upper E with double grave"}}}, {"category": "<PERSON>", "key": "0206", "mappings": {"default": {"default": "cap E inverted breve"}, "mathspeak": {"default": "modifying above upper E with inverted breve", "brief": "mod above upper E with inverted breve", "sbrief": "mod above upper E with inverted breve"}}}, {"category": "<PERSON>", "key": "0208", "mappings": {"default": {"default": "cap I double grave"}, "mathspeak": {"default": "modifying above upper I with double grave", "brief": "mod above upper I with double grave", "sbrief": "mod above upper I with double grave"}}}, {"category": "<PERSON>", "key": "020A", "mappings": {"default": {"default": "cap I inverted breve"}, "mathspeak": {"default": "modifying above upper I with inverted breve", "brief": "mod above upper I with inverted breve", "sbrief": "mod above upper I with inverted breve"}}}, {"category": "<PERSON>", "key": "020C", "mappings": {"default": {"default": "cap O double grave"}, "mathspeak": {"default": "modifying above upper O with double grave", "brief": "mod above upper O with double grave", "sbrief": "mod above upper O with double grave"}}}, {"category": "<PERSON>", "key": "020E", "mappings": {"default": {"default": "cap O inverted breve"}, "mathspeak": {"default": "modifying above upper O with inverted breve", "brief": "mod above upper O with inverted breve", "sbrief": "mod above upper O with inverted breve"}}}, {"category": "<PERSON>", "key": "0210", "mappings": {"default": {"default": "cap R double grave"}, "mathspeak": {"default": "modifying above upper R with double grave", "brief": "mod above upper R with double grave", "sbrief": "mod above upper R with double grave"}}}, {"category": "<PERSON>", "key": "0212", "mappings": {"default": {"default": "cap R inverted breve"}, "mathspeak": {"default": "modifying above upper R with inverted breve", "brief": "mod above upper R with inverted breve", "sbrief": "mod above upper R with inverted breve"}}}, {"category": "<PERSON>", "key": "0214", "mappings": {"default": {"default": "cap U double grave"}, "mathspeak": {"default": "modifying above upper U with double grave", "brief": "mod above upper U with double grave", "sbrief": "mod above upper U with double grave"}}}, {"category": "<PERSON>", "key": "0216", "mappings": {"default": {"default": "cap U inverted breve"}, "mathspeak": {"default": "modifying above upper U with inverted breve", "brief": "mod above upper U with inverted breve", "sbrief": "mod above upper U with inverted breve"}}}, {"category": "<PERSON>", "key": "0218", "mappings": {"default": {"default": "cap S comma below"}, "mathspeak": {"default": "modifying below upper S with comma below", "brief": "mod below upper S with comma below", "sbrief": "mod below upper S with comma below"}}}, {"category": "<PERSON>", "key": "021A", "mappings": {"default": {"default": "cap T comma below"}, "mathspeak": {"default": "modifying below upper T with comma below", "brief": "mod below upper T with comma below", "sbrief": "mod below upper T with comma below"}}}, {"category": "<PERSON>", "key": "021E", "mappings": {"default": {"default": "cap H caron"}, "mathspeak": {"default": "modifying above upper H with caron", "brief": "mod above upper H with caron", "sbrief": "mod above upper H with caron"}}}, {"category": "<PERSON>", "key": "0226", "mappings": {"default": {"default": "cap A overdot"}, "mathspeak": {"default": "modifying above upper A with dot", "brief": "mod above upper A with dot", "sbrief": "mod above upper A with dot"}}}, {"category": "<PERSON>", "key": "0228", "mappings": {"default": {"default": "cap E cedilla"}, "mathspeak": {"default": "modifying above upper E with cedilla", "brief": "mod above upper E with cedilla", "sbrief": "mod above upper E with cedilla"}}}, {"category": "<PERSON>", "key": "022E", "mappings": {"default": {"default": "cap O overdot"}, "mathspeak": {"default": "modifying above upper O with dot", "brief": "mod above upper O with dot", "sbrief": "mod above upper O with dot"}}}, {"category": "<PERSON>", "key": "0232", "mappings": {"default": {"default": "cap Y overbar"}, "mathspeak": {"default": "upper Y overbar", "brief": "upper Y overbar", "sbrief": "upper Y overbar"}}}, {"category": "<PERSON>", "key": "1E00", "mappings": {"default": {"default": "cap A ring below"}, "mathspeak": {"default": "modifying below upper A with ring below", "brief": "mod below upper A with ring below", "sbrief": "mod below upper A with ring below"}}}, {"category": "<PERSON>", "key": "1E02", "mappings": {"default": {"default": "cap B overdot"}, "mathspeak": {"default": "modifying above upper B with dot", "brief": "mod above upper B with dot", "sbrief": "mod above upper B with dot"}}}, {"category": "<PERSON>", "key": "1E04", "mappings": {"default": {"default": "cap B underdot"}, "mathspeak": {"default": "modifying below upper B with dot", "brief": "mod below upper B with dot", "sbrief": "mod below upper B with dot"}}}, {"category": "<PERSON>", "key": "1E06", "mappings": {"default": {"default": "cap B underbar"}, "mathspeak": {"default": "upper B underbar", "brief": "upper B underbar", "sbrief": "upper B underbar"}}}, {"category": "<PERSON>", "key": "1E0A", "mappings": {"default": {"default": "cap D overdot"}, "mathspeak": {"default": "modifying above upper D with dot", "brief": "mod above upper D with dot", "sbrief": "mod above upper D with dot"}}}, {"category": "<PERSON>", "key": "1E0C", "mappings": {"default": {"default": "cap D underdot"}, "mathspeak": {"default": "modifying below upper D with dot", "brief": "mod below upper D with dot", "sbrief": "mod below upper D with dot"}}}, {"category": "<PERSON>", "key": "1E0E", "mappings": {"default": {"default": "cap D underbar"}, "mathspeak": {"default": "upper D underbar", "brief": "upper D underbar", "sbrief": "upper D underbar"}}}, {"category": "<PERSON>", "key": "1E10", "mappings": {"default": {"default": "cap D cedilla"}, "mathspeak": {"default": "modifying above upper D with cedilla", "brief": "mod above upper D with cedilla", "sbrief": "mod above upper D with cedilla"}}}, {"category": "<PERSON>", "key": "1E12", "mappings": {"default": {"default": "cap D underhat"}, "mathspeak": {"default": "modifying below upper D with caret", "brief": "mod below upper D with caret", "sbrief": "mod below upper D with caret"}}}, {"category": "<PERSON>", "key": "1E18", "mappings": {"default": {"default": "cap E underhat"}, "mathspeak": {"default": "modifying below upper E with caret", "brief": "mod below upper E with caret", "sbrief": "mod below upper E with caret"}}}, {"category": "<PERSON>", "key": "1E1A", "mappings": {"default": {"default": "cap E tilde below"}, "mathspeak": {"default": "upper E undertilde", "brief": "upper E undertilde", "sbrief": "upper E undertilde"}}}, {"category": "<PERSON>", "key": "1E1E", "mappings": {"default": {"default": "cap F overdot"}, "mathspeak": {"default": "modifying above upper F with dot", "brief": "mod above upper F with dot", "sbrief": "mod above upper F with dot"}}}, {"category": "<PERSON>", "key": "1E20", "mappings": {"default": {"default": "cap G overbar"}, "mathspeak": {"default": "upper G overbar", "brief": "upper G overbar", "sbrief": "upper G overbar"}}}, {"category": "<PERSON>", "key": "1E22", "mappings": {"default": {"default": "cap H overdot"}, "mathspeak": {"default": "modifying above upper H with dot", "brief": "mod above upper H with dot", "sbrief": "mod above upper H with dot"}}}, {"category": "<PERSON>", "key": "1E24", "mappings": {"default": {"default": "cap H underdot"}, "mathspeak": {"default": "modifying below upper H with dot", "brief": "mod below upper H with dot", "sbrief": "mod below upper H with dot"}}}, {"category": "<PERSON>", "key": "1E26", "mappings": {"default": {"default": "cap H double overdot"}, "mathspeak": {"default": "modifying above upper H with double dot", "brief": "mod above upper H with double dot", "sbrief": "mod above upper H with double dot"}}}, {"category": "<PERSON>", "key": "1E28", "mappings": {"default": {"default": "cap H cedilla"}, "mathspeak": {"default": "modifying above upper H with cedilla", "brief": "mod above upper H with cedilla", "sbrief": "mod above upper H with cedilla"}}}, {"category": "<PERSON>", "key": "1E2A", "mappings": {"default": {"default": "cap H breve below"}, "mathspeak": {"default": "modifying below upper H with breve below", "brief": "mod below upper H with breve below", "sbrief": "mod below upper H with breve below"}}}, {"category": "<PERSON>", "key": "1E2C", "mappings": {"default": {"default": "cap I tilde below"}, "mathspeak": {"default": "upper I undertilde", "brief": "upper I undertilde", "sbrief": "upper I undertilde"}}}, {"category": "<PERSON>", "key": "1E30", "mappings": {"default": {"default": "cap K acute"}, "mathspeak": {"default": "modifying above upper K with acute", "brief": "mod above upper K with acute", "sbrief": "mod above upper K with acute"}}}, {"category": "<PERSON>", "key": "1E32", "mappings": {"default": {"default": "cap K underdot"}, "mathspeak": {"default": "modifying below upper K with dot", "brief": "mod below upper K with dot", "sbrief": "mod below upper K with dot"}}}, {"category": "<PERSON>", "key": "1E34", "mappings": {"default": {"default": "cap K underbar"}, "mathspeak": {"default": "upper K underbar", "brief": "upper K underbar", "sbrief": "upper K underbar"}}}, {"category": "<PERSON>", "key": "1E36", "mappings": {"default": {"default": "cap L underdot"}, "mathspeak": {"default": "modifying below upper L with dot", "brief": "mod below upper L with dot", "sbrief": "mod below upper L with dot"}}}, {"category": "<PERSON>", "key": "1E3A", "mappings": {"default": {"default": "cap L underbar"}, "mathspeak": {"default": "upper L underbar", "brief": "upper L underbar", "sbrief": "upper L underbar"}}}, {"category": "<PERSON>", "key": "1E3C", "mappings": {"default": {"default": "cap L underhat"}, "mathspeak": {"default": "modifying below upper L with caret", "brief": "mod below upper L with caret", "sbrief": "mod below upper L with caret"}}}, {"category": "<PERSON>", "key": "1E3E", "mappings": {"default": {"default": "cap M acute"}, "mathspeak": {"default": "modifying above upper M with acute", "brief": "mod above upper M with acute", "sbrief": "mod above upper M with acute"}}}, {"category": "<PERSON>", "key": "1E40", "mappings": {"default": {"default": "cap M overdot"}, "mathspeak": {"default": "modifying above upper M with dot", "brief": "mod above upper M with dot", "sbrief": "mod above upper M with dot"}}}, {"category": "<PERSON>", "key": "1E42", "mappings": {"default": {"default": "cap M underdot"}, "mathspeak": {"default": "modifying below upper M with dot", "brief": "mod below upper M with dot", "sbrief": "mod below upper M with dot"}}}, {"category": "<PERSON>", "key": "1E44", "mappings": {"default": {"default": "cap N overdot"}, "mathspeak": {"default": "modifying above upper N with dot", "brief": "mod above upper N with dot", "sbrief": "mod above upper N with dot"}}}, {"category": "<PERSON>", "key": "1E46", "mappings": {"default": {"default": "cap N underdot"}, "mathspeak": {"default": "modifying below upper N with dot", "brief": "mod below upper N with dot", "sbrief": "mod below upper N with dot"}}}, {"category": "<PERSON>", "key": "1E48", "mappings": {"default": {"default": "cap N underbar"}, "mathspeak": {"default": "upper N underbar", "brief": "upper N underbar", "sbrief": "upper N underbar"}}}, {"category": "<PERSON>", "key": "1E4A", "mappings": {"default": {"default": "cap N underhat"}, "mathspeak": {"default": "modifying below upper N with caret", "brief": "mod below upper N with caret", "sbrief": "mod below upper N with caret"}}}, {"category": "<PERSON>", "key": "1E54", "mappings": {"default": {"default": "cap P acute"}, "mathspeak": {"default": "modifying above upper P with acute", "brief": "mod above upper P with acute", "sbrief": "mod above upper P with acute"}}}, {"category": "<PERSON>", "key": "1E56", "mappings": {"default": {"default": "cap P overdot"}, "mathspeak": {"default": "modifying above upper P with dot", "brief": "mod above upper P with dot", "sbrief": "mod above upper P with dot"}}}, {"category": "<PERSON>", "key": "1E58", "mappings": {"default": {"default": "cap R overdot"}, "mathspeak": {"default": "modifying above upper R with dot", "brief": "mod above upper R with dot", "sbrief": "mod above upper R with dot"}}}, {"category": "<PERSON>", "key": "1E5A", "mappings": {"default": {"default": "cap R underdot"}, "mathspeak": {"default": "modifying below upper R with dot", "brief": "mod below upper R with dot", "sbrief": "mod below upper R with dot"}}}, {"category": "<PERSON>", "key": "1E5E", "mappings": {"default": {"default": "cap R underbar"}, "mathspeak": {"default": "upper R underbar", "brief": "upper R underbar", "sbrief": "upper R underbar"}}}, {"category": "<PERSON>", "key": "1E60", "mappings": {"default": {"default": "cap S overdot"}, "mathspeak": {"default": "modifying above upper S with dot", "brief": "mod above upper S with dot", "sbrief": "mod above upper S with dot"}}}, {"category": "<PERSON>", "key": "1E62", "mappings": {"default": {"default": "cap S underdot"}, "mathspeak": {"default": "modifying below upper S with dot", "brief": "mod below upper S with dot", "sbrief": "mod below upper S with dot"}}}, {"category": "<PERSON>", "key": "1E6A", "mappings": {"default": {"default": "cap T overdot"}, "mathspeak": {"default": "modifying above upper T with dot", "brief": "mod above upper T with dot", "sbrief": "mod above upper T with dot"}}}, {"category": "<PERSON>", "key": "1E6C", "mappings": {"default": {"default": "cap T underdot"}, "mathspeak": {"default": "modifying below upper T with dot", "brief": "mod below upper T with dot", "sbrief": "mod below upper T with dot"}}}, {"category": "<PERSON>", "key": "1E6E", "mappings": {"default": {"default": "cap T underbar"}, "mathspeak": {"default": "upper T underbar", "brief": "upper T underbar", "sbrief": "upper T underbar"}}}, {"category": "<PERSON>", "key": "1E70", "mappings": {"default": {"default": "cap T underhat"}, "mathspeak": {"default": "modifying below upper T with caret", "brief": "mod below upper T with caret", "sbrief": "mod below upper T with caret"}}}, {"category": "<PERSON>", "key": "1E72", "mappings": {"default": {"default": "cap U double underdot"}, "mathspeak": {"default": "modifying below upper U with double dot", "brief": "mod below upper U with double dot", "sbrief": "mod below upper U with double dot"}}}, {"category": "<PERSON>", "key": "1E74", "mappings": {"default": {"default": "cap U tilde below"}, "mathspeak": {"default": "upper U undertilde", "brief": "upper U undertilde", "sbrief": "upper U undertilde"}}}, {"category": "<PERSON>", "key": "1E76", "mappings": {"default": {"default": "cap U underhat"}, "mathspeak": {"default": "modifying below upper U with caret", "brief": "mod below upper U with caret", "sbrief": "mod below upper U with caret"}}}, {"category": "<PERSON>", "key": "1E7C", "mappings": {"default": {"default": "cap V tilde"}, "mathspeak": {"default": "upper V overtilde", "brief": "upper V overtilde", "sbrief": "upper V overtilde"}}}, {"category": "<PERSON>", "key": "1E7E", "mappings": {"default": {"default": "cap V underdot"}, "mathspeak": {"default": "modifying below upper V with dot", "brief": "mod below upper V with dot", "sbrief": "mod below upper V with dot"}}}, {"category": "<PERSON>", "key": "1E80", "mappings": {"default": {"default": "cap W grave"}, "mathspeak": {"default": "modifying above upper W with grave", "brief": "mod above upper W with grave", "sbrief": "mod above upper W with grave"}}}, {"category": "<PERSON>", "key": "1E82", "mappings": {"default": {"default": "cap W acute"}, "mathspeak": {"default": "modifying above upper W with acute", "brief": "mod above upper W with acute", "sbrief": "mod above upper W with acute"}}}, {"category": "<PERSON>", "key": "1E84", "mappings": {"default": {"default": "cap W double overdot"}, "mathspeak": {"default": "modifying above upper W with double dot", "brief": "mod above upper W with double dot", "sbrief": "mod above upper W with double dot"}}}, {"category": "<PERSON>", "key": "1E86", "mappings": {"default": {"default": "cap W overdot"}, "mathspeak": {"default": "modifying above upper W with dot", "brief": "mod above upper W with dot", "sbrief": "mod above upper W with dot"}}}, {"category": "<PERSON>", "key": "1E88", "mappings": {"default": {"default": "cap W underdot"}, "mathspeak": {"default": "modifying below upper W with dot", "brief": "mod below upper W with dot", "sbrief": "mod below upper W with dot"}}}, {"category": "<PERSON>", "key": "1E8A", "mappings": {"default": {"default": "cap X overdot"}, "mathspeak": {"default": "modifying above upper X with dot", "brief": "mod above upper X with dot", "sbrief": "mod above upper X with dot"}}}, {"category": "<PERSON>", "key": "1E8C", "mappings": {"default": {"default": "cap X double overdot"}, "mathspeak": {"default": "modifying above upper X with double dot", "brief": "mod above upper X with double dot", "sbrief": "mod above upper X with double dot"}}}, {"category": "<PERSON>", "key": "1E8E", "mappings": {"default": {"default": "cap Y overdot"}, "mathspeak": {"default": "modifying above upper Y with dot", "brief": "mod above upper Y with dot", "sbrief": "mod above upper Y with dot"}}}, {"category": "<PERSON>", "key": "1E90", "mappings": {"default": {"default": "cap Z circumflex"}, "mathspeak": {"default": "modifying above upper Z with circumflex", "brief": "mod above upper Z with circumflex", "sbrief": "mod above upper Z with circumflex"}}}, {"category": "<PERSON>", "key": "1E92", "mappings": {"default": {"default": "cap Z underdot"}, "mathspeak": {"default": "modifying below upper Z with dot", "brief": "mod below upper Z with dot", "sbrief": "mod below upper Z with dot"}}}, {"category": "<PERSON>", "key": "1E94", "mappings": {"default": {"default": "cap Z underbar"}, "mathspeak": {"default": "upper Z underbar", "brief": "upper Z underbar", "sbrief": "upper Z underbar"}}}, {"category": "<PERSON>", "key": "1EA0", "mappings": {"default": {"default": "cap A underdot"}, "mathspeak": {"default": "modifying below upper A with dot", "brief": "mod below upper A with dot", "sbrief": "mod below upper A with dot"}}}, {"category": "<PERSON>", "key": "1EA2", "mappings": {"default": {"default": "cap A hook"}, "mathspeak": {"default": "modifying above upper A with hook", "brief": "mod above upper A with hook", "sbrief": "mod above upper A with hook"}}}, {"category": "<PERSON>", "key": "1EB8", "mappings": {"default": {"default": "cap E underdot"}, "mathspeak": {"default": "modifying below upper E with dot", "brief": "mod below upper E with dot", "sbrief": "mod below upper E with dot"}}}, {"category": "<PERSON>", "key": "1EBA", "mappings": {"default": {"default": "cap E hook"}, "mathspeak": {"default": "modifying above upper E with hook", "brief": "mod above upper E with hook", "sbrief": "mod above upper E with hook"}}}, {"category": "<PERSON>", "key": "1EBC", "mappings": {"default": {"default": "cap E tilde"}, "mathspeak": {"default": "upper E overtilde", "brief": "upper E overtilde", "sbrief": "upper E overtilde"}}}, {"category": "<PERSON>", "key": "1EC8", "mappings": {"default": {"default": "cap I hook"}, "mathspeak": {"default": "modifying above upper I with hook", "brief": "mod above upper I with hook", "sbrief": "mod above upper I with hook"}}}, {"category": "<PERSON>", "key": "1ECA", "mappings": {"default": {"default": "cap I underdot"}, "mathspeak": {"default": "modifying below upper I with dot", "brief": "mod below upper I with dot", "sbrief": "mod below upper I with dot"}}}, {"category": "<PERSON>", "key": "1ECC", "mappings": {"default": {"default": "cap O underdot"}, "mathspeak": {"default": "modifying below upper O with dot", "brief": "mod below upper O with dot", "sbrief": "mod below upper O with dot"}}}, {"category": "<PERSON>", "key": "1ECE", "mappings": {"default": {"default": "cap O hook"}, "mathspeak": {"default": "modifying above upper O with hook", "brief": "mod above upper O with hook", "sbrief": "mod above upper O with hook"}}}, {"category": "<PERSON>", "key": "1EE4", "mappings": {"default": {"default": "cap U underdot"}, "mathspeak": {"default": "modifying below upper U with dot", "brief": "mod below upper U with dot", "sbrief": "mod below upper U with dot"}}}, {"category": "<PERSON>", "key": "1EE6", "mappings": {"default": {"default": "cap U hook"}, "mathspeak": {"default": "modifying above upper U with hook", "brief": "mod above upper U with hook", "sbrief": "mod above upper U with hook"}}}, {"category": "<PERSON>", "key": "1EF2", "mappings": {"default": {"default": "cap Y grave"}, "mathspeak": {"default": "modifying above upper Y with grave", "brief": "mod above upper Y with grave", "sbrief": "mod above upper Y with grave"}}}, {"category": "<PERSON>", "key": "1EF4", "mappings": {"default": {"default": "cap Y underdot"}, "mathspeak": {"default": "modifying below upper Y with dot", "brief": "mod below upper Y with dot", "sbrief": "mod below upper Y with dot"}}}, {"category": "<PERSON>", "key": "1EF6", "mappings": {"default": {"default": "cap Y hook"}, "mathspeak": {"default": "modifying above upper Y with hook", "brief": "mod above upper Y with hook", "sbrief": "mod above upper Y with hook"}}}, {"category": "<PERSON>", "key": "1EF8", "mappings": {"default": {"default": "cap Y tilde"}, "mathspeak": {"default": "upper Y overtilde", "brief": "upper Y overtilde", "sbrief": "upper Y overtilde"}}}], "en/symbols/math_angles.js": [{"locale": "en"}, {"category": "Sm", "mappings": {"default": {"default": "right angle with arc"}}, "key": "22BE"}, {"category": "Sm", "mappings": {"default": {"default": "right angle with downwards zigzag arrow"}}, "key": "237C"}, {"category": "Sm", "mappings": {"default": {"default": "three dimensional angle"}}, "key": "27C0"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle opening left"}}, "key": "299B"}, {"category": "Sm", "mappings": {"default": {"default": "right angle variant with square"}}, "key": "299C"}, {"category": "Sm", "mappings": {"default": {"default": "measured right angle with dot"}}, "key": "299D"}, {"category": "Sm", "mappings": {"default": {"default": "angle with s inside"}}, "key": "299E"}, {"category": "Sm", "mappings": {"default": {"default": "acute angle"}}, "key": "299F"}, {"category": "Sm", "mappings": {"default": {"default": "spherical angle opening left"}}, "key": "29A0"}, {"category": "Sm", "mappings": {"default": {"default": "spherical angle opening up"}}, "key": "29A1"}, {"category": "Sm", "mappings": {"default": {"default": "turned angle"}}, "key": "29A2"}, {"category": "Sm", "mappings": {"default": {"default": "reversed angle"}}, "key": "29A3"}, {"category": "Sm", "mappings": {"default": {"default": "angle with underbar"}}, "key": "29A4"}, {"category": "Sm", "mappings": {"default": {"default": "reversed angle with underbar"}}, "key": "29A5"}, {"category": "Sm", "mappings": {"default": {"default": "oblique angle opening up"}}, "key": "29A6"}, {"category": "Sm", "mappings": {"default": {"default": "oblique angle opening down"}}, "key": "29A7"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and right"}}, "key": "29A8"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and left"}}, "key": "29A9"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and right"}}, "key": "29AA"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and left"}}, "key": "29AB"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and up"}}, "key": "29AC"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and up"}}, "key": "29AD"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and down"}}, "key": "29AE"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and down"}}, "key": "29AF"}], "en/symbols/math_arrows.js": [{"locale": "en"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow"}, "mathspeak": {"sbrief": "L arrow"}}, "key": "2190"}, {"category": "Sm", "mappings": {"default": {"default": "up arrow"}, "mathspeak": {"sbrief": "U arrow"}}, "key": "2191"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow"}, "mathspeak": {"sbrief": "R arrow"}}, "key": "2192"}, {"category": "Sm", "mappings": {"default": {"default": "down arrow"}, "mathspeak": {"sbrief": "D arrow"}}, "key": "2193"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow"}, "mathspeak": {"sbrief": "L R arrow"}}, "key": "2194"}, {"category": "So", "mappings": {"default": {"default": "up down arrow"}, "mathspeak": {"sbrief": "U D arrow"}}, "key": "2195"}, {"category": "So", "mappings": {"default": {"default": "up left arrow"}, "mathspeak": {"sbrief": "U L arrow"}}, "key": "2196"}, {"category": "So", "mappings": {"default": {"default": "up right arrow"}, "mathspeak": {"sbrief": "U R arrow"}}, "key": "2197"}, {"category": "So", "mappings": {"default": {"default": "down right arrow"}, "mathspeak": {"sbrief": "D R arrow"}}, "key": "2198"}, {"category": "So", "mappings": {"default": {"default": "down left arrow"}, "mathspeak": {"sbrief": "D L arrow"}}, "key": "2199"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with stroke"}, "mathspeak": {"sbrief": "L arrow with stroke"}}, "key": "219A"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with stroke"}, "mathspeak": {"sbrief": "R arrow with stroke"}}, "key": "219B"}, {"category": "So", "mappings": {"default": {"default": "left wave arrow"}, "mathspeak": {"sbrief": "L wave arrow"}}, "key": "219C"}, {"category": "So", "mappings": {"default": {"default": "right wave arrow"}, "mathspeak": {"sbrief": "R wave arrow"}}, "key": "219D"}, {"category": "So", "mappings": {"default": {"default": "two headed left arrow"}, "mathspeak": {"sbrief": "two headed L arrow"}}, "key": "219E"}, {"category": "So", "mappings": {"default": {"default": "two headed up arrow"}, "mathspeak": {"sbrief": "two headed U arrow"}}, "key": "219F"}, {"category": "Sm", "mappings": {"default": {"default": "two headed right arrow"}, "mathspeak": {"sbrief": "two headed R arrow"}}, "key": "21A0"}, {"category": "So", "mappings": {"default": {"default": "two headed down arrow"}, "mathspeak": {"sbrief": "two headed D arrow"}}, "key": "21A1"}, {"category": "So", "mappings": {"default": {"default": "left arrow with tail"}, "mathspeak": {"sbrief": "L arrow with tail"}}, "key": "21A2"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with tail"}, "mathspeak": {"sbrief": "R arrow with tail"}}, "key": "21A3"}, {"category": "So", "mappings": {"default": {"default": "left arrow from bar"}, "mathspeak": {"sbrief": "L arrow from bar"}}, "key": "21A4"}, {"category": "So", "mappings": {"default": {"default": "up arrow from bar"}, "mathspeak": {"sbrief": "U arrow from bar"}}, "key": "21A5"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow from bar"}, "mathspeak": {"sbrief": "R arrow from bar"}}, "key": "21A6"}, {"category": "So", "mappings": {"default": {"default": "down arrow from bar"}, "mathspeak": {"sbrief": "D arrow from bar"}}, "key": "21A7"}, {"category": "So", "mappings": {"default": {"default": "up down arrow with base"}, "mathspeak": {"sbrief": "U D arrow with base"}}, "key": "21A8"}, {"category": "So", "mappings": {"default": {"default": "left arrow with hook"}, "mathspeak": {"sbrief": "L arrow with hook"}}, "key": "21A9"}, {"category": "So", "mappings": {"default": {"default": "right arrow with hook"}, "mathspeak": {"sbrief": "R arrow with hook"}}, "key": "21AA"}, {"category": "So", "mappings": {"default": {"default": "left arrow with loop"}, "mathspeak": {"sbrief": "L arrow with loop"}}, "key": "21AB"}, {"category": "So", "mappings": {"default": {"default": "right arrow with loop"}, "mathspeak": {"sbrief": "R arrow with loop"}}, "key": "21AC"}, {"category": "So", "mappings": {"default": {"default": "left right wave arrow"}, "mathspeak": {"sbrief": "L R wave arrow"}}, "key": "21AD"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with stroke"}, "mathspeak": {"sbrief": "L R arrow with stroke"}}, "key": "21AE"}, {"category": "So", "mappings": {"default": {"default": "down zigzag arrow"}, "mathspeak": {"sbrief": "d zigzag arrow"}}, "key": "21AF"}, {"category": "So", "mappings": {"default": {"default": "up arrow with tip left"}, "mathspeak": {"sbrief": "U arrow with tip left"}}, "key": "21B0"}, {"category": "So", "mappings": {"default": {"default": "up arrow with tip right"}, "mathspeak": {"sbrief": "U arrow with tip right"}}, "key": "21B1"}, {"category": "So", "mappings": {"default": {"default": "down arrow with tip left"}, "mathspeak": {"sbrief": "D arrow with tip left"}}, "key": "21B2"}, {"category": "So", "mappings": {"default": {"default": "down arrow with tip right"}, "mathspeak": {"sbrief": "D arrow with tip right"}}, "key": "21B3"}, {"category": "So", "mappings": {"default": {"default": "right arrow with corner down"}, "mathspeak": {"sbrief": "R arrow with corner down"}}, "key": "21B4"}, {"category": "So", "mappings": {"default": {"default": "down arrow with corner left"}, "mathspeak": {"sbrief": "D arrow with corner left"}}, "key": "21B5"}, {"category": "So", "mappings": {"default": {"default": "anticlockwise top semicircle arrow"}}, "key": "21B6"}, {"category": "So", "mappings": {"default": {"default": "clockwise top semicircle arrow"}}, "key": "21B7"}, {"category": "So", "mappings": {"default": {"default": "up left arrow to long bar"}, "mathspeak": {"sbrief": "U L arrow to long bar"}}, "key": "21B8"}, {"category": "So", "mappings": {"default": {"default": "left arrow to bar over right arrow to bar"}, "mathspeak": {"sbrief": "L arrow to bar over R arrow to bar"}}, "key": "21B9"}, {"category": "So", "mappings": {"default": {"default": "anticlockwise open circle arrow"}}, "key": "21BA"}, {"category": "So", "mappings": {"default": {"default": "clockwise open circle arrow"}}, "key": "21BB"}, {"category": "So", "mappings": {"default": {"default": "right arrow over left arrow"}, "mathspeak": {"sbrief": "R arrow over L arrow"}}, "key": "21C4"}, {"category": "So", "mappings": {"default": {"default": "up arrow left of down arrow"}, "mathspeak": {"sbrief": "U arrow L of D arrow"}}, "key": "21C5"}, {"category": "So", "mappings": {"default": {"default": "left arrow over right arrow"}, "mathspeak": {"sbrief": "L arrow over R arrow"}}, "key": "21C6"}, {"category": "So", "mappings": {"default": {"default": "left paired arrows"}, "mathspeak": {"sbrief": "L paired arrows"}}, "key": "21C7"}, {"category": "So", "mappings": {"default": {"default": "up paired arrows"}, "mathspeak": {"sbrief": "U paired arrows"}}, "key": "21C8"}, {"category": "So", "mappings": {"default": {"default": "right paired arrows"}, "mathspeak": {"sbrief": "R paired arrows"}}, "key": "21C9"}, {"category": "So", "mappings": {"default": {"default": "down paired arrows"}, "mathspeak": {"sbrief": "D paired arrows"}}, "key": "21CA"}, {"category": "So", "mappings": {"default": {"default": "left double arrow with stroke"}, "mathspeak": {"sbrief": "L double arrow with stroke"}}, "key": "21CD"}, {"category": "Sm", "mappings": {"default": {"default": "left right double arrow with stroke"}, "mathspeak": {"sbrief": "L R double arrow with stroke"}}, "key": "21CE"}, {"category": "Sm", "mappings": {"default": {"default": "right double arrow with stroke"}, "mathspeak": {"sbrief": "R double arrow with stroke"}}, "key": "21CF"}, {"category": "So", "mappings": {"default": {"default": "left double arrow"}, "mathspeak": {"sbrief": "L double arrow"}}, "key": "21D0"}, {"category": "So", "mappings": {"default": {"default": "up double arrow"}, "mathspeak": {"sbrief": "U double arrow"}}, "key": "21D1"}, {"category": "Sm", "mappings": {"default": {"default": "right double arrow"}, "mathspeak": {"sbrief": "R double arrow"}}, "key": "21D2"}, {"category": "So", "mappings": {"default": {"default": "down double arrow"}, "mathspeak": {"sbrief": "d double arrow"}}, "key": "21D3"}, {"category": "Sm", "mappings": {"default": {"default": "left right double arrow"}, "mathspeak": {"sbrief": "L R double arrow"}}, "key": "21D4"}, {"category": "So", "mappings": {"default": {"default": "up down double arrow"}, "mathspeak": {"sbrief": "U d double arrow"}}, "key": "21D5"}, {"category": "So", "mappings": {"default": {"default": "up left double arrow"}, "mathspeak": {"sbrief": "U L double arrow"}}, "key": "21D6"}, {"category": "So", "mappings": {"default": {"default": "up right double arrow"}, "mathspeak": {"sbrief": "U R double arrow"}}, "key": "21D7"}, {"category": "So", "mappings": {"default": {"default": "down right double arrow"}, "mathspeak": {"sbrief": "d R double arrow"}}, "key": "21D8"}, {"category": "So", "mappings": {"default": {"default": "down left double arrow"}, "mathspeak": {"sbrief": "d L double arrow"}}, "key": "21D9"}, {"category": "So", "mappings": {"default": {"default": "left triple arrow"}, "mathspeak": {"sbrief": "L triple arrow"}}, "key": "21DA"}, {"category": "So", "mappings": {"default": {"default": "right triple arrow"}, "mathspeak": {"sbrief": "r triple arrow"}}, "key": "21DB"}, {"category": "So", "mappings": {"default": {"default": "left squiggle arrow"}, "mathspeak": {"sbrief": "L squiggle arrow"}}, "key": "21DC"}, {"category": "So", "mappings": {"default": {"default": "right squiggle arrow"}, "mathspeak": {"sbrief": "r squiggle arrow"}}, "key": "21DD"}, {"category": "So", "mappings": {"default": {"default": "up arrow with double stroke"}, "mathspeak": {"sbrief": "U arrow with double stroke"}}, "key": "21DE"}, {"category": "So", "mappings": {"default": {"default": "down arrow with double stroke"}, "mathspeak": {"sbrief": "D arrow with double stroke"}}, "key": "21DF"}, {"category": "So", "mappings": {"default": {"default": "left dasheD arrow"}, "mathspeak": {"sbrief": "L dasheD arrow"}}, "key": "21E0"}, {"category": "So", "mappings": {"default": {"default": "up dasheD arrow"}, "mathspeak": {"sbrief": "U dasheD arrow"}}, "key": "21E1"}, {"category": "So", "mappings": {"default": {"default": "right dasheD arrow"}, "mathspeak": {"sbrief": "r dasheD arrow"}}, "key": "21E2"}, {"category": "So", "mappings": {"default": {"default": "down dasheD arrow"}, "mathspeak": {"sbrief": "d dasheD arrow"}}, "key": "21E3"}, {"category": "So", "mappings": {"default": {"default": "left arrow to bar"}, "mathspeak": {"sbrief": "L arrow to bar"}}, "key": "21E4"}, {"category": "So", "mappings": {"default": {"default": "right arrow to bar"}, "mathspeak": {"sbrief": "R arrow to bar"}}, "key": "21E5"}, {"category": "So", "mappings": {"default": {"default": "white left arrow"}, "mathspeak": {"sbrief": "white L arrow"}}, "key": "21E6"}, {"category": "So", "mappings": {"default": {"default": "white up arrow"}, "mathspeak": {"sbrief": "white U arrow"}}, "key": "21E7"}, {"category": "So", "mappings": {"default": {"default": "white right arrow"}, "mathspeak": {"sbrief": "white R arrow"}}, "key": "21E8"}, {"category": "So", "mappings": {"default": {"default": "white down arrow"}, "mathspeak": {"sbrief": "white D arrow"}}, "key": "21E9"}, {"category": "So", "mappings": {"default": {"default": "white up arrow from bar"}, "mathspeak": {"sbrief": "white U arrow from bar"}}, "key": "21EA"}, {"category": "So", "mappings": {"default": {"default": "white up arrow on pedestal"}, "mathspeak": {"sbrief": "white U arrow on pedestal"}}, "key": "21EB"}, {"category": "So", "mappings": {"default": {"default": "white up arrow on pedestal with horizontal bar"}, "mathspeak": {"sbrief": "white U arrow on pedestal with horizontal bar"}}, "key": "21EC"}, {"category": "So", "mappings": {"default": {"default": "white up arrow on pedestal with vertical bar"}, "mathspeak": {"sbrief": "white U arrow on pedestal with vertical bar"}}, "key": "21ED"}, {"category": "So", "mappings": {"default": {"default": "white double up arrow"}, "mathspeak": {"sbrief": "white double U arrow"}}, "key": "21EE"}, {"category": "So", "mappings": {"default": {"default": "white double up arrow on pedestal"}, "mathspeak": {"sbrief": "white double U arrow on pedestal"}}, "key": "21EF"}, {"category": "So", "mappings": {"default": {"default": "white right arrow from wall"}, "mathspeak": {"sbrief": "white R arrow from wall"}}, "key": "21F0"}, {"category": "So", "mappings": {"default": {"default": "north west arrow to corner"}}, "key": "21F1"}, {"category": "So", "mappings": {"default": {"default": "south east arrow to corner"}}, "key": "21F2"}, {"category": "So", "mappings": {"default": {"default": "up down white arrow"}, "mathspeak": {"sbrief": "U d white arrow"}}, "key": "21F3"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with small circle"}, "mathspeak": {"sbrief": "R arrow with small circle"}}, "key": "21F4"}, {"category": "Sm", "mappings": {"default": {"default": "down arrow left of up arrow"}, "mathspeak": {"sbrief": "D arrow l of U arrow"}}, "key": "21F5"}, {"category": "Sm", "mappings": {"default": {"default": "three right arrows"}, "mathspeak": {"sbrief": "three R arrows"}}, "key": "21F6"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with vertical stroke"}, "mathspeak": {"sbrief": "L arrow with vertical stroke"}}, "key": "21F7"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with vertical stroke"}, "mathspeak": {"sbrief": "R arrow with vertical stroke"}}, "key": "21F8"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with vertical stroke"}, "mathspeak": {"sbrief": "L R arrow with vertical stroke"}}, "key": "21F9"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with double vertical stroke"}, "mathspeak": {"sbrief": "L arrow with double vertical stroke"}}, "key": "21FA"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "R arrow with double vertical stroke"}}, "key": "21FB"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "L R arrow with double vertical stroke"}}, "key": "21FC"}, {"category": "Sm", "mappings": {"default": {"default": "left open headed arrow"}, "mathspeak": {"sbrief": "l open headed arrow"}}, "key": "21FD"}, {"category": "Sm", "mappings": {"default": {"default": "right open headed arrow"}, "mathspeak": {"sbrief": "r open headed arrow"}}, "key": "21FE"}, {"category": "Sm", "mappings": {"default": {"default": "left right open headed arrow"}, "mathspeak": {"sbrief": "L R open headed arrow"}}, "key": "21FF"}, {"category": "So", "mappings": {"default": {"default": "electric arrow"}}, "key": "2301"}, {"category": "So", "mappings": {"default": {"default": "up arrowhead"}, "mathspeak": {"sbrief": "U arrowhead"}}, "key": "2303"}, {"category": "So", "mappings": {"default": {"default": "down arrowhead"}, "mathspeak": {"sbrief": "D arrowhead"}}, "key": "2304"}, {"category": "So", "mappings": {"default": {"default": "up arrowhead between two horizontal bars", "alternative": "enter key"}, "mathspeak": {"sbrief": "U arrowhead between two horizontal bars"}}, "key": "2324"}, {"category": "So", "mappings": {"default": {"default": "broken circle with northwest arrow"}}, "key": "238B"}, {"category": "So", "mappings": {"default": {"default": "heavy wide headed right arrow"}, "mathspeak": {"sbrief": "heavy wide headed R arrow"}}, "key": "2794"}, {"category": "So", "mappings": {"default": {"default": "heavy down right arrow"}, "mathspeak": {"sbrief": "heavy d R arrow"}}, "key": "2798"}, {"category": "So", "mappings": {"default": {"default": "heavy right arrow"}, "mathspeak": {"sbrief": "heavy R arrow"}}, "key": "2799"}, {"category": "So", "mappings": {"default": {"default": "heavy up right arrow"}, "mathspeak": {"sbrief": "heavy U R arrow"}}, "key": "279A"}, {"category": "So", "mappings": {"default": {"default": "drafting point right arrow"}, "mathspeak": {"sbrief": "drafting point R arrow"}}, "key": "279B"}, {"category": "So", "mappings": {"default": {"default": "heavy round tipped right arrow"}, "mathspeak": {"sbrief": "heavy round tipped R arrow"}}, "key": "279C"}, {"category": "So", "mappings": {"default": {"default": "triangle headed right arrow"}, "mathspeak": {"sbrief": "triangle headed R arrow"}}, "key": "279D"}, {"category": "So", "mappings": {"default": {"default": "heavy triangle headed right arrow"}, "mathspeak": {"sbrief": "heavy triangle headed R arrow"}}, "key": "279E"}, {"category": "So", "mappings": {"default": {"default": "dashed triangle headed right arrow"}, "mathspeak": {"sbrief": "dashed triangle headed R arrow"}}, "key": "279F"}, {"category": "So", "mappings": {"default": {"default": "heavy dashed triangle headed right arrow"}, "mathspeak": {"sbrief": "heavy dashed triangle headed R arrow"}}, "key": "27A0"}, {"category": "So", "mappings": {"default": {"default": "black right arrow"}, "mathspeak": {"sbrief": "black R arrow"}}, "key": "27A1"}, {"category": "So", "mappings": {"default": {"default": "three d top lighted right arrowhead"}, "mathspeak": {"sbrief": "three d top lighted R arrowhead"}}, "key": "27A2"}, {"category": "So", "mappings": {"default": {"default": "three d bottom lighted right arrowhead"}, "mathspeak": {"sbrief": "three d bottom lighted R arrowhead"}}, "key": "27A3"}, {"category": "So", "mappings": {"default": {"default": "black right arrowhead"}, "mathspeak": {"sbrief": "black R arrowhead"}}, "key": "27A4"}, {"category": "So", "mappings": {"default": {"default": "heavy black curved down and right arrow"}, "mathspeak": {"sbrief": "heavy black curved d and R arrow"}}, "key": "27A5"}, {"category": "So", "mappings": {"default": {"default": "heavy black curved up and right arrow"}, "mathspeak": {"sbrief": "heavy black curved U and R arrow"}}, "key": "27A6"}, {"category": "So", "mappings": {"default": {"default": "squat black right arrow"}, "mathspeak": {"sbrief": "squat black R arrow"}}, "key": "27A7"}, {"category": "So", "mappings": {"default": {"default": "heavy concave pointed black right arrow"}, "mathspeak": {"sbrief": "heavy concave pointed black R arrow"}}, "key": "27A8"}, {"category": "So", "mappings": {"default": {"default": "right shaded white right arrow"}, "mathspeak": {"sbrief": "right shaded white R arrow"}}, "key": "27A9"}, {"category": "So", "mappings": {"default": {"default": "left shaded white right arrow"}, "mathspeak": {"sbrief": "left shaded white R arrow"}}, "key": "27AA"}, {"category": "So", "mappings": {"default": {"default": "back tilted shadowed white right arrow"}, "mathspeak": {"sbrief": "back tilted shadowed white R arrow"}}, "key": "27AB"}, {"category": "So", "mappings": {"default": {"default": "front tilted shadowed white right arrow"}, "mathspeak": {"sbrief": "front tilted shadowed white R arrow"}}, "key": "27AC"}, {"category": "So", "mappings": {"default": {"default": "heavy lower right shadowed white right arrow"}, "mathspeak": {"sbrief": "heavy lower right shadowed white R arrow"}}, "key": "27AD"}, {"category": "So", "mappings": {"default": {"default": "heavy upper right shadowed white right arrow"}, "mathspeak": {"sbrief": "heavy upper right shadowed white R arrow"}}, "key": "27AE"}, {"category": "So", "mappings": {"default": {"default": "notched lower right shadowed white right arrow"}, "mathspeak": {"sbrief": "notched lower right shadowed white R arrow"}}, "key": "27AF"}, {"category": "So", "mappings": {"default": {"default": "notched upper right shadowed white right arrow"}, "mathspeak": {"sbrief": "notched upper right shadowed white R arrow"}}, "key": "27B1"}, {"category": "So", "mappings": {"default": {"default": "circled heavy white right arrow"}, "mathspeak": {"sbrief": "circled heavy white R arrow"}}, "key": "27B2"}, {"category": "So", "mappings": {"default": {"default": "white feathered right arrow"}, "mathspeak": {"sbrief": "white feathered R arrow"}}, "key": "27B3"}, {"category": "So", "mappings": {"default": {"default": "black feathered down right arrow"}, "mathspeak": {"sbrief": "black feathered d R arrow"}}, "key": "27B4"}, {"category": "So", "mappings": {"default": {"default": "black feathered right arrow"}, "mathspeak": {"sbrief": "black feathered R arrow"}}, "key": "27B5"}, {"category": "So", "mappings": {"default": {"default": "black feathered up right arrow"}, "mathspeak": {"sbrief": "black feathered U R arrow"}}, "key": "27B6"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered down right arrow"}, "mathspeak": {"sbrief": "heavy black feathered d R arrow"}}, "key": "27B7"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered right arrow"}, "mathspeak": {"sbrief": "heavy black feathered R arrow"}}, "key": "27B8"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered up right arrow"}, "mathspeak": {"sbrief": "heavy black feathered U R arrow"}}, "key": "27B9"}, {"category": "So", "mappings": {"default": {"default": "teardrop barbed right arrow"}, "mathspeak": {"sbrief": "teardrop barbed R arrow"}}, "key": "27BA"}, {"category": "So", "mappings": {"default": {"default": "heavy teardrop shanked right arrow"}, "mathspeak": {"sbrief": "heavy teardrop shanked R arrow"}}, "key": "27BB"}, {"category": "So", "mappings": {"default": {"default": "wedge tailed right arrow"}, "mathspeak": {"sbrief": "wedge tailed R arrow"}}, "key": "27BC"}, {"category": "So", "mappings": {"default": {"default": "heavy wedge tailed right arrow"}, "mathspeak": {"sbrief": "heavy wedge tailed R arrow"}}, "key": "27BD"}, {"category": "So", "mappings": {"default": {"default": "open outlined right arrow"}, "mathspeak": {"sbrief": "open outlined R arrow"}}, "key": "27BE"}, {"category": "Sm", "mappings": {"default": {"default": "up quadruple arrow"}, "mathspeak": {"sbrief": "U quadruple arrow"}}, "key": "27F0"}, {"category": "Sm", "mappings": {"default": {"default": "down quadruple arrow"}, "mathspeak": {"sbrief": "d quadrule arrow"}}, "key": "27F1"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise gapped circle arrow"}}, "key": "27F2"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise gapped circle arrow"}}, "key": "27F3"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with circled plus"}, "mathspeak": {"sbrief": "R arrow with circled plus"}}, "key": "27F4"}, {"category": "Sm", "mappings": {"default": {"default": "long left arrow"}, "mathspeak": {"sbrief": "long L arrow"}}, "key": "27F5"}, {"category": "Sm", "mappings": {"default": {"default": "long right arrow"}, "mathspeak": {"sbrief": "long R arrow"}}, "key": "27F6"}, {"category": "Sm", "mappings": {"default": {"default": "long left right arrow"}, "mathspeak": {"sbrief": "long L R arrow"}}, "key": "27F7"}, {"category": "Sm", "mappings": {"default": {"default": "long left double arrow"}, "mathspeak": {"sbrief": "long l double arrow"}}, "key": "27F8"}, {"category": "Sm", "mappings": {"default": {"default": "long right double arrow"}, "mathspeak": {"sbrief": "long R double arrow"}}, "key": "27F9"}, {"category": "Sm", "mappings": {"default": {"default": "long left right double arrow"}, "mathspeak": {"sbrief": "long L R double arrow"}}, "key": "27FA"}, {"category": "Sm", "mappings": {"default": {"default": "long left arrow from bar"}, "mathspeak": {"sbrief": "long L arrow from bar"}}, "key": "27FB"}, {"category": "Sm", "mappings": {"default": {"default": "long right arrow from bar"}, "mathspeak": {"sbrief": "long R arrow from bar"}}, "key": "27FC"}, {"category": "Sm", "mappings": {"default": {"default": "long left double arrow from bar"}, "mathspeak": {"sbrief": "long l double arrow from bar"}}, "key": "27FD"}, {"category": "Sm", "mappings": {"default": {"default": "long right double arrow from bar"}, "mathspeak": {"sbrief": "long R double arrow from bar"}}, "key": "27FE"}, {"category": "Sm", "mappings": {"default": {"default": "long right squiggle arrow"}, "mathspeak": {"sbrief": "long r squiggle arrow"}}, "key": "27FF"}, {"category": "Sm", "mappings": {"default": {"default": "two headed right arrow with vertical stroke"}, "mathspeak": {"sbrief": "two headed R arrow with vertical stroke"}}, "key": "2900"}, {"category": "Sm", "mappings": {"default": {"default": "two headed right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "two headed R arrow with double vertical stroke"}}, "key": "2901"}, {"category": "Sm", "mappings": {"default": {"default": "double left arrow with vertical stroke"}, "mathspeak": {"sbrief": "double L arrow with vertical stroke"}}, "key": "2902"}, {"category": "Sm", "mappings": {"default": {"default": "double right arrow with vertical stroke"}, "mathspeak": {"sbrief": "double R arrow with vertical stroke"}}, "key": "2903"}, {"category": "Sm", "mappings": {"default": {"default": "double left right arrow with vertical stroke"}, "mathspeak": {"sbrief": "double L R arrow with vertical stroke"}}, "key": "2904"}, {"category": "Sm", "mappings": {"default": {"default": "two headed right arrow from bar"}, "mathspeak": {"sbrief": "two headed R arrow from bar"}}, "key": "2905"}, {"category": "Sm", "mappings": {"default": {"default": "double left arrow from bar"}, "mathspeak": {"sbrief": "double L arrow from bar"}}, "key": "2906"}, {"category": "Sm", "mappings": {"default": {"default": "double right arrow from bar"}, "mathspeak": {"sbrief": "double R arrow from bar"}}, "key": "2907"}, {"category": "Sm", "mappings": {"default": {"default": "arrow down with horizontal stroke"}}, "key": "2908"}, {"category": "Sm", "mappings": {"default": {"default": "up arrow with horizontal stroke"}, "mathspeak": {"sbrief": "U arrow with horizontal stroke"}}, "key": "2909"}, {"category": "Sm", "mappings": {"default": {"default": "up triple arrow"}, "mathspeak": {"sbrief": "U triple arrow"}}, "key": "290A"}, {"category": "Sm", "mappings": {"default": {"default": "down triple arrow"}, "mathspeak": {"sbrief": "d triple arrow"}}, "key": "290B"}, {"category": "Sm", "mappings": {"default": {"default": "left double dash arrow"}, "mathspeak": {"sbrief": "l double dash arrow"}}, "key": "290C"}, {"category": "Sm", "mappings": {"default": {"default": "right double dash arrow"}, "mathspeak": {"sbrief": "R double dash arrow"}}, "key": "290D"}, {"category": "Sm", "mappings": {"default": {"default": "left triple dash arrow"}, "mathspeak": {"sbrief": "l triple dash arrow"}}, "key": "290E"}, {"category": "Sm", "mappings": {"default": {"default": "right triple dash arrow"}, "mathspeak": {"sbrief": "r triple dash arrow"}}, "key": "290F"}, {"category": "Sm", "mappings": {"default": {"default": "right two headed triple dash arrow"}, "mathspeak": {"sbrief": "r two headed triple dash arrow"}}, "key": "2910"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with dotted stem"}, "mathspeak": {"sbrief": "R arrow with dotted stem"}}, "key": "2911"}, {"category": "Sm", "mappings": {"default": {"default": "up arrow to bar"}, "mathspeak": {"sbrief": "U arrow to bar"}}, "key": "2912"}, {"category": "Sm", "mappings": {"default": {"default": "down arrow to bar"}, "mathspeak": {"sbrief": "D arrow to bar"}}, "key": "2913"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "R arrow with tail with vertical stroke"}}, "key": "2914"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "R arrow with tail with double vertical stroke"}}, "key": "2915"}, {"category": "Sm", "mappings": {"default": {"default": "right two headed arrow with tail"}, "mathspeak": {"sbrief": "r two headed arrow with tail"}}, "key": "2916"}, {"category": "Sm", "mappings": {"default": {"default": "right two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "r two headed arrow with tail with vertical stroke"}}, "key": "2917"}, {"category": "Sm", "mappings": {"default": {"default": "right two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "r two headed arrow with tail with double vertical stroke"}}, "key": "2918"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow tail"}, "mathspeak": {"sbrief": "L arrow tail"}}, "key": "2919"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow tail"}, "mathspeak": {"sbrief": "R arrow tail"}}, "key": "291A"}, {"category": "Sm", "mappings": {"default": {"default": "left double arrow tail"}, "mathspeak": {"sbrief": "l double arrow tail"}}, "key": "291B"}, {"category": "Sm", "mappings": {"default": {"default": "right double arrow tail"}, "mathspeak": {"sbrief": "R double arrow tail"}}, "key": "291C"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow to black diamond"}, "mathspeak": {"sbrief": "L arrow to black diamond"}}, "key": "291D"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow to black diamond"}, "mathspeak": {"sbrief": "R arrow to black diamond"}}, "key": "291E"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow from bar to black diamond"}, "mathspeak": {"sbrief": "L arrow from bar to black diamond"}}, "key": "291F"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow from bar to black diamond"}, "mathspeak": {"sbrief": "R arrow from bar to black diamond"}}, "key": "2920"}, {"category": "Sm", "mappings": {"default": {"default": "north west and south east arrow"}}, "key": "2921"}, {"category": "Sm", "mappings": {"default": {"default": "north east and south west arrow"}}, "key": "2922"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow with hook"}}, "key": "2923"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow with hook"}}, "key": "2924"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow with hook"}}, "key": "2925"}, {"category": "Sm", "mappings": {"default": {"default": "south west arrow with hook"}}, "key": "2926"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow and north east arrow"}}, "key": "2927"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow and south east arrow"}}, "key": "2928"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow and south west arrow"}}, "key": "2929"}, {"category": "Sm", "mappings": {"default": {"default": "south west arrow and north west arrow"}}, "key": "292A"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow crossing north east arrow"}}, "key": "292D"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow crossing south east arrow"}}, "key": "292E"}, {"category": "Sm", "mappings": {"default": {"default": "falling diagonal crossing north east arrow"}}, "key": "292F"}, {"category": "Sm", "mappings": {"default": {"default": "rising diagonal crossing south east arrow"}}, "key": "2930"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow crossing north west arrow"}}, "key": "2931"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow crossing north east arrow"}}, "key": "2932"}, {"category": "Sm", "mappings": {"default": {"default": "wave arrow pointing directly right"}}, "key": "2933"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing right then curving up"}}, "key": "2934"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing right then curving down"}}, "key": "2935"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing down then curving left"}}, "key": "2936"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing down then curving right"}}, "key": "2937"}, {"category": "Sm", "mappings": {"default": {"default": "right side arc clockwise arrow"}, "mathspeak": {"sbrief": "r side arc clockwise arrow"}}, "key": "2938"}, {"category": "Sm", "mappings": {"default": {"default": "left side arc anticlockwise arrow"}, "mathspeak": {"sbrief": "l side arc anticlockwise arrow"}}, "key": "2939"}, {"category": "Sm", "mappings": {"default": {"default": "top arc anticlockwise arrow"}}, "key": "293A"}, {"category": "Sm", "mappings": {"default": {"default": "bottom arc anticlockwise arrow"}}, "key": "293B"}, {"category": "Sm", "mappings": {"default": {"default": "top arc clockwise arrow with minus"}}, "key": "293C"}, {"category": "Sm", "mappings": {"default": {"default": "top arc anticlockwise arrow with plus"}}, "key": "293D"}, {"category": "Sm", "mappings": {"default": {"default": "down right semicircular clockwise arrow"}, "mathspeak": {"sbrief": "d r semicircular clockwise arrow"}}, "key": "293E"}, {"category": "Sm", "mappings": {"default": {"default": "down left semicircular anticlockwise arrow"}, "mathspeak": {"sbrief": "d l semicircular anticlockwise arrow"}}, "key": "293F"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise closed circle arrow"}}, "key": "2940"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise closed circle arrow"}}, "key": "2941"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow above short left arrow"}, "mathspeak": {"sbrief": "R arrow above short L arrow"}}, "key": "2942"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow above short right arrow"}, "mathspeak": {"sbrief": "L arrow above short R arrow"}}, "key": "2943"}, {"category": "Sm", "mappings": {"default": {"default": "short right arrow above left arrow"}, "mathspeak": {"sbrief": "short R arrow above L arrow"}}, "key": "2944"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with plus below"}, "mathspeak": {"sbrief": "R arrow with plus below"}}, "key": "2945"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with plus below"}, "mathspeak": {"sbrief": "L arrow with plus below"}}, "key": "2946"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow through x"}, "mathspeak": {"sbrief": "R arrow through x"}}, "key": "2947"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow through small circle"}, "mathspeak": {"sbrief": "L R arrow through small circle"}}, "key": "2948"}, {"category": "Sm", "mappings": {"default": {"default": "up two headed arrow from small circle"}, "mathspeak": {"sbrief": "U two headed arrow from small circle"}}, "key": "2949"}, {"category": "Sm", "mappings": {"default": {"default": "right double arrow with rounded head"}, "mathspeak": {"sbrief": "R double arrow with rounded head"}}, "key": "2970"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above right arrow"}, "mathspeak": {"sbrief": "equals sign above R arrow"}}, "key": "2971"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator above right arrow"}, "mathspeak": {"sbrief": "tilde operator above R arrow"}}, "key": "2972"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow above tilde operator"}, "mathspeak": {"sbrief": "L arrow above tilde operator"}}, "key": "2973"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow above tilde operator"}, "mathspeak": {"sbrief": "R arrow above tilde operator"}}, "key": "2974"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow above almost equals"}, "mathspeak": {"sbrief": "R arrow above almost equals"}}, "key": "2975"}, {"category": "Sm", "mappings": {"default": {"default": "less than above left arrow"}, "mathspeak": {"sbrief": "less than above L arrow"}}, "key": "2976"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow through less than"}, "mathspeak": {"sbrief": "L arrow through less than"}}, "key": "2977"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above right arrow"}, "mathspeak": {"sbrief": "greater than above R arrow"}}, "key": "2978"}, {"category": "Sm", "mappings": {"default": {"default": "subset above right arrow"}, "mathspeak": {"sbrief": "subset above R arrow"}}, "key": "2979"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow through subset"}, "mathspeak": {"sbrief": "L arrow through subset"}}, "key": "297A"}, {"category": "Sm", "mappings": {"default": {"default": "superset above left arrow"}, "mathspeak": {"sbrief": "suerset above L arrow"}}, "key": "297B"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with right arrow above"}, "mathspeak": {"sbrief": "empty set with R arrow above"}}, "key": "29B3"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with left arrow above"}, "mathspeak": {"sbrief": "empty set with L arrow above"}}, "key": "29B4"}, {"category": "Sm", "mappings": {"default": {"default": "up arrow through circle"}, "mathspeak": {"sbrief": "U arrow through circle"}}, "key": "29BD"}, {"category": "Sm", "mappings": {"default": {"default": "black diamond with down arrow"}, "mathspeak": {"sbrief": "black diamond with D arrow"}}, "key": "29EA"}, {"category": "Sm", "mappings": {"default": {"default": "white circle with down arrow"}, "mathspeak": {"sbrief": "white circle with D arrow"}}, "key": "29EC"}, {"category": "Sm", "mappings": {"default": {"default": "black circle with down arrow"}, "mathspeak": {"sbrief": "black circle with D arrow"}}, "key": "29ED"}, {"category": "Sm", "mappings": {"default": {"default": "integral with left arrow with hook"}, "mathspeak": {"sbrief": "integral with L arrow with hook"}}, "key": "2A17"}, {"category": "So", "mappings": {"default": {"default": "north east white arrow"}}, "key": "2B00"}, {"category": "So", "mappings": {"default": {"default": "north west white arrow"}}, "key": "2B01"}, {"category": "So", "mappings": {"default": {"default": "south east white arrow"}}, "key": "2B02"}, {"category": "So", "mappings": {"default": {"default": "south west white arrow"}}, "key": "2B03"}, {"category": "So", "mappings": {"default": {"default": "left right white arrow"}, "mathspeak": {"sbrief": "L R white arrow"}}, "key": "2B04"}, {"category": "So", "mappings": {"default": {"default": "left black arrow"}, "mathspeak": {"sbrief": "L black arrow"}}, "key": "2B05"}, {"category": "So", "mappings": {"default": {"default": "up black arrow"}, "mathspeak": {"sbrief": "U black arrow"}}, "key": "2B06"}, {"category": "So", "mappings": {"default": {"default": "down black arrow"}, "mathspeak": {"sbrief": "D black arrow"}}, "key": "2B07"}, {"category": "So", "mappings": {"default": {"default": "north east black arrow"}}, "key": "2B08"}, {"category": "So", "mappings": {"default": {"default": "north west black arrow"}}, "key": "2B09"}, {"category": "So", "mappings": {"default": {"default": "south east black arrow"}}, "key": "2B0A"}, {"category": "So", "mappings": {"default": {"default": "south west black arrow"}}, "key": "2B0B"}, {"category": "So", "mappings": {"default": {"default": "left right black arrow"}, "mathspeak": {"sbrief": "L R black arrow"}}, "key": "2B0C"}, {"category": "So", "mappings": {"default": {"default": "up down black arrow"}, "mathspeak": {"sbrief": "U D black arrow"}}, "key": "2B0D"}, {"category": "So", "mappings": {"default": {"default": "right arrow with tip down"}, "mathspeak": {"sbrief": "R arrow with tip down"}}, "key": "2B0E"}, {"category": "So", "mappings": {"default": {"default": "right arrow with tip up"}, "mathspeak": {"sbrief": "R arrow with tip up"}}, "key": "2B0F"}, {"category": "So", "mappings": {"default": {"default": "left arrow with tip down"}, "mathspeak": {"sbrief": "L arrow with tip down"}}, "key": "2B10"}, {"category": "So", "mappings": {"default": {"default": "left arrow with tip up"}, "mathspeak": {"sbrief": "L arrow with tip up"}}, "key": "2B11"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with small circle"}, "mathspeak": {"sbrief": "L arrow with small circle"}}, "key": "2B30"}, {"category": "Sm", "mappings": {"default": {"default": "three left arrows"}, "mathspeak": {"sbrief": "three L arrows"}}, "key": "2B31"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with circled plus"}, "mathspeak": {"sbrief": "L arrow with circled plus"}}, "key": "2B32"}, {"category": "Sm", "mappings": {"default": {"default": "long left squiggle arrow"}, "mathspeak": {"sbrief": "long l squiggle arrow"}}, "key": "2B33"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow with vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with vertical stroke"}}, "key": "2B34"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow with double vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with double vertical stroke"}}, "key": "2B35"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow from bar"}, "mathspeak": {"sbrief": "l two headed arrow from bar"}}, "key": "2B36"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed triple dash arrow"}, "mathspeak": {"sbrief": "l two headed triple dash arrow"}}, "key": "2B37"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with dotted stem"}, "mathspeak": {"sbrief": "L arrow with dotted stem"}}, "key": "2B38"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "L arrow with tail with vertical stroke"}}, "key": "2B39"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "L arrow with tail with double vertical stroke"}}, "key": "2B3A"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow with tail"}, "mathspeak": {"sbrief": "l two headed arrow with tail"}}, "key": "2B3B"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with tail with vertical stroke"}}, "key": "2B3C"}, {"category": "Sm", "mappings": {"default": {"default": "left two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with tail with double vertical stroke"}}, "key": "2B3D"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow through x"}, "mathspeak": {"sbrief": "L arrow through x"}}, "key": "2B3E"}, {"category": "Sm", "mappings": {"default": {"default": "wave arrow pointing directly left"}}, "key": "2B3F"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above left arrow"}, "mathspeak": {"sbrief": "equals sign above L arrow"}}, "key": "2B40"}, {"category": "Sm", "mappings": {"default": {"default": "reverse tilde operator above left arrow"}, "mathspeak": {"sbrief": "reverse tilde operator above L arrow"}}, "key": "2B41"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow above reverse almost equals"}, "mathspeak": {"sbrief": "L arrow above reverse almost equals"}}, "key": "2B42"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow through greater than"}, "mathspeak": {"sbrief": "R arrow through greater than"}}, "key": "2B43"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow through superset"}, "mathspeak": {"sbrief": "R arrow through superset"}}, "key": "2B44"}, {"category": "So", "mappings": {"default": {"default": "left quadruple arrow"}, "mathspeak": {"sbrief": "l quadrule arrow"}}, "key": "2B45"}, {"category": "So", "mappings": {"default": {"default": "right quadruple arrow"}, "mathspeak": {"sbrief": "r quadrule arrow"}}, "key": "2B46"}, {"category": "Sm", "mappings": {"default": {"default": "reverse tilde operator above right arrow"}, "mathspeak": {"sbrief": "reverse tilde operator above R arrow"}}, "key": "2B47"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow above reverse almost equals"}, "mathspeak": {"sbrief": "R arrow above reverse almost equals"}}, "key": "2B48"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator above left arrow"}, "mathspeak": {"sbrief": "tilde operator above L arrow"}}, "key": "2B49"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow above almost equals"}, "mathspeak": {"sbrief": "L arrow above almost equals"}}, "key": "2B4A"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow above reverse tilde operator"}, "mathspeak": {"sbrief": "L arrow above reverse tilde operator"}}, "key": "2B4B"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow above reverse tilde operator"}, "mathspeak": {"sbrief": "R arrow above reverse tilde operator"}}, "key": "2B4C"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth left arrow"}, "mathspeak": {"sbrief": "halfwidth L arrow"}}, "key": "FFE9"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth up arrow"}, "mathspeak": {"sbrief": "halfwidth U arrow"}}, "key": "FFEA"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth right arrow"}, "mathspeak": {"sbrief": "halfwidth R arrow"}}, "key": "FFEB"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth down arrow"}, "mathspeak": {"sbrief": "halfwidth D arrow"}}, "key": "FFEC"}], "en/symbols/math_characters.js": [{"locale": "en"}, {"category": "Ll", "mappings": {"default": {"default": "script l"}}, "key": "2113"}, {"category": "Sm", "mappings": {"default": {"default": "script cap P"}, "mathspeak": {"default": "script upper P"}}, "key": "2118"}, {"category": "Ll", "mappings": {"default": {"default": "double struck pi"}}, "key": "213C"}, {"category": "Ll", "mappings": {"default": {"default": "double struck gamma"}}, "key": "213D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "double struck cap Gamma"}, "mathspeak": {"default": "double struck upper Gamma"}}, "key": "213E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "double struck cap Pi"}, "mathspeak": {"default": "double struck upper Pi"}}, "key": "213F"}, {"category": "Sm", "mappings": {"default": {"default": "double struck sum"}, "mathspeak": {"default": "double struck sigma summation"}}, "key": "2140"}, {"category": "<PERSON>", "mappings": {"default": {"default": "double struck italic cap D"}, "mathspeak": {"default": "double struck italic upper D"}}, "key": "2145"}, {"category": "Ll", "mappings": {"default": {"default": "double struck italic d"}}, "key": "2146"}, {"category": "Ll", "mappings": {"default": {"default": "double struck italic e"}}, "key": "2147"}, {"category": "Ll", "mappings": {"default": {"default": "double struck italic i"}}, "key": "2148"}, {"category": "Ll", "mappings": {"default": {"default": "double struck italic j"}}, "key": "2149"}, {"category": "Ll", "mappings": {"default": {"default": "italic dotless i"}}, "key": "1D6A4"}, {"category": "Ll", "mappings": {"default": {"default": "italic dotless j"}}, "key": "1D6A5"}], "en/symbols/math_delimiters.js": [{"locale": "en"}, {"category": "Ps", "mappings": {"default": {"default": "left parenthesis"}, "mathspeak": {"brief": "left p'ren", "sbrief": "L p'ren"}, "clearspeak": {"default": "open paren"}}, "key": "0028"}, {"category": "Pe", "mappings": {"default": {"default": "right parenthesis"}, "mathspeak": {"brief": "right p'ren", "sbrief": "R p'ren"}, "clearspeak": {"default": "close paren"}}, "key": "0029"}, {"category": "Ps", "mappings": {"default": {"default": "left bracket"}, "mathspeak": {"brief": "left brack", "sbrief": "L brack"}, "clearspeak": {"default": "open bracket"}}, "key": "005B"}, {"category": "Pe", "mappings": {"default": {"default": "right bracket"}, "mathspeak": {"brief": "right brack", "sbrief": "R brack"}, "clearspeak": {"default": "close bracket"}}, "key": "005D"}, {"category": "Ps", "mappings": {"default": {"default": "left brace"}, "mathspeak": {"sbrief": "L brace"}, "clearspeak": {"default": "open brace"}}, "key": "007B"}, {"category": "Pe", "mappings": {"default": {"default": "right brace"}, "mathspeak": {"sbrief": "R brace"}, "clearspeak": {"default": "close brace"}}, "key": "007D"}, {"category": "Ps", "mappings": {"default": {"default": "left bracket with quill"}, "mathspeak": {"brief": "left brack with quill", "sbrief": "L brack with quill"}}, "key": "2045"}, {"category": "Pe", "mappings": {"default": {"default": "right bracket with quill"}, "mathspeak": {"brief": "right brack with quill", "sbrief": "R brack with quill"}}, "key": "2046"}, {"category": "Sm", "mappings": {"default": {"default": "left ceiling"}}, "key": "2308"}, {"category": "Sm", "mappings": {"default": {"default": "right ceiling"}}, "key": "2309"}, {"category": "Sm", "mappings": {"default": {"default": "left floor"}}, "key": "230A"}, {"category": "Sm", "mappings": {"default": {"default": "right floor"}}, "key": "230B"}, {"category": "So", "mappings": {"default": {"default": "bottom right crop"}}, "key": "230C"}, {"category": "So", "mappings": {"default": {"default": "bottom left crop"}}, "key": "230D"}, {"category": "So", "mappings": {"default": {"default": "top right crop"}}, "key": "230E"}, {"category": "So", "mappings": {"default": {"default": "top left crop"}}, "key": "230F"}, {"category": "So", "mappings": {"default": {"default": "top left corner"}}, "key": "231C"}, {"category": "So", "mappings": {"default": {"default": "top right corner"}}, "key": "231D"}, {"category": "So", "mappings": {"default": {"default": "bottom left corner"}}, "key": "231E"}, {"category": "So", "mappings": {"default": {"default": "bottom right corner"}}, "key": "231F"}, {"category": "Sm", "mappings": {"default": {"default": "top half integral"}}, "key": "2320"}, {"category": "Sm", "mappings": {"default": {"default": "bottom half integral"}}, "key": "2321"}, {"category": "Ps", "mappings": {"default": {"default": "left pointing angle bracket", "physics": "bra"}}, "key": "2329"}, {"category": "Pe", "mappings": {"default": {"default": "right pointing angle bracket", "physics": "ket"}}, "key": "232A"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis upper hook"}, "mathspeak": {"brief": "left p'ren upper hook", "sbrief": "L p'ren upper hook"}}, "key": "239B"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis extension"}, "mathspeak": {"brief": "left p'ren extension", "sbrief": "L p'ren extension"}}, "key": "239C"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis lower hook"}, "mathspeak": {"brief": "left p'ren lower hook", "sbrief": "L p'ren lower hook"}}, "key": "239D"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis upper hook"}, "mathspeak": {"brief": "right p'ren upper hook", "sbrief": "R p'ren upper hook"}}, "key": "239E"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis extension"}, "mathspeak": {"brief": "right p'ren extension", "sbrief": "R p'ren extension"}}, "key": "239F"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis lower hook"}, "mathspeak": {"brief": "right p'ren lower hook", "sbrief": "R p'ren lower hook"}}, "key": "23A0"}, {"category": "Sm", "mappings": {"default": {"default": "left bracket upper corner"}, "mathspeak": {"brief": "left brack upper corner", "sbrief": "L brack upper corner"}}, "key": "23A1"}, {"category": "Sm", "mappings": {"default": {"default": "left bracket extension"}, "mathspeak": {"brief": "left brack extension", "sbrief": "L brack extension"}}, "key": "23A2"}, {"category": "Sm", "mappings": {"default": {"default": "left bracket lower corner"}, "mathspeak": {"brief": "left brack lower corner", "sbrief": "L brack lower corner"}}, "key": "23A3"}, {"category": "Sm", "mappings": {"default": {"default": "right bracket upper corner"}, "mathspeak": {"brief": "right brack upper corner", "sbrief": "R brack upper corner"}}, "key": "23A4"}, {"category": "Sm", "mappings": {"default": {"default": "right bracket extension"}, "mathspeak": {"brief": "right brack extension", "sbrief": "R brack extension"}}, "key": "23A5"}, {"category": "Sm", "mappings": {"default": {"default": "right bracket lower corner"}, "mathspeak": {"brief": "right brack lower corner", "sbrief": "R brack lower corner"}}, "key": "23A6"}, {"category": "Sm", "mappings": {"default": {"default": "left brace upper hook"}, "mathspeak": {"sbrief": "L brace upper hook"}}, "key": "23A7"}, {"category": "Sm", "mappings": {"default": {"default": "left brace middle piece"}, "mathspeak": {"sbrief": "L brace middle piece"}}, "key": "23A8"}, {"category": "Sm", "mappings": {"default": {"default": "left brace lower hook"}, "mathspeak": {"sbrief": "L brace lower hook"}}, "key": "23A9"}, {"category": "Sm", "mappings": {"default": {"default": "brace extension"}}, "key": "23AA"}, {"category": "Sm", "mappings": {"default": {"default": "right brace upper hook"}, "mathspeak": {"sbrief": "R brace upper hook"}}, "key": "23AB"}, {"category": "Sm", "mappings": {"default": {"default": "right brace middle piece"}, "mathspeak": {"sbrief": "R brace middle piece"}}, "key": "23AC"}, {"category": "Sm", "mappings": {"default": {"default": "right brace lower hook"}, "mathspeak": {"sbrief": "R brace lower hook"}}, "key": "23AD"}, {"category": "Sm", "mappings": {"default": {"default": "integral extension"}}, "key": "23AE"}, {"category": "Sm", "mappings": {"default": {"default": "horizontal line extension"}}, "key": "23AF"}, {"category": "Sm", "mappings": {"default": {"default": "upper left or lower right brace section"}}, "key": "23B0"}, {"category": "Sm", "mappings": {"default": {"default": "upper right or lower left brace section"}}, "key": "23B1"}, {"category": "Sm", "mappings": {"default": {"default": "summation top"}}, "key": "23B2"}, {"category": "Sm", "mappings": {"default": {"default": "summation bottom"}}, "key": "23B3"}, {"category": "So", "mappings": {"default": {"default": "top bracket"}, "mathspeak": {"brief": "top brack", "sbrief": "T brack"}}, "key": "23B4"}, {"category": "So", "mappings": {"default": {"default": "bottom bracket"}, "mathspeak": {"brief": "bottom brack", "sbrief": "B brack"}}, "key": "23B5"}, {"category": "So", "mappings": {"default": {"default": "bottom bracket over top bracket"}, "mathspeak": {"brief": "bottom brack over top brack", "sbrief": "B brack over T brack"}}, "key": "23B6"}, {"category": "So", "mappings": {"default": {"default": "radical symbol bottom"}}, "key": "23B7"}, {"category": "So", "mappings": {"default": {"default": "left vertical box line"}}, "key": "23B8"}, {"category": "So", "mappings": {"default": {"default": "right vertical box line"}}, "key": "23B9"}, {"category": "Sm", "mappings": {"default": {"default": "top parenthesis"}, "mathspeak": {"brief": "top p'ren", "sbrief": "t p'ren"}}, "key": "23DC"}, {"category": "Sm", "mappings": {"default": {"default": "bottom parenthesis"}, "mathspeak": {"brief": "bottom p'ren", "sbrief": "b p'ren"}}, "key": "23DD"}, {"category": "Sm", "mappings": {"default": {"default": "top brace"}, "mathspeak": {"sbrief": "T brace"}}, "key": "23DE"}, {"category": "Sm", "mappings": {"default": {"default": "bottom brace"}, "mathspeak": {"sbrief": "B brace"}}, "key": "23DF"}, {"category": "Sm", "mappings": {"default": {"default": "top tortoise shell bracket"}}, "key": "23E0"}, {"category": "Sm", "mappings": {"default": {"default": "bottom tortoise shell bracket"}}, "key": "23E1"}, {"category": "Ps", "mappings": {"default": {"default": "medium left parenthesis ornament"}, "mathspeak": {"brief": "medium left p'ren ornament", "sbrief": "medium L p'ren ornament"}}, "key": "2768"}, {"category": "Pe", "mappings": {"default": {"default": "medium right parenthesis ornament"}, "mathspeak": {"brief": "medium right p'ren ornament", "sbrief": "medium R p'ren ornament"}}, "key": "2769"}, {"category": "Ps", "mappings": {"default": {"default": "medium flattened left parenthesis ornament"}, "mathspeak": {"brief": "medium flattened left p'ren ornament", "sbrief": "medium flattened L p'ren ornament"}}, "key": "276A"}, {"category": "Pe", "mappings": {"default": {"default": "medium flattened right parenthesis ornament"}, "mathspeak": {"brief": "medium flattened right p'ren ornament", "sbrief": "medium flattened R p'ren ornament"}}, "key": "276B"}, {"category": "Ps", "mappings": {"default": {"default": "medium left pointing angle bracket ornament"}}, "key": "276C"}, {"category": "Pe", "mappings": {"default": {"default": "medium right pointing angle bracket ornament"}}, "key": "276D"}, {"category": "Ps", "mappings": {"default": {"default": "heavy left pointing angle quotation mark ornament"}}, "key": "276E"}, {"category": "Pe", "mappings": {"default": {"default": "heavy right pointing angle quotation mark ornament"}}, "key": "276F"}, {"category": "Ps", "mappings": {"default": {"default": "heavy left pointing angle bracket ornament"}}, "key": "2770"}, {"category": "Pe", "mappings": {"default": {"default": "heavy right pointing angle bracket ornament"}}, "key": "2771"}, {"category": "Ps", "mappings": {"default": {"default": "light left tortoise shell bracket ornament"}}, "key": "2772"}, {"category": "Pe", "mappings": {"default": {"default": "light right tortoise shell bracket ornament"}}, "key": "2773"}, {"category": "Ps", "mappings": {"default": {"default": "medium left brace ornament"}, "mathspeak": {"sbrief": "medium L brace ornament"}}, "key": "2774"}, {"category": "Pe", "mappings": {"default": {"default": "medium right brace ornament"}, "mathspeak": {"sbrief": "medium R brace ornament"}}, "key": "2775"}, {"category": "Ps", "mappings": {"default": {"default": "left s shaped bag delimiter"}}, "key": "27C5"}, {"category": "Pe", "mappings": {"default": {"default": "right s shaped bag delimiter"}}, "key": "27C6"}, {"category": "Ps", "mappings": {"default": {"default": "left white bracket"}}, "key": "27E6"}, {"category": "Pe", "mappings": {"default": {"default": "right white bracket"}}, "key": "27E7"}, {"category": "Ps", "mappings": {"default": {"default": "left angle bracket"}, "mathspeak": {"sbrief": "l angle bracket"}}, "key": "27E8"}, {"category": "Pe", "mappings": {"default": {"default": "right angle bracket"}, "mathspeak": {"sbrief": "r angle bracket"}}, "key": "27E9"}, {"category": "Ps", "mappings": {"default": {"default": "left double angle bracket"}}, "key": "27EA"}, {"category": "Pe", "mappings": {"default": {"default": "right double angle bracket"}}, "key": "27EB"}, {"category": "Ps", "mappings": {"default": {"default": "left white tortoise shell bracket"}}, "key": "27EC"}, {"category": "Pe", "mappings": {"default": {"default": "right white tortoise shell bracket"}}, "key": "27ED"}, {"category": "Ps", "mappings": {"default": {"default": "flattened left parenthesis"}, "mathspeak": {"brief": "flattened left p'ren", "sbrief": "flattened L p'ren"}}, "key": "27EE"}, {"category": "Pe", "mappings": {"default": {"default": "flattened right parenthesis"}, "mathspeak": {"brief": "flattened right p'ren", "sbrief": "flattened R p'ren"}}, "key": "27EF"}, {"category": "Ps", "mappings": {"default": {"default": "left white brace"}}, "key": "2983"}, {"category": "Pe", "mappings": {"default": {"default": "right white brace"}}, "key": "2984"}, {"category": "Ps", "mappings": {"default": {"default": "white left parenthesis"}, "mathspeak": {"brief": "white left p'ren", "sbrief": "white L p'ren"}}, "key": "2985"}, {"category": "Pe", "mappings": {"default": {"default": "white right parenthesis"}, "mathspeak": {"brief": "white right p'ren", "sbrief": "white R p'ren"}}, "key": "2986"}, {"category": "Ps", "mappings": {"default": {"default": "z notation left image bracket"}}, "key": "2987"}, {"category": "Pe", "mappings": {"default": {"default": "z notation right image bracket"}}, "key": "2988"}, {"category": "Ps", "mappings": {"default": {"default": "z notation left binding bracket"}}, "key": "2989"}, {"category": "Pe", "mappings": {"default": {"default": "z notation right binding bracket"}}, "key": "298A"}, {"category": "Ps", "mappings": {"default": {"default": "left bracket with underbar"}, "mathspeak": {"brief": "left brack with underbar", "sbrief": "L brack with underbar"}}, "key": "298B"}, {"category": "Pe", "mappings": {"default": {"default": "right bracket with underbar"}, "mathspeak": {"brief": "right brack with underbar", "sbrief": "R brack with underbar"}}, "key": "298C"}, {"category": "Ps", "mappings": {"default": {"default": "left bracket with tick in top corner"}, "mathspeak": {"brief": "left brack with tick in top corner", "sbrief": "L brack with tick in top corner"}}, "key": "298D"}, {"category": "Pe", "mappings": {"default": {"default": "right bracket with tick in bottom corner"}, "mathspeak": {"brief": "right brack with tick in bottom corner", "sbrief": "R brack with tick in bottom corner"}}, "key": "298E"}, {"category": "Ps", "mappings": {"default": {"default": "left bracket with tick in bottom corner"}, "mathspeak": {"brief": "left brack with tick in bottom corner", "sbrief": "L brack with tick in bottom corner"}}, "key": "298F"}, {"category": "Pe", "mappings": {"default": {"default": "right bracket with tick in top corner"}, "mathspeak": {"brief": "right brack with tick in top corner", "sbrief": "R brack with tick in top corner"}}, "key": "2990"}, {"category": "Ps", "mappings": {"default": {"default": "left angle bracket with dot"}, "mathspeak": {"sbrief": "l angle bracket with dot"}}, "key": "2991"}, {"category": "Pe", "mappings": {"default": {"default": "right angle bracket with dot"}, "mathspeak": {"sbrief": "r angle bracket with dot"}}, "key": "2992"}, {"category": "Ps", "mappings": {"default": {"default": "left arc less than bracket"}}, "key": "2993"}, {"category": "Pe", "mappings": {"default": {"default": "right arc greater than bracket"}}, "key": "2994"}, {"category": "Ps", "mappings": {"default": {"default": "double left arc greater than bracket"}}, "key": "2995"}, {"category": "Pe", "mappings": {"default": {"default": "double right arc less than bracket"}}, "key": "2996"}, {"category": "Ps", "mappings": {"default": {"default": "left black tortoise shell bracket"}}, "key": "2997"}, {"category": "Pe", "mappings": {"default": {"default": "right black tortoise shell bracket"}}, "key": "2998"}, {"category": "Ps", "mappings": {"default": {"default": "left wiggly fence"}}, "key": "29D8"}, {"category": "Pe", "mappings": {"default": {"default": "right wiggly fence"}}, "key": "29D9"}, {"category": "Ps", "mappings": {"default": {"default": "left double wiggly fence"}}, "key": "29DA"}, {"category": "Pe", "mappings": {"default": {"default": "right double wiggly fence"}}, "key": "29DB"}, {"category": "Ps", "mappings": {"default": {"default": "left pointing curved angle bracket"}}, "key": "29FC"}, {"category": "Pe", "mappings": {"default": {"default": "right pointing curved angle bracket"}}, "key": "29FD"}, {"category": "Ps", "mappings": {"default": {"default": "top half left bracket"}, "mathspeak": {"brief": "top half left brack", "sbrief": "top half L brack"}}, "key": "2E22"}, {"category": "Pe", "mappings": {"default": {"default": "top half right bracket"}, "mathspeak": {"brief": "top half right brack", "sbrief": "top half R brack"}}, "key": "2E23"}, {"category": "Ps", "mappings": {"default": {"default": "bottom half left bracket"}, "mathspeak": {"brief": "bottom half left brack", "sbrief": "bottom half L brack"}}, "key": "2E24"}, {"category": "Pe", "mappings": {"default": {"default": "bottom half right bracket"}, "mathspeak": {"brief": "bottom half right brack", "sbrief": "bottom half R brack"}}, "key": "2E25"}, {"category": "Ps", "mappings": {"default": {"default": "left sideways U bracket"}}, "key": "2E26"}, {"category": "Pe", "mappings": {"default": {"default": "right sideways U bracket"}}, "key": "2E27"}, {"category": "Ps", "mappings": {"default": {"default": "double left parenthesis"}, "mathspeak": {"brief": "double left p'ren", "sbrief": "double L p'ren"}}, "key": "2E28"}, {"category": "Pe", "mappings": {"default": {"default": "double right parenthesis"}, "mathspeak": {"brief": "double right p'ren", "sbrief": "double R p'ren"}}, "key": "2E29"}, {"category": "Ps", "mappings": {"default": {"default": "left angle bracket"}, "mathspeak": {"sbrief": "l angle bracket"}}, "key": "3008"}, {"category": "Pe", "mappings": {"default": {"default": "right angle bracket"}, "mathspeak": {"sbrief": "r angle bracket"}}, "key": "3009"}, {"category": "Ps", "mappings": {"default": {"default": "left double angle bracket"}}, "key": "300A"}, {"category": "Pe", "mappings": {"default": {"default": "right double angle bracket"}}, "key": "300B"}, {"category": "Ps", "mappings": {"default": {"default": "left corner bracket"}}, "key": "300C"}, {"category": "Pe", "mappings": {"default": {"default": "right corner bracket"}}, "key": "300D"}, {"category": "Ps", "mappings": {"default": {"default": "left white corner bracket"}}, "key": "300E"}, {"category": "Pe", "mappings": {"default": {"default": "right white corner bracket"}}, "key": "300F"}, {"category": "Ps", "mappings": {"default": {"default": "left black lenticular bracket"}}, "key": "3010"}, {"category": "Pe", "mappings": {"default": {"default": "right black lenticular bracket"}}, "key": "3011"}, {"category": "Ps", "mappings": {"default": {"default": "left tortoise shell bracket"}}, "key": "3014"}, {"category": "Pe", "mappings": {"default": {"default": "right tortoise shell bracket"}}, "key": "3015"}, {"category": "Ps", "mappings": {"default": {"default": "left white lenticular bracket"}}, "key": "3016"}, {"category": "Pe", "mappings": {"default": {"default": "right white lenticular bracket"}}, "key": "3017"}, {"category": "Ps", "mappings": {"default": {"default": "left white tortoise shell bracket"}}, "key": "3018"}, {"category": "Pe", "mappings": {"default": {"default": "right white tortoise shell bracket"}}, "key": "3019"}, {"category": "Ps", "mappings": {"default": {"default": "left white bracket"}}, "key": "301A"}, {"category": "Pe", "mappings": {"default": {"default": "right white bracket"}}, "key": "301B"}, {"category": "Ps", "mappings": {"default": {"default": "reversed double prime quotation mark"}}, "key": "301D"}, {"category": "Pe", "mappings": {"default": {"default": "double prime quotation mark"}}, "key": "301E"}, {"category": "Pe", "mappings": {"default": {"default": "low double prime quotation mark"}}, "key": "301F"}, {"category": "Ps", "mappings": {"default": {"default": "ornate left parenthesis"}, "mathspeak": {"brief": "ornate left p'ren", "sbrief": "ornate L p'ren"}}, "key": "FD3E"}, {"category": "Pe", "mappings": {"default": {"default": "ornate right parenthesis"}, "mathspeak": {"brief": "ornate right p'ren", "sbrief": "ornate R p'ren"}}, "key": "FD3F"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left white lenticular bracket"}}, "key": "FE17"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right white lenticular brakcet"}}, "key": "FE18"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left parenthesis"}, "mathspeak": {"brief": "presentation form for vertical left p'ren", "sbrief": "presentation form for vertical L p'ren"}}, "key": "FE35"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right parenthesis"}, "mathspeak": {"brief": "presentation form for vertical right p'ren", "sbrief": "presentation form for vertical R p'ren"}}, "key": "FE36"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left brace"}, "mathspeak": {"sbrief": "presentation form for vertical L brace"}}, "key": "FE37"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right brace"}, "mathspeak": {"sbrief": "presentation form for vertical r brace"}}, "key": "FE38"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left tortoise shell bracket"}}, "key": "FE39"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right tortoise shell bracket"}}, "key": "FE3A"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left black lenticular bracket"}}, "key": "FE3B"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right black lenticular bracket"}}, "key": "FE3C"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left double angle bracket"}}, "key": "FE3D"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right double angle bracket"}}, "key": "FE3E"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left angle bracket"}, "mathspeak": {"sbrief": "presentation form for vertical l angle bracket"}}, "key": "FE3F"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right angle bracket"}, "mathspeak": {"sbrief": "presentation form for vertical r angle bracket"}}, "key": "FE40"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left corner bracket"}}, "key": "FE41"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right corner bracket"}}, "key": "FE42"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left white corner bracket"}}, "key": "FE43"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right white corner bracket"}}, "key": "FE44"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left bracket"}, "mathspeak": {"brief": "presentation form for vertical left brack", "sbrief": "presentation form for vertical L brack"}}, "key": "FE47"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right bracket"}, "mathspeak": {"brief": "presentation form for vertical right brack", "sbrief": "presentation form for vertical r brack"}}, "key": "FE48"}, {"category": "Ps", "mappings": {"default": {"default": "small left parenthesis"}, "mathspeak": {"brief": "small left p'ren", "sbrief": "small L p'ren"}}, "key": "FE59"}, {"category": "Pe", "mappings": {"default": {"default": "small right parenthesis"}, "mathspeak": {"brief": "small right p'ren", "sbrief": "small R p'ren"}}, "key": "FE5A"}, {"category": "Ps", "mappings": {"default": {"default": "small left brace"}, "mathspeak": {"sbrief": "small L brace"}}, "key": "FE5B"}, {"category": "Pe", "mappings": {"default": {"default": "small right brace"}, "mathspeak": {"sbrief": "small r brace"}}, "key": "FE5C"}, {"category": "Ps", "mappings": {"default": {"default": "small left tortoise shell bracket"}}, "key": "FE5D"}, {"category": "Pe", "mappings": {"default": {"default": "small right tortoise shell bracket"}}, "key": "FE5E"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left parenthesis"}, "mathspeak": {"brief": "fullwidth left p'ren", "sbrief": "fullwidth L p'ren"}}, "key": "FF08"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right parenthesis"}, "mathspeak": {"brief": "fullwidth right p'ren", "sbrief": "fullwidth R p'ren"}}, "key": "FF09"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left bracket"}, "mathspeak": {"brief": "fullwidth left brack", "sbrief": "fullwidth L brack"}}, "key": "FF3B"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right bracket"}, "mathspeak": {"brief": "fullwidth right brack", "sbrief": "fullwidth r brack"}}, "key": "FF3D"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left brace"}, "mathspeak": {"sbrief": "fullwidth L brace"}}, "key": "FF5B"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right brace"}, "mathspeak": {"sbrief": "fullwidth r brace"}}, "key": "FF5D"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth white left parenthesis"}, "mathspeak": {"brief": "fullwidth white left p'ren", "sbrief": "fullwidth white L p'ren"}}, "key": "FF5F"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth white right parenthesis"}, "mathspeak": {"brief": "fullwidth white right p'ren", "sbrief": "fullwidth white R p'ren"}}, "key": "FF60"}, {"category": "Ps", "mappings": {"default": {"default": "halfwidth left corner bracket"}}, "key": "FF62"}, {"category": "Pe", "mappings": {"default": {"default": "halfwidth right corner bracket"}}, "key": "FF63"}], "en/symbols/math_geometry.js": [{"locale": "en"}, {"category": "So", "mappings": {"default": {"default": "box drawings light horizontal"}}, "key": "2500"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy horizontal"}}, "key": "2501"}, {"category": "So", "mappings": {"default": {"default": "box drawings light vertical"}}, "key": "2502"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy vertical"}}, "key": "2503"}, {"category": "So", "mappings": {"default": {"default": "box drawings light triple dash horizontal"}}, "key": "2504"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy triple dash horizontal"}}, "key": "2505"}, {"category": "So", "mappings": {"default": {"default": "box drawings light triple dash vertical"}}, "key": "2506"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy triple dash vertical"}}, "key": "2507"}, {"category": "So", "mappings": {"default": {"default": "box drawings light quadruple dash horizontal"}}, "key": "2508"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy quadruple dash horizontal"}}, "key": "2509"}, {"category": "So", "mappings": {"default": {"default": "box drawings light quadruple dash vertical"}}, "key": "250A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy quadruple dash vertical"}}, "key": "250B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light down and right"}}, "key": "250C"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and right heavy"}}, "key": "250D"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and right light"}}, "key": "250E"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy down and right"}}, "key": "250F"}, {"category": "So", "mappings": {"default": {"default": "box drawings light down and left"}}, "key": "2510"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and left heavy"}}, "key": "2511"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and left light"}}, "key": "2512"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy down and left"}}, "key": "2513"}, {"category": "So", "mappings": {"default": {"default": "box drawings light up and right"}}, "key": "2514"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and right heavy"}}, "key": "2515"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and right light"}}, "key": "2516"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy up and right"}}, "key": "2517"}, {"category": "So", "mappings": {"default": {"default": "box drawings light up and left"}}, "key": "2518"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and left heavy"}}, "key": "2519"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and left light"}}, "key": "251A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy up and left"}}, "key": "251B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light vertical and right"}}, "key": "251C"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical light and right heavy"}}, "key": "251D"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and right down light"}}, "key": "251E"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and right up light"}}, "key": "251F"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical heavy and right light"}}, "key": "2520"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and right up heavy"}}, "key": "2521"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and right down heavy"}}, "key": "2522"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy vertical and right"}}, "key": "2523"}, {"category": "So", "mappings": {"default": {"default": "box drawings light vertical and left"}}, "key": "2524"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical light and left heavy"}}, "key": "2525"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and left down light"}}, "key": "2526"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and left up light"}}, "key": "2527"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical heavy and left light"}}, "key": "2528"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and left up heavy"}}, "key": "2529"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and left down heavy"}}, "key": "252A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy vertical and left"}}, "key": "252B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light down and horizontal"}}, "key": "252C"}, {"category": "So", "mappings": {"default": {"default": "box drawings left heavy and right down light"}}, "key": "252D"}, {"category": "So", "mappings": {"default": {"default": "box drawings right heavy and left down light"}}, "key": "252E"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and horizontal heavy"}}, "key": "252F"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and horizontal light"}}, "key": "2530"}, {"category": "So", "mappings": {"default": {"default": "box drawings right light and left down heavy"}}, "key": "2531"}, {"category": "So", "mappings": {"default": {"default": "box drawings left light and right down heavy"}}, "key": "2532"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy down and horizontal"}}, "key": "2533"}, {"category": "So", "mappings": {"default": {"default": "box drawings light up and horizontal"}}, "key": "2534"}, {"category": "So", "mappings": {"default": {"default": "box drawings left heavy and right up light"}}, "key": "2535"}, {"category": "So", "mappings": {"default": {"default": "box drawings right heavy and left up light"}}, "key": "2536"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and horizontal heavy"}}, "key": "2537"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and horizontal light"}}, "key": "2538"}, {"category": "So", "mappings": {"default": {"default": "box drawings right light and left up heavy"}}, "key": "2539"}, {"category": "So", "mappings": {"default": {"default": "box drawings left light and right up heavy"}}, "key": "253A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy up and horizontal"}}, "key": "253B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light vertical and horizontal"}}, "key": "253C"}, {"category": "So", "mappings": {"default": {"default": "box drawings left heavy and right vertical light"}}, "key": "253D"}, {"category": "So", "mappings": {"default": {"default": "box drawings right heavy and left vertical light"}}, "key": "253E"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical light and horizontal heavy"}}, "key": "253F"}, {"category": "So", "mappings": {"default": {"default": "box drawings up heavy and down horizontal light"}}, "key": "2540"}, {"category": "So", "mappings": {"default": {"default": "box drawings down heavy and up horizontal light"}}, "key": "2541"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical heavy and horizontal light"}}, "key": "2542"}, {"category": "So", "mappings": {"default": {"default": "box drawings left up heavy and right down light"}}, "key": "2543"}, {"category": "So", "mappings": {"default": {"default": "box drawings right up heavy and left down light"}}, "key": "2544"}, {"category": "So", "mappings": {"default": {"default": "box drawings left down heavy and right up light"}}, "key": "2545"}, {"category": "So", "mappings": {"default": {"default": "box drawings right down heavy and left up light"}}, "key": "2546"}, {"category": "So", "mappings": {"default": {"default": "box drawings down light and up horizontal heavy"}}, "key": "2547"}, {"category": "So", "mappings": {"default": {"default": "box drawings up light and down horizontal heavy"}}, "key": "2548"}, {"category": "So", "mappings": {"default": {"default": "box drawings right light and left vertical heavy"}}, "key": "2549"}, {"category": "So", "mappings": {"default": {"default": "box drawings left light and right vertical heavy"}}, "key": "254A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy vertical and horizontal"}}, "key": "254B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light double dash horizontal"}}, "key": "254C"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy double dash horizontal"}}, "key": "254D"}, {"category": "So", "mappings": {"default": {"default": "box drawings light double dash vertical"}}, "key": "254E"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy double dash vertical"}}, "key": "254F"}, {"category": "So", "mappings": {"default": {"default": "box drawings double horizontal"}}, "key": "2550"}, {"category": "So", "mappings": {"default": {"default": "box drawings double vertical"}}, "key": "2551"}, {"category": "So", "mappings": {"default": {"default": "box drawings down single and right double"}}, "key": "2552"}, {"category": "So", "mappings": {"default": {"default": "box drawings down double and right single"}}, "key": "2553"}, {"category": "So", "mappings": {"default": {"default": "box drawings double down and right"}}, "key": "2554"}, {"category": "So", "mappings": {"default": {"default": "box drawings down single and left double"}}, "key": "2555"}, {"category": "So", "mappings": {"default": {"default": "box drawings down double and left single"}}, "key": "2556"}, {"category": "So", "mappings": {"default": {"default": "box drawings double down and left"}}, "key": "2557"}, {"category": "So", "mappings": {"default": {"default": "box drawings up single and right double"}}, "key": "2558"}, {"category": "So", "mappings": {"default": {"default": "box drawings up double and right single"}}, "key": "2559"}, {"category": "So", "mappings": {"default": {"default": "box drawings double up and right"}}, "key": "255A"}, {"category": "So", "mappings": {"default": {"default": "box drawings up single and left double"}}, "key": "255B"}, {"category": "So", "mappings": {"default": {"default": "box drawings up double and left single"}}, "key": "255C"}, {"category": "So", "mappings": {"default": {"default": "box drawings double up and left"}}, "key": "255D"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical single and right double"}}, "key": "255E"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical double and right single"}}, "key": "255F"}, {"category": "So", "mappings": {"default": {"default": "box drawings double vertical and right"}}, "key": "2560"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical single and left double"}}, "key": "2561"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical double and left single"}}, "key": "2562"}, {"category": "So", "mappings": {"default": {"default": "box drawings double vertical and left"}}, "key": "2563"}, {"category": "So", "mappings": {"default": {"default": "box drawings down single and horizontal double"}}, "key": "2564"}, {"category": "So", "mappings": {"default": {"default": "box drawings down double and horizontal single"}}, "key": "2565"}, {"category": "So", "mappings": {"default": {"default": "box drawings double down and horizontal"}}, "key": "2566"}, {"category": "So", "mappings": {"default": {"default": "box drawings up single and horizontal double"}}, "key": "2567"}, {"category": "So", "mappings": {"default": {"default": "box drawings up double and horizontal single"}}, "key": "2568"}, {"category": "So", "mappings": {"default": {"default": "box drawings double up and horizontal"}}, "key": "2569"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical single and horizontal double"}}, "key": "256A"}, {"category": "So", "mappings": {"default": {"default": "box drawings vertical double and horizontal single"}}, "key": "256B"}, {"category": "So", "mappings": {"default": {"default": "box drawings double vertical and horizontal"}}, "key": "256C"}, {"category": "So", "mappings": {"default": {"default": "box drawings light arc down and right"}}, "key": "256D"}, {"category": "So", "mappings": {"default": {"default": "box drawings light arc down and left"}}, "key": "256E"}, {"category": "So", "mappings": {"default": {"default": "box drawings light arc up and left"}}, "key": "256F"}, {"category": "So", "mappings": {"default": {"default": "box drawings light arc up and right"}}, "key": "2570"}, {"category": "So", "mappings": {"default": {"default": "box drawings light diagonal upper right to lower left"}}, "key": "2571"}, {"category": "So", "mappings": {"default": {"default": "box drawings light diagonal upper left to lower right"}}, "key": "2572"}, {"category": "So", "mappings": {"default": {"default": "box drawings light diagonal cross"}}, "key": "2573"}, {"category": "So", "mappings": {"default": {"default": "box drawings light left"}}, "key": "2574"}, {"category": "So", "mappings": {"default": {"default": "box drawings light up"}}, "key": "2575"}, {"category": "So", "mappings": {"default": {"default": "box drawings light right"}}, "key": "2576"}, {"category": "So", "mappings": {"default": {"default": "box drawings light down"}}, "key": "2577"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy left"}}, "key": "2578"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy up"}}, "key": "2579"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy right"}}, "key": "257A"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy down"}}, "key": "257B"}, {"category": "So", "mappings": {"default": {"default": "box drawings light left and heavy right"}}, "key": "257C"}, {"category": "So", "mappings": {"default": {"default": "box drawings light up and heavy down"}}, "key": "257D"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy left and light right"}}, "key": "257E"}, {"category": "So", "mappings": {"default": {"default": "box drawings heavy up and light down"}}, "key": "257F"}, {"category": "So", "mappings": {"default": {"default": "upper half block"}}, "key": "2580"}, {"category": "So", "mappings": {"default": {"default": "lower one eighth block"}}, "key": "2581"}, {"category": "So", "mappings": {"default": {"default": "lower one quarter block"}}, "key": "2582"}, {"category": "So", "mappings": {"default": {"default": "lower three eighths block"}}, "key": "2583"}, {"category": "So", "mappings": {"default": {"default": "lower half block"}}, "key": "2584"}, {"category": "So", "mappings": {"default": {"default": "lower five eighths block"}}, "key": "2585"}, {"category": "So", "mappings": {"default": {"default": "lower three quarters block"}}, "key": "2586"}, {"category": "So", "mappings": {"default": {"default": "lower seven eighths block"}}, "key": "2587"}, {"category": "So", "mappings": {"default": {"default": "full block"}}, "key": "2588"}, {"category": "So", "mappings": {"default": {"default": "left seven eighths block"}}, "key": "2589"}, {"category": "So", "mappings": {"default": {"default": "left three quarters block"}}, "key": "258A"}, {"category": "So", "mappings": {"default": {"default": "left five eighths block"}}, "key": "258B"}, {"category": "So", "mappings": {"default": {"default": "left half block"}}, "key": "258C"}, {"category": "So", "mappings": {"default": {"default": "left three eighths block"}}, "key": "258D"}, {"category": "So", "mappings": {"default": {"default": "left one quarter block"}}, "key": "258E"}, {"category": "So", "mappings": {"default": {"default": "left one eighth block"}}, "key": "258F"}, {"category": "So", "mappings": {"default": {"default": "right half block"}}, "key": "2590"}, {"category": "So", "mappings": {"default": {"default": "light shade"}}, "key": "2591"}, {"category": "So", "mappings": {"default": {"default": "medium shade"}}, "key": "2592"}, {"category": "So", "mappings": {"default": {"default": "dark shade"}}, "key": "2593"}, {"category": "So", "mappings": {"default": {"default": "upper one eighth block"}}, "key": "2594"}, {"category": "So", "mappings": {"default": {"default": "right one eighth block"}}, "key": "2595"}, {"category": "So", "mappings": {"default": {"default": "quadrant lower left"}}, "key": "2596"}, {"category": "So", "mappings": {"default": {"default": "quadrant lower right"}}, "key": "2597"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper left"}}, "key": "2598"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper left and lower left and lower right"}}, "key": "2599"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper left and lower right"}}, "key": "259A"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper left and upper right and lower left"}}, "key": "259B"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper left and upper right and lower right"}}, "key": "259C"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper right"}}, "key": "259D"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper right and lower left"}}, "key": "259E"}, {"category": "So", "mappings": {"default": {"default": "quadrant upper right and lower left and lower right"}}, "key": "259F"}, {"category": "So", "mappings": {"default": {"default": "black square"}}, "key": "25A0"}, {"category": "So", "mappings": {"default": {"default": "white square"}}, "key": "25A1"}, {"category": "So", "mappings": {"default": {"default": "white square with rounded corners"}}, "key": "25A2"}, {"category": "So", "mappings": {"default": {"default": "white square containing black small square"}}, "key": "25A3"}, {"category": "So", "mappings": {"default": {"default": "square with horizontal fill"}}, "key": "25A4"}, {"category": "So", "mappings": {"default": {"default": "square with vertical fill"}}, "key": "25A5"}, {"category": "So", "mappings": {"default": {"default": "square with orthogonal crosshatch fill"}}, "key": "25A6"}, {"category": "So", "mappings": {"default": {"default": "square with upper left to lower right fill"}}, "key": "25A7"}, {"category": "So", "mappings": {"default": {"default": "square with upper right to lower left fill"}}, "key": "25A8"}, {"category": "So", "mappings": {"default": {"default": "square with diagonal crosshatch fill"}}, "key": "25A9"}, {"category": "So", "mappings": {"default": {"default": "black small square"}}, "key": "25AA"}, {"category": "So", "mappings": {"default": {"default": "white small square"}}, "key": "25AB"}, {"category": "So", "mappings": {"default": {"default": "black rectangle"}}, "key": "25AC"}, {"category": "So", "mappings": {"default": {"default": "white rectangle"}}, "key": "25AD"}, {"category": "So", "mappings": {"default": {"default": "black vertical rectangle"}}, "key": "25AE"}, {"category": "So", "mappings": {"default": {"default": "white vertical rectangle"}}, "key": "25AF"}, {"category": "So", "mappings": {"default": {"default": "black parallelogram"}}, "key": "25B0"}, {"category": "So", "mappings": {"default": {"default": "white parallelogram"}}, "key": "25B1"}, {"category": "So", "mappings": {"default": {"default": "black up pointing triangle"}}, "key": "25B2"}, {"category": "So", "mappings": {"default": {"default": "white up pointing triangle"}}, "key": "25B3"}, {"category": "So", "mappings": {"default": {"default": "black up pointing small triangle"}}, "key": "25B4"}, {"category": "So", "mappings": {"default": {"default": "white up pointing small triangle"}}, "key": "25B5"}, {"category": "So", "mappings": {"default": {"default": "black right pointing triangle"}}, "key": "25B6"}, {"category": "Sm", "mappings": {"default": {"default": "white right pointing triangle"}}, "key": "25B7"}, {"category": "So", "mappings": {"default": {"default": "black right pointing small triangle"}}, "key": "25B8"}, {"category": "So", "mappings": {"default": {"default": "white right pointing small triangle"}}, "key": "25B9"}, {"category": "So", "mappings": {"default": {"default": "black right pointing pointer"}}, "key": "25BA"}, {"category": "So", "mappings": {"default": {"default": "white right pointing pointer"}}, "key": "25BB"}, {"category": "So", "mappings": {"default": {"default": "black down pointing triangle"}}, "key": "25BC"}, {"category": "So", "mappings": {"default": {"default": "white down pointing triangle"}}, "key": "25BD"}, {"category": "So", "mappings": {"default": {"default": "black down pointing small triangle"}}, "key": "25BE"}, {"category": "So", "mappings": {"default": {"default": "white down pointing small triangle"}}, "key": "25BF"}, {"category": "So", "mappings": {"default": {"default": "black left pointing triangle"}}, "key": "25C0"}, {"category": "Sm", "mappings": {"default": {"default": "white left pointing triangle"}}, "key": "25C1"}, {"category": "So", "mappings": {"default": {"default": "black left pointing small triangle"}}, "key": "25C2"}, {"category": "So", "mappings": {"default": {"default": "white left pointing small triangle"}}, "key": "25C3"}, {"category": "So", "mappings": {"default": {"default": "black left pointing pointer"}}, "key": "25C4"}, {"category": "So", "mappings": {"default": {"default": "white left pointing pointer"}}, "key": "25C5"}, {"category": "So", "mappings": {"default": {"default": "black diamond"}}, "key": "25C6"}, {"category": "So", "mappings": {"default": {"default": "white diamond"}}, "key": "25C7"}, {"category": "So", "mappings": {"default": {"default": "white diamond containing black small diamond"}}, "key": "25C8"}, {"category": "So", "mappings": {"default": {"default": "fisheye"}}, "key": "25C9"}, {"category": "So", "mappings": {"default": {"default": "lozenge"}}, "key": "25CA"}, {"category": "So", "mappings": {"default": {"default": "white circle"}}, "key": "25CB"}, {"category": "So", "mappings": {"default": {"default": "dotted circle"}}, "key": "25CC"}, {"category": "So", "mappings": {"default": {"default": "circle with vertical fill"}}, "key": "25CD"}, {"category": "So", "mappings": {"default": {"default": "bullseye"}}, "key": "25CE"}, {"category": "So", "mappings": {"default": {"default": "black circle"}}, "key": "25CF"}, {"category": "So", "mappings": {"default": {"default": "circle with left half black"}}, "key": "25D0"}, {"category": "So", "mappings": {"default": {"default": "circle with right half black"}}, "key": "25D1"}, {"category": "So", "mappings": {"default": {"default": "circle with lower half black"}}, "key": "25D2"}, {"category": "So", "mappings": {"default": {"default": "circle with upper half black"}}, "key": "25D3"}, {"category": "So", "mappings": {"default": {"default": "circle with upper right quadrant black"}}, "key": "25D4"}, {"category": "So", "mappings": {"default": {"default": "circle with all but upper left quadrant black"}}, "key": "25D5"}, {"category": "So", "mappings": {"default": {"default": "left half black circle"}}, "key": "25D6"}, {"category": "So", "mappings": {"default": {"default": "right half black circle"}}, "key": "25D7"}, {"category": "So", "mappings": {"default": {"default": "inverse bullet"}}, "key": "25D8"}, {"category": "So", "mappings": {"default": {"default": "inverse white circle"}}, "key": "25D9"}, {"category": "So", "mappings": {"default": {"default": "upper half inverse white circle"}}, "key": "25DA"}, {"category": "So", "mappings": {"default": {"default": "lower half inverse white circle"}}, "key": "25DB"}, {"category": "So", "mappings": {"default": {"default": "upper left quadrant circular arc"}}, "key": "25DC"}, {"category": "So", "mappings": {"default": {"default": "upper right quadrant circular arc"}}, "key": "25DD"}, {"category": "So", "mappings": {"default": {"default": "lower right quadrant circular arc"}}, "key": "25DE"}, {"category": "So", "mappings": {"default": {"default": "lower left quadrant circular arc"}}, "key": "25DF"}, {"category": "So", "mappings": {"default": {"default": "upper half circle"}}, "key": "25E0"}, {"category": "So", "mappings": {"default": {"default": "lower half circle"}}, "key": "25E1"}, {"category": "So", "mappings": {"default": {"default": "black lower right triangle"}}, "key": "25E2"}, {"category": "So", "mappings": {"default": {"default": "black lower left triangle"}}, "key": "25E3"}, {"category": "So", "mappings": {"default": {"default": "black upper left triangle"}}, "key": "25E4"}, {"category": "So", "mappings": {"default": {"default": "black upper right triangle"}}, "key": "25E5"}, {"category": "So", "mappings": {"default": {"default": "white bullet"}}, "key": "25E6"}, {"category": "So", "mappings": {"default": {"default": "square with left half black"}}, "key": "25E7"}, {"category": "So", "mappings": {"default": {"default": "square with right half black"}}, "key": "25E8"}, {"category": "So", "mappings": {"default": {"default": "square with upper left diagonal half black"}}, "key": "25E9"}, {"category": "So", "mappings": {"default": {"default": "square with lower right diagonal half black"}}, "key": "25EA"}, {"category": "So", "mappings": {"default": {"default": "white square with vertical bisecting line"}}, "key": "25EB"}, {"category": "So", "mappings": {"default": {"default": "white up pointing triangle with dot"}}, "key": "25EC"}, {"category": "So", "mappings": {"default": {"default": "up pointing triangle with left half black"}}, "key": "25ED"}, {"category": "So", "mappings": {"default": {"default": "up pointing triangle with right half black"}}, "key": "25EE"}, {"category": "So", "mappings": {"default": {"default": "large circle"}}, "key": "25EF"}, {"category": "So", "mappings": {"default": {"default": "white square with upper left quadrant"}}, "key": "25F0"}, {"category": "So", "mappings": {"default": {"default": "white square with lower left quadrant"}}, "key": "25F1"}, {"category": "So", "mappings": {"default": {"default": "white square with lower right quadrant"}}, "key": "25F2"}, {"category": "So", "mappings": {"default": {"default": "white square with upper right quadrant"}}, "key": "25F3"}, {"category": "So", "mappings": {"default": {"default": "white circle with upper left quadrant"}}, "key": "25F4"}, {"category": "So", "mappings": {"default": {"default": "white circle with lower left quadrant"}}, "key": "25F5"}, {"category": "So", "mappings": {"default": {"default": "white circle with lower right quadrant"}}, "key": "25F6"}, {"category": "So", "mappings": {"default": {"default": "white circle with upper right quadrant"}}, "key": "25F7"}, {"category": "Sm", "mappings": {"default": {"default": "upper left triangle"}}, "key": "25F8"}, {"category": "Sm", "mappings": {"default": {"default": "upper right triangle"}}, "key": "25F9"}, {"category": "Sm", "mappings": {"default": {"default": "lower left triangle"}}, "key": "25FA"}, {"category": "Sm", "mappings": {"default": {"default": "white medium square"}}, "key": "25FB"}, {"category": "Sm", "mappings": {"default": {"default": "black medium square"}}, "key": "25FC"}, {"category": "Sm", "mappings": {"default": {"default": "white medium small square"}}, "key": "25FD"}, {"category": "Sm", "mappings": {"default": {"default": "black medium small square"}}, "key": "25FE"}, {"category": "Sm", "mappings": {"default": {"default": "lower right triangle"}}, "key": "25FF"}, {"category": "So", "mappings": {"default": {"default": "square with top half black"}}, "key": "2B12"}, {"category": "So", "mappings": {"default": {"default": "square with bottom half black"}}, "key": "2B13"}, {"category": "So", "mappings": {"default": {"default": "square with upper right diagonal half black"}}, "key": "2B14"}, {"category": "So", "mappings": {"default": {"default": "square with lower left diagonal half black"}}, "key": "2B15"}, {"category": "So", "mappings": {"default": {"default": "diamond with left half black"}}, "key": "2B16"}, {"category": "So", "mappings": {"default": {"default": "diamond with right half black"}}, "key": "2B17"}, {"category": "So", "mappings": {"default": {"default": "diamond with top half black"}}, "key": "2B18"}, {"category": "So", "mappings": {"default": {"default": "diamond with bottom half black"}}, "key": "2B19"}, {"category": "So", "mappings": {"default": {"default": "dotted square"}}, "key": "2B1A"}, {"category": "So", "mappings": {"default": {"default": "black large square"}}, "key": "2B1B"}, {"category": "So", "mappings": {"default": {"default": "white large square"}}, "key": "2B1C"}, {"category": "So", "mappings": {"default": {"default": "black very small square"}}, "key": "2B1D"}, {"category": "So", "mappings": {"default": {"default": "white very small square"}}, "key": "2B1E"}, {"category": "So", "mappings": {"default": {"default": "black pentagon"}}, "key": "2B1F"}, {"category": "So", "mappings": {"default": {"default": "white pentagon"}}, "key": "2B20"}, {"category": "So", "mappings": {"default": {"default": "white hexagon"}}, "key": "2B21"}, {"category": "So", "mappings": {"default": {"default": "black hexagon"}}, "key": "2B22"}, {"category": "So", "mappings": {"default": {"default": "horizontal black hexagon"}}, "key": "2B23"}, {"category": "So", "mappings": {"default": {"default": "black large circle"}}, "key": "2B24"}, {"category": "So", "mappings": {"default": {"default": "black medium diamond"}}, "key": "2B25"}, {"category": "So", "mappings": {"default": {"default": "white medium diamond"}}, "key": "2B26"}, {"category": "So", "mappings": {"default": {"default": "black medium lozenge"}}, "key": "2B27"}, {"category": "So", "mappings": {"default": {"default": "white medium lozenge"}}, "key": "2B28"}, {"category": "So", "mappings": {"default": {"default": "black small diamond"}}, "key": "2B29"}, {"category": "So", "mappings": {"default": {"default": "black small lozenge"}}, "key": "2B2A"}, {"category": "So", "mappings": {"default": {"default": "white small lozenge"}}, "key": "2B2B"}, {"category": "So", "mappings": {"default": {"default": "black horizontal ellipse"}}, "key": "2B2C"}, {"category": "So", "mappings": {"default": {"default": "white horizontal ellipse"}}, "key": "2B2D"}, {"category": "So", "mappings": {"default": {"default": "black vertical ellipse"}}, "key": "2B2E"}, {"category": "So", "mappings": {"default": {"default": "white vertical ellipse"}}, "key": "2B2F"}, {"category": "So", "mappings": {"default": {"default": "white medium star"}}, "key": "2B50"}, {"category": "So", "mappings": {"default": {"default": "black small star"}}, "key": "2B51"}, {"category": "So", "mappings": {"default": {"default": "white small star"}}, "key": "2B52"}, {"category": "So", "mappings": {"default": {"default": "black right pointing pentagon"}}, "key": "2B53"}, {"category": "So", "mappings": {"default": {"default": "white right pointing pentagon"}}, "key": "2B54"}, {"category": "So", "mappings": {"default": {"default": "heavy large circle"}}, "key": "2B55"}, {"category": "So", "mappings": {"default": {"default": "heavy oval with oval inside"}}, "key": "2B56"}, {"category": "So", "mappings": {"default": {"default": "heavy circle with circle inside"}}, "key": "2B57"}, {"category": "So", "mappings": {"default": {"default": "heavy circle"}}, "key": "2B58"}, {"category": "So", "mappings": {"default": {"default": "heavy circled saltire"}}, "key": "2B59"}], "en/symbols/math_harpoons.js": [{"locale": "en"}, {"category": "So", "mappings": {"default": {"default": "left harpoon with barb up"}}, "key": "21BC"}, {"category": "So", "mappings": {"default": {"default": "left harpoon with barb down"}}, "key": "21BD"}, {"category": "So", "mappings": {"default": {"default": "up harpoon with barb right"}}, "key": "21BE"}, {"category": "So", "mappings": {"default": {"default": "up harpoon with barb left"}}, "key": "21BF"}, {"category": "So", "mappings": {"default": {"default": "right harpoon with barb up"}}, "key": "21C0"}, {"category": "So", "mappings": {"default": {"default": "right harpoon with barb down"}}, "key": "21C1"}, {"category": "So", "mappings": {"default": {"default": "down harpoon with barb right"}}, "key": "21C2"}, {"category": "So", "mappings": {"default": {"default": "down harpoon with barb left"}}, "key": "21C3"}, {"category": "So", "mappings": {"default": {"default": "left harpoon over right harpoon"}}, "key": "21CB"}, {"category": "So", "mappings": {"default": {"default": "right harpoon over left harpoon"}}, "key": "21CC"}, {"category": "Sm", "mappings": {"default": {"default": "left barb up right barb down harpoon"}}, "key": "294A"}, {"category": "Sm", "mappings": {"default": {"default": "left barb down right barb up harpoon"}}, "key": "294B"}, {"category": "Sm", "mappings": {"default": {"default": "up barb right down barb left harpoon"}}, "key": "294C"}, {"category": "Sm", "mappings": {"default": {"default": "up barb left down barb right harpoon"}}, "key": "294D"}, {"category": "Sm", "mappings": {"default": {"default": "left barb up right barb up harpoon"}}, "key": "294E"}, {"category": "Sm", "mappings": {"default": {"default": "up barb right down barb right harpoon"}}, "key": "294F"}, {"category": "Sm", "mappings": {"default": {"default": "left barb down right barb down harpoon"}}, "key": "2950"}, {"category": "Sm", "mappings": {"default": {"default": "up barb left down barb left harpoon"}}, "key": "2951"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb up to bar"}}, "key": "2952"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb up to bar"}}, "key": "2953"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb right to bar"}}, "key": "2954"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb right to bar"}}, "key": "2955"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb down to bar"}}, "key": "2956"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb down to bar"}}, "key": "2957"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb left to bar"}}, "key": "2958"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb left to bar"}}, "key": "2959"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb up from bar"}}, "key": "295A"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb up from bar"}}, "key": "295B"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb right from bar"}}, "key": "295C"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb right from bar"}}, "key": "295D"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb down from bar"}}, "key": "295E"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb down from bar"}}, "key": "295F"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb left from bar"}}, "key": "2960"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb left from bar"}}, "key": "2961"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb up above left harpoon with barb down"}}, "key": "2962"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb left beside up harpoon with barb right"}}, "key": "2963"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb up above right harpoon with barb down"}}, "key": "2964"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb left beside down harpoon with barb right"}}, "key": "2965"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb up above right harpoon with barb up"}}, "key": "2966"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb down above right harpoon with barb down"}}, "key": "2967"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb up above left harpoon with barb up"}}, "key": "2968"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb down above left harpoon with barb down"}}, "key": "2969"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb up above long dash"}}, "key": "296A"}, {"category": "Sm", "mappings": {"default": {"default": "left harpoon with barb down below long dash"}}, "key": "296B"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb up above long dash"}}, "key": "296C"}, {"category": "Sm", "mappings": {"default": {"default": "right harpoon with barb down below long dash"}}, "key": "296D"}, {"category": "Sm", "mappings": {"default": {"default": "up harpoon with barb left beside down harpoon with barb right"}}, "key": "296E"}, {"category": "Sm", "mappings": {"default": {"default": "down harpoon with barb left beside up harpoon with barb right"}}, "key": "296F"}, {"category": "Sm", "mappings": {"default": {"default": "left fish tail"}}, "key": "297C"}, {"category": "Sm", "mappings": {"default": {"default": "right fish tail"}}, "key": "297D"}, {"category": "Sm", "mappings": {"default": {"default": "up fish tail"}}, "key": "297E"}, {"category": "Sm", "mappings": {"default": {"default": "down fish tail"}}, "key": "297F"}], "en/symbols/math_non_characters.js": [{"locale": "en"}, {"category": "Ll", "mappings": {"default": {"default": "italic h over two pi", "physics": "planck constant over two pi"}}, "key": "210F"}, {"category": "So", "mappings": {"default": {"default": "l b bar"}}, "key": "2114"}, {"category": "So", "mappings": {"default": {"default": "numero"}}, "key": "2116"}, {"category": "So", "mappings": {"default": {"default": "sound recording copyright"}}, "key": "2117"}, {"category": "So", "mappings": {"default": {"default": "prescription take"}}, "key": "211E"}, {"category": "So", "mappings": {"default": {"default": "response"}}, "key": "211F"}, {"category": "So", "mappings": {"default": {"default": "service mark"}}, "key": "2120"}, {"category": "So", "mappings": {"default": {"default": "telephone sign", "alternative": "t e l symbol"}}, "key": "2121"}, {"category": "So", "mappings": {"default": {"default": "trade mark"}}, "key": "2122"}, {"category": "So", "mappings": {"default": {"default": "versicle"}}, "key": "2123"}, {"category": "So", "mappings": {"default": {"default": "ounce"}}, "key": "2125"}, {"category": "<PERSON>", "mappings": {"default": {"default": "ohm"}}, "key": "2126"}, {"category": "So", "mappings": {"default": {"default": "inverted ohm"}}, "key": "2127"}, {"category": "<PERSON>", "mappings": {"default": {"default": "kelvin"}}, "key": "212A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "angstrom"}}, "key": "212B"}, {"category": "So", "mappings": {"default": {"default": "estimated"}}, "key": "212E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "turned cap F"}, "mathspeak": {"default": "turned upper F"}}, "key": "2132"}, {"category": "Ll", "mappings": {"default": {"default": "information source"}}, "key": "2139"}, {"category": "So", "mappings": {"default": {"default": "rotated cap Q"}, "mathspeak": {"default": "rotated upper Q"}}, "key": "213A"}, {"category": "So", "mappings": {"default": {"default": "facsimile sign"}}, "key": "213B"}, {"category": "Sm", "mappings": {"default": {"default": "turned sans serif cap G"}, "mathspeak": {"default": "turned sans serif upper G"}}, "key": "2141"}, {"category": "Sm", "mappings": {"default": {"default": "turned sans serif cap L"}, "mathspeak": {"default": "turned sans serif upper L"}}, "key": "2142"}, {"category": "Sm", "mappings": {"default": {"default": "reversed sans serif cap L"}, "mathspeak": {"default": "reversed sans serif upper L"}}, "key": "2143"}, {"category": "Sm", "mappings": {"default": {"default": "turned sans serif cap Y"}, "mathspeak": {"default": "turned sans serif upper Y"}}, "key": "2144"}], "en/symbols/math_symbols.js": [{"locale": "en"}, {"category": "Po", "mappings": {"default": {"default": "exclamation mark"}}, "key": "0021"}, {"category": "Po", "mappings": {"default": {"default": "quotation mark"}}, "key": "0022"}, {"category": "Po", "mappings": {"default": {"default": "number sign", "alternative": "hash"}, "mathspeak": {"brief": "num sign", "sbrief": "num sign"}}, "key": "0023"}, {"category": "Sc", "mappings": {"default": {"default": "dollar sign"}}, "key": "0024"}, {"category": "Po", "mappings": {"default": {"default": "percent sign"}}, "key": "0025"}, {"category": "Po", "mappings": {"default": {"default": "ampersand"}}, "key": "0026"}, {"category": "Po", "mappings": {"default": {"default": "prime"}}, "key": "0027"}, {"category": "Po", "mappings": {"default": {"default": "asterisk"}}, "key": "002A"}, {"category": "Sm", "mappings": {"default": {"default": "plus"}}, "key": "002B"}, {"category": "Po", "mappings": {"default": {"default": "comma"}}, "key": "002C"}, {"category": "Pd", "mappings": {"default": {"default": "minus"}, "mathspeak": {"default": "hyphen"}}, "key": "002D"}, {"category": "Po", "mappings": {"default": {"default": "period"}}, "key": "002E"}, {"category": "Po", "mappings": {"default": {"default": "slash", "alternative": "solidus"}, "emacspeak": {"default": "slash"}}, "key": "002F"}, {"category": "Po", "mappings": {"default": {"default": "colon"}}, "key": "003A"}, {"category": "Po", "mappings": {"default": {"default": "semicolon"}}, "key": "003B"}, {"category": "Sm", "mappings": {"default": {"default": "less than"}, "clearspeak": {"default": "is less than"}}, "key": "003C"}, {"category": "Sm", "mappings": {"default": {"default": "equals"}}, "key": "003D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than"}, "clearspeak": {"default": "is greater than"}}, "key": "003E"}, {"category": "Po", "mappings": {"default": {"default": "question mark"}}, "key": "003F"}, {"category": "Po", "mappings": {"default": {"default": "at"}}, "key": "0040"}, {"category": "Po", "mappings": {"default": {"default": "backslash"}}, "key": "005C"}, {"category": "Sk", "mappings": {"default": {"default": "hat"}, "mathspeak": {"default": "caret"}}, "key": "005E"}, {"category": "Pc", "mappings": {"default": {"default": "bar", "alternative": "underline"}}, "key": "005F"}, {"category": "Sk", "mappings": {"default": {"default": "grave"}, "mathspeak": {"default": "grave"}}, "key": "0060"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar"}}, "key": "007C"}, {"category": "Sm", "mappings": {"default": {"default": "tilde"}}, "key": "007E"}, {"category": "Po", "mappings": {"default": {"default": "inverted exclamation mark"}}, "key": "00A1"}, {"category": "Sc", "mappings": {"default": {"default": "cent sign"}}, "key": "00A2"}, {"category": "Sc", "mappings": {"default": {"default": "pound sign"}}, "key": "00A3"}, {"category": "Sc", "mappings": {"default": {"default": "currency sign"}}, "key": "00A4"}, {"category": "Sc", "mappings": {"default": {"default": "yen sign"}}, "key": "00A5"}, {"category": "So", "mappings": {"default": {"default": "broken vertical bar"}}, "key": "00A6"}, {"category": "Po", "mappings": {"default": {"default": "section sign"}}, "key": "00A7"}, {"category": "Sk", "mappings": {"default": {"default": "two dots"}}, "key": "00A8"}, {"category": "So", "mappings": {"default": {"default": "copyright sign"}}, "key": "00A9"}, {"category": "Lo", "mappings": {"default": {"default": "feminine ordinal indicator"}}, "key": "00AA"}, {"category": "Pi", "mappings": {"default": {"default": "left pointing guillemet"}}, "key": "00AB"}, {"category": "Sm", "mappings": {"default": {"default": "not sign"}}, "key": "00AC"}, {"category": "So", "mappings": {"default": {"default": "registered sign"}, "mathspeak": {"default": "registered trade mark sign"}, "clearspeak": {"default": "trade mark sign"}}, "key": "00AE"}, {"category": "Sk", "mappings": {"default": {"default": "bar"}}, "key": "00AF"}, {"category": "So", "mappings": {"default": {"default": "degree"}, "clearspeak": {"default": "degrees"}}, "key": "00B0"}, {"category": "Sm", "mappings": {"default": {"default": "plus or minus"}}, "key": "00B1"}, {"category": "Sk", "mappings": {"default": {"default": "acute"}}, "key": "00B4"}, {"category": "Ll", "mappings": {"default": {"default": "micro sign"}}, "key": "00B5"}, {"category": "Po", "mappings": {"default": {"default": "paragraph sign"}}, "key": "00B6"}, {"category": "Po", "mappings": {"default": {"default": "dot"}, "clearspeak": {"default": "times"}}, "key": "00B7"}, {"category": "Sk", "mappings": {"default": {"default": "cedilla"}}, "key": "00B8"}, {"category": "Lo", "mappings": {"default": {"default": "masculine ordinal indicator"}}, "key": "00BA"}, {"category": "Pf", "mappings": {"default": {"default": "right pointing guillemet"}}, "key": "00BB"}, {"category": "Po", "mappings": {"default": {"default": "inverted question mark"}}, "key": "00BF"}, {"category": "Sm", "mappings": {"default": {"default": "times"}, "clearspeak": {"MultsymbolX_By": "by", "MultsymbolX_Cross": "cross"}}, "key": "00D7"}, {"category": "Sm", "mappings": {"default": {"default": "division sign"}}, "key": "00F7"}, {"category": "Sk", "mappings": {"default": {"default": "breve"}}, "key": "02D8"}, {"category": "Sk", "mappings": {"default": {"default": "dot above"}, "mathspeak": {"default": "dot"}}, "key": "02D9"}, {"category": "Sk", "mappings": {"default": {"default": "ring above"}}, "key": "02DA"}, {"category": "Sk", "mappings": {"default": {"default": "ogonek"}}, "key": "02DB"}, {"category": "Sk", "mappings": {"default": {"default": "tilde"}}, "key": "02DC"}, {"category": "Sk", "mappings": {"default": {"default": "double acute"}}, "key": "02DD"}, {"category": "Pd", "mappings": {"default": {"default": "hyphen"}}, "key": "2010"}, {"category": "Pd", "mappings": {"default": {"default": "non breaking hyphen"}}, "key": "2011"}, {"category": "Pd", "mappings": {"default": {"default": "figure dash"}}, "key": "2012"}, {"category": "Pd", "mappings": {"default": {"default": "en dash"}}, "key": "2013"}, {"category": "Pd", "mappings": {"default": {"default": "em dash"}}, "key": "2014"}, {"category": "Pd", "mappings": {"default": {"default": "horizontal bar"}, "mathspeak": {"default": "quotation dash"}}, "key": "2015"}, {"category": "Po", "mappings": {"default": {"default": "double vertical bar"}}, "key": "2016"}, {"category": "Po", "mappings": {"default": {"default": "double underline"}}, "key": "2017"}, {"category": "Pi", "mappings": {"default": {"default": "left single quotation mark"}}, "key": "2018"}, {"category": "Pf", "mappings": {"default": {"default": "right single quotation mark"}}, "key": "2019"}, {"category": "Ps", "mappings": {"default": {"default": "low right single quotation mark"}}, "key": "201A"}, {"category": "Pi", "mappings": {"default": {"default": "left reversed single quotation mark"}}, "key": "201B"}, {"category": "Pi", "mappings": {"default": {"default": "left double quotation mark"}}, "key": "201C"}, {"category": "Pf", "mappings": {"default": {"default": "right double quotation mark"}}, "key": "201D"}, {"category": "Ps", "mappings": {"default": {"default": "low right double quotation mark"}}, "key": "201E"}, {"category": "Pi", "mappings": {"default": {"default": "left reversed double quotation mark"}}, "key": "201F"}, {"category": "Po", "mappings": {"default": {"default": "dagger"}}, "key": "2020"}, {"category": "Po", "mappings": {"default": {"default": "double dagger"}}, "key": "2021"}, {"category": "Po", "mappings": {"default": {"default": "bullet"}}, "key": "2022"}, {"category": "Po", "mappings": {"default": {"default": "triangular bullet"}}, "key": "2023"}, {"category": "Po", "mappings": {"default": {"default": "one dot leader"}}, "key": "2024"}, {"category": "Po", "mappings": {"default": {"default": "two dot leader"}}, "key": "2025"}, {"category": "Po", "mappings": {"default": {"default": "ellipsis"}, "clearspeak": {"default": "dot dot dot"}}, "key": "2026"}, {"category": "Po", "mappings": {"default": {"default": "hyphenation point"}}, "key": "2027"}, {"category": "Po", "mappings": {"default": {"default": "per mille"}}, "key": "2030"}, {"category": "Po", "mappings": {"default": {"default": "per ten thousand"}}, "key": "2031"}, {"category": "Po", "mappings": {"default": {"default": "prime"}}, "key": "2032"}, {"category": "Po", "mappings": {"default": {"default": "double prime"}}, "key": "2033"}, {"category": "Po", "mappings": {"default": {"default": "triple prime"}}, "key": "2034"}, {"category": "Po", "mappings": {"default": {"default": "reversed prime"}}, "key": "2035"}, {"category": "Po", "mappings": {"default": {"default": "reversed double prime"}}, "key": "2036"}, {"category": "Po", "mappings": {"default": {"default": "reversed triple prime"}}, "key": "2037"}, {"category": "Po", "mappings": {"default": {"default": "caret"}}, "key": "2038"}, {"category": "Pi", "mappings": {"default": {"default": "left pointing single guillemet"}}, "key": "2039"}, {"category": "Pf", "mappings": {"default": {"default": "right pointing single guillemet"}}, "key": "203A"}, {"category": "Po", "mappings": {"default": {"default": "reference mark"}}, "key": "203B"}, {"category": "Po", "mappings": {"default": {"default": "double exclamation mark"}}, "key": "203C"}, {"category": "Po", "mappings": {"default": {"default": "interrobang"}}, "key": "203D"}, {"category": "Po", "mappings": {"default": {"default": "overline"}, "mathspeak": {"default": "bar"}}, "key": "203E"}, {"category": "Pc", "mappings": {"default": {"default": "undertie"}}, "key": "203F"}, {"category": "Pc", "mappings": {"default": {"default": "character tie"}}, "key": "2040"}, {"category": "Po", "mappings": {"default": {"default": "caret insertion point"}}, "key": "2041"}, {"category": "Po", "mappings": {"default": {"default": "asterism"}}, "key": "2042"}, {"category": "Po", "mappings": {"default": {"default": "hyphen bullet"}}, "key": "2043"}, {"category": "Sm", "mappings": {"default": {"default": "fraction slash"}}, "key": "2044"}, {"category": "Po", "mappings": {"default": {"default": "double question mark"}}, "key": "2047"}, {"category": "Po", "mappings": {"default": {"default": "question exclamation mark"}}, "key": "2048"}, {"category": "Po", "mappings": {"default": {"default": "exclamation question mark"}}, "key": "2049"}, {"category": "Po", "mappings": {"default": {"default": "reversed pilcrow"}}, "key": "204B"}, {"category": "Po", "mappings": {"default": {"default": "black leftwards bullet"}}, "key": "204C"}, {"category": "Po", "mappings": {"default": {"default": "black rightwards bullet"}}, "key": "204D"}, {"category": "Po", "mappings": {"default": {"default": "low asterisk"}}, "key": "204E"}, {"category": "Po", "mappings": {"default": {"default": "reversed semicolon"}}, "key": "204F"}, {"category": "Po", "mappings": {"default": {"default": "close up"}}, "key": "2050"}, {"category": "Po", "mappings": {"default": {"default": "two asterisks aligned vertically"}}, "key": "2051"}, {"category": "Sm", "mappings": {"default": {"default": "commercial minus"}}, "key": "2052"}, {"category": "Po", "mappings": {"default": {"default": "swung dash"}}, "key": "2053"}, {"category": "Pc", "mappings": {"default": {"default": "inverted undertie"}}, "key": "2054"}, {"category": "Po", "mappings": {"default": {"default": "flower punctuation mark"}}, "key": "2055"}, {"category": "Po", "mappings": {"default": {"default": "three dot punctuation"}}, "key": "2056"}, {"category": "Po", "mappings": {"default": {"default": "quadruple prime"}}, "key": "2057"}, {"category": "Po", "mappings": {"default": {"default": "four dot punctuation"}}, "key": "2058"}, {"category": "Po", "mappings": {"default": {"default": "five dot punctuation"}}, "key": "2059"}, {"category": "Po", "mappings": {"default": {"default": "two dot punctuation"}}, "key": "205A"}, {"category": "Po", "mappings": {"default": {"default": "four dot mark"}}, "key": "205B"}, {"category": "Po", "mappings": {"default": {"default": "dotted cross"}}, "key": "205C"}, {"category": "Po", "mappings": {"default": {"default": "tricolon"}}, "key": "205D"}, {"category": "Po", "mappings": {"default": {"default": "vertical four dots"}}, "key": "205E"}, {"category": "Sm", "mappings": {"default": {"default": "superscript plus"}}, "key": "207A"}, {"category": "Sm", "mappings": {"default": {"default": "superscript minus"}}, "key": "207B"}, {"category": "Sm", "mappings": {"default": {"default": "superscript equals"}}, "key": "207C"}, {"category": "Ps", "mappings": {"default": {"default": "superscript left parenthesis"}}, "key": "207D"}, {"category": "Pe", "mappings": {"default": {"default": "superscript right parenthesis"}}, "key": "207E"}, {"category": "Sm", "mappings": {"default": {"default": "subscript plus"}}, "key": "208A"}, {"category": "Sm", "mappings": {"default": {"default": "subscript minus"}}, "key": "208B"}, {"category": "Sm", "mappings": {"default": {"default": "subscript equals"}}, "key": "208C"}, {"category": "Ps", "mappings": {"default": {"default": "subscript left parenthesis"}}, "key": "208D"}, {"category": "Pe", "mappings": {"default": {"default": "subscript right parenthesis"}}, "key": "208E"}, {"category": "So", "mappings": {"default": {"default": "property line"}}, "key": "214A"}, {"category": "Sm", "mappings": {"default": {"default": "turned ampersand"}}, "key": "214B"}, {"category": "So", "mappings": {"default": {"default": "per"}}, "key": "214C"}, {"category": "So", "mappings": {"default": {"default": "aktieselskab"}}, "key": "214D"}, {"category": "Ll", "mappings": {"default": {"default": "turned small f"}}, "key": "214E"}, {"category": "Sm", "mappings": {"default": {"default": "for all"}}, "key": "2200"}, {"category": "Sm", "mappings": {"default": {"default": "complement"}}, "key": "2201"}, {"category": "Sm", "mappings": {"default": {"default": "there exists"}}, "key": "2203"}, {"category": "Sm", "mappings": {"default": {"default": "there does not exist"}}, "key": "2204"}, {"category": "Sm", "mappings": {"default": {"default": "empty set"}}, "key": "2205"}, {"category": "Sm", "mappings": {"default": {"default": "increment"}}, "key": "2206"}, {"category": "Sm", "mappings": {"default": {"default": "element of"}, "clearspeak": {"default": "is a member of", "SetMemberSymbol_Member": "is a member of", "SetMemberSymbol_Element": "is an element of", "SetMemberSymbol_In": "is in", "SetMemberSymbol_Belongs": "belongs to"}}, "key": "2208"}, {"category": "Sm", "mappings": {"default": {"default": "not an element of"}, "clearspeak": {"default": "is not a member of", "SetMemberSymbol_Member": "is not a member of", "SetMemberSymbol_Element": "is not an element of", "SetMemberSymbol_In": "is not in", "SetMemberSymbol_Belongs": "does not belong to"}}, "key": "2209"}, {"category": "Sm", "mappings": {"default": {"default": "small element of"}, "clearspeak": {"default": "is a member of", "SetMemberSymbol_Member": "is a member of", "SetMemberSymbol_Element": "is an element of", "SetMemberSymbol_In": "is in", "SetMemberSymbol_Belongs": "belongs to"}}, "key": "220A"}, {"category": "Sm", "mappings": {"default": {"default": "contains as member"}}, "key": "220B"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as member"}}, "key": "220C"}, {"category": "Sm", "mappings": {"default": {"default": "small contains as member"}}, "key": "220D"}, {"category": "Sm", "mappings": {"default": {"default": "end of proof"}}, "key": "220E"}, {"category": "Sm", "mappings": {"default": {"default": "product"}}, "key": "220F"}, {"category": "Sm", "mappings": {"default": {"default": "coproduct"}}, "key": "2210"}, {"category": "Sm", "mappings": {"default": {"default": "sum"}, "mathspeak": {"default": "sigma summation"}}, "key": "2211"}, {"category": "Sm", "mappings": {"default": {"default": "minus"}}, "key": "2212"}, {"category": "Sm", "mappings": {"default": {"default": "minus or plus"}}, "key": "2213"}, {"category": "Sm", "mappings": {"default": {"default": "dot plus"}}, "key": "2214"}, {"category": "Sm", "mappings": {"default": {"default": "division slash"}}, "key": "2215"}, {"category": "Sm", "mappings": {"default": {"default": "set minus"}}, "key": "2216"}, {"category": "Sm", "mappings": {"default": {"default": "asterisk"}}, "key": "2217"}, {"category": "Sm", "mappings": {"default": {"default": "ring"}, "clearspeak": {"default": "composed with"}}, "key": "2218"}, {"category": "Sm", "mappings": {"default": {"default": "bullet"}}, "key": "2219"}, {"category": "Sm", "mappings": {"default": {"default": "square root"}}, "key": "221A"}, {"category": "Sm", "mappings": {"default": {"default": "cube root"}}, "key": "221B"}, {"category": "Sm", "mappings": {"default": {"default": "fourth root"}}, "key": "221C"}, {"category": "Sm", "mappings": {"default": {"default": "proportional to"}}, "key": "221D"}, {"category": "Sm", "mappings": {"default": {"default": "infinity"}}, "key": "221E"}, {"category": "Sm", "mappings": {"default": {"default": "right angle"}}, "key": "221F"}, {"category": "Sm", "mappings": {"default": {"default": "angle"}}, "key": "2220"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle"}}, "key": "2221"}, {"category": "Sm", "mappings": {"default": {"default": "spherical angle"}}, "key": "2222"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar", "alternative": "divides"}}, "key": "2223"}, {"category": "Sm", "mappings": {"default": {"default": "does not divide"}}, "key": "2224"}, {"category": "Sm", "mappings": {"default": {"default": "parallel to"}}, "key": "2225"}, {"category": "Sm", "mappings": {"default": {"default": "not parallel to"}}, "key": "2226"}, {"category": "Sm", "mappings": {"default": {"default": "and"}}, "key": "2227"}, {"category": "Sm", "mappings": {"default": {"default": "or"}}, "key": "2228"}, {"category": "Sm", "mappings": {"default": {"default": "intersection"}}, "key": "2229"}, {"category": "Sm", "mappings": {"default": {"default": "union"}}, "key": "222A"}, {"category": "Sm", "mappings": {"default": {"default": "integral"}}, "key": "222B"}, {"category": "Sm", "mappings": {"default": {"default": "double integral"}}, "key": "222C"}, {"category": "Sm", "mappings": {"default": {"default": "triple integral"}}, "key": "222D"}, {"category": "Sm", "mappings": {"default": {"default": "contour integral"}}, "key": "222E"}, {"category": "Sm", "mappings": {"default": {"default": "surface integral"}}, "key": "222F"}, {"category": "Sm", "mappings": {"default": {"default": "volume integral"}}, "key": "2230"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise integral"}}, "key": "2231"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise contour integral"}}, "key": "2232"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise contour integral"}}, "key": "2233"}, {"category": "Sm", "mappings": {"default": {"default": "therefore"}}, "key": "2234"}, {"category": "Sm", "mappings": {"default": {"default": "because"}}, "key": "2235"}, {"category": "Sm", "mappings": {"default": {"default": "ratio"}}, "key": "2236"}, {"category": "Sm", "mappings": {"default": {"default": "proportion"}}, "key": "2237"}, {"category": "Sm", "mappings": {"default": {"default": "dot minus"}}, "key": "2238"}, {"category": "Sm", "mappings": {"default": {"default": "excess"}}, "key": "2239"}, {"category": "Sm", "mappings": {"default": {"default": "geometric proportion"}}, "key": "223A"}, {"category": "Sm", "mappings": {"default": {"default": "homothetic"}}, "key": "223B"}, {"category": "Sm", "mappings": {"default": {"default": "tilde"}}, "key": "223C"}, {"category": "Sm", "mappings": {"default": {"default": "reversed tilde"}}, "key": "223D"}, {"category": "Sm", "mappings": {"default": {"default": "inverted lazy s"}}, "key": "223E"}, {"category": "Sm", "mappings": {"default": {"default": "sine wave"}}, "key": "223F"}, {"category": "Sm", "mappings": {"default": {"default": "wreath product"}}, "key": "2240"}, {"category": "Sm", "mappings": {"default": {"default": "not tilde"}}, "key": "2241"}, {"category": "Sm", "mappings": {"default": {"default": "minus tilde"}}, "key": "2242"}, {"category": "Sm", "mappings": {"default": {"default": "asymptotically equals"}}, "key": "2243"}, {"category": "Sm", "mappings": {"default": {"default": "not asymptotically equals"}}, "key": "2244"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equals"}}, "key": "2245"}, {"category": "Sm", "mappings": {"default": {"default": "approximately but not actually equals"}}, "key": "2246"}, {"category": "Sm", "mappings": {"default": {"default": "neither approximately nor actually equals"}}, "key": "2247"}, {"category": "Sm", "mappings": {"default": {"default": "almost equals"}}, "key": "2248"}, {"category": "Sm", "mappings": {"default": {"default": "not almost equals"}}, "key": "2249"}, {"category": "Sm", "mappings": {"default": {"default": "almost equal or equals"}}, "key": "224A"}, {"category": "Sm", "mappings": {"default": {"default": "triple tilde"}}, "key": "224B"}, {"category": "Sm", "mappings": {"default": {"default": "all equals"}}, "key": "224C"}, {"category": "Sm", "mappings": {"default": {"default": "equivalent to"}}, "key": "224D"}, {"category": "Sm", "mappings": {"default": {"default": "geometrically equivalent to"}}, "key": "224E"}, {"category": "Sm", "mappings": {"default": {"default": "difference between"}}, "key": "224F"}, {"category": "Sm", "mappings": {"default": {"default": "approaches the limit"}}, "key": "2250"}, {"category": "Sm", "mappings": {"default": {"default": "geometrically equals"}}, "key": "2251"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equals or the image of"}}, "key": "2252"}, {"category": "Sm", "mappings": {"default": {"default": "image of or approximately equals"}}, "key": "2253"}, {"category": "Sm", "mappings": {"default": {"default": "colon equals"}}, "key": "2254"}, {"category": "Sm", "mappings": {"default": {"default": "equals colon"}}, "key": "2255"}, {"category": "Sm", "mappings": {"default": {"default": "ring in equals"}}, "key": "2256"}, {"category": "Sm", "mappings": {"default": {"default": "ring equals"}}, "key": "2257"}, {"category": "Sm", "mappings": {"default": {"default": "corresponds to"}}, "key": "2258"}, {"category": "Sm", "mappings": {"default": {"default": "estimates"}}, "key": "2259"}, {"category": "Sm", "mappings": {"default": {"default": "equiangular to"}, "clearspeak": {"default": "is equiangular to"}}, "key": "225A"}, {"category": "Sm", "mappings": {"default": {"default": "star equals"}}, "key": "225B"}, {"category": "Sm", "mappings": {"default": {"default": "delta equals"}}, "key": "225C"}, {"category": "Sm", "mappings": {"default": {"default": "equals by definition"}, "clearspeak": {"default": "is defined to be"}}, "key": "225D"}, {"category": "Sm", "mappings": {"default": {"default": "measured by"}, "clearspeak": {"default": "is measured by"}}, "key": "225E"}, {"category": "Sm", "mappings": {"default": {"default": "questioned equals"}}, "key": "225F"}, {"category": "Sm", "mappings": {"default": {"default": "not equals"}, "clearspeak": {"default": "is not equal to"}}, "key": "2260"}, {"category": "Sm", "mappings": {"default": {"default": "identical to"}, "clearspeak": {"default": "is identical to"}}, "key": "2261"}, {"category": "Sm", "mappings": {"default": {"default": "not identical to"}, "clearspeak": {"default": "is not identical to"}}, "key": "2262"}, {"category": "Sm", "mappings": {"default": {"default": "strictly equivalent to"}, "clearspeak": {"default": "is strictly equivalent to"}}, "key": "2263"}, {"category": "Sm", "mappings": {"default": {"default": "less than or equals"}, "clearspeak": {"default": "is less than or equal to"}}, "key": "2264"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or equals"}, "clearspeak": {"default": "is greater than or equal to"}}, "key": "2265"}, {"category": "Sm", "mappings": {"default": {"default": "less than over equals"}, "clearspeak": {"default": "is less than over equals"}}, "key": "2266"}, {"category": "Sm", "mappings": {"default": {"default": "greater than over equals"}, "clearspeak": {"default": "is greater than over equals"}}, "key": "2267"}, {"category": "Sm", "mappings": {"default": {"default": "less than but not equals"}, "clearspeak": {"default": "is less than but not equal to"}}, "key": "2268"}, {"category": "Sm", "mappings": {"default": {"default": "greater than but not equals"}, "clearspeak": {"default": "is greater than but not equal to"}}, "key": "2269"}, {"category": "Sm", "mappings": {"default": {"default": "much less than"}, "clearspeak": {"default": "is much less than"}}, "key": "226A"}, {"category": "Sm", "mappings": {"default": {"default": "much greater than"}, "clearspeak": {"default": "is much greater than"}}, "key": "226B"}, {"category": "Sm", "mappings": {"default": {"default": "between"}}, "key": "226C"}, {"category": "Sm", "mappings": {"default": {"default": "not equivalent to"}, "clearspeak": {"default": "is not equivalent to"}}, "key": "226D"}, {"category": "Sm", "mappings": {"default": {"default": "not less than"}, "clearspeak": {"default": "is not less than"}}, "key": "226E"}, {"category": "Sm", "mappings": {"default": {"default": "not greater than"}, "clearspeak": {"default": "is not greater than"}}, "key": "226F"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor equals"}, "clearspeak": {"default": "is neither less than nor equal to"}}, "key": "2270"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor equals"}, "clearspeak": {"default": "is neither greater than nor equal to"}}, "key": "2271"}, {"category": "Sm", "mappings": {"default": {"default": "less than or equivalent to"}, "clearspeak": {"default": "is less than or equivalent to"}}, "key": "2272"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or equivalent to"}, "clearspeak": {"default": "is greater than or equivalent to"}}, "key": "2273"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor equivalent to"}, "clearspeak": {"default": "is neither less than nor equivalent to"}}, "key": "2274"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor equivalent to"}, "clearspeak": {"default": "is neither greater than nor equivalent to"}}, "key": "2275"}, {"category": "Sm", "mappings": {"default": {"default": "less than or greater than"}, "clearspeak": {"default": "is less than or greater than"}}, "key": "2276"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or less than"}, "clearspeak": {"default": "is greater than or less than"}}, "key": "2277"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor greater than"}, "clearspeak": {"default": "is neither less than nor greater than"}}, "key": "2278"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor less than"}, "clearspeak": {"default": "is neither greater than nor less than"}}, "key": "2279"}, {"category": "Sm", "mappings": {"default": {"default": "precedes"}}, "key": "227A"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds"}}, "key": "227B"}, {"category": "Sm", "mappings": {"default": {"default": "precedes or equal to"}}, "key": "227C"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds or equal to"}}, "key": "227D"}, {"category": "Sm", "mappings": {"default": {"default": "precedes or equivalent to"}}, "key": "227E"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds or equivalent to"}}, "key": "227F"}, {"category": "Sm", "mappings": {"default": {"default": "does not precede"}}, "key": "2280"}, {"category": "Sm", "mappings": {"default": {"default": "does not succeed"}}, "key": "2281"}, {"category": "Sm", "mappings": {"default": {"default": "subset of"}}, "key": "2282"}, {"category": "Sm", "mappings": {"default": {"default": "superset of"}}, "key": "2283"}, {"category": "Sm", "mappings": {"default": {"default": "not a subset of"}}, "key": "2284"}, {"category": "Sm", "mappings": {"default": {"default": "not a superset of"}}, "key": "2285"}, {"category": "Sm", "mappings": {"default": {"default": "subset of or equal to"}}, "key": "2286"}, {"category": "Sm", "mappings": {"default": {"default": "superset of or equal to"}}, "key": "2287"}, {"category": "Sm", "mappings": {"default": {"default": "neither a subset of nor equal to"}}, "key": "2288"}, {"category": "Sm", "mappings": {"default": {"default": "neither a superset of nor equal to"}}, "key": "2289"}, {"category": "Sm", "mappings": {"default": {"default": "subset of or not equals"}}, "key": "228A"}, {"category": "Sm", "mappings": {"default": {"default": "superset of or not equals"}}, "key": "228B"}, {"category": "Sm", "mappings": {"default": {"default": "multiset"}}, "key": "228C"}, {"category": "Sm", "mappings": {"default": {"default": "multiset multiplication"}}, "key": "228D"}, {"category": "Sm", "mappings": {"default": {"default": "multiset union"}}, "key": "228E"}, {"category": "Sm", "mappings": {"default": {"default": "square image of"}}, "key": "228F"}, {"category": "Sm", "mappings": {"default": {"default": "square original of"}}, "key": "2290"}, {"category": "Sm", "mappings": {"default": {"default": "square image of or equal to"}}, "key": "2291"}, {"category": "Sm", "mappings": {"default": {"default": "square original of or equal to"}}, "key": "2292"}, {"category": "Sm", "mappings": {"default": {"default": "square cap"}}, "key": "2293"}, {"category": "Sm", "mappings": {"default": {"default": "square cup"}}, "key": "2294"}, {"category": "Sm", "mappings": {"default": {"default": "circled plus"}}, "key": "2295"}, {"category": "Sm", "mappings": {"default": {"default": "circled minus"}}, "key": "2296"}, {"category": "Sm", "mappings": {"default": {"default": "circled times"}}, "key": "2297"}, {"category": "Sm", "mappings": {"default": {"default": "circled division slash"}}, "key": "2298"}, {"category": "Sm", "mappings": {"default": {"default": "circled dot"}}, "key": "2299"}, {"category": "Sm", "mappings": {"default": {"default": "circled ring"}}, "key": "229A"}, {"category": "Sm", "mappings": {"default": {"default": "circled asterisk"}}, "key": "229B"}, {"category": "Sm", "mappings": {"default": {"default": "circled equals"}}, "key": "229C"}, {"category": "Sm", "mappings": {"default": {"default": "circled dash"}}, "key": "229D"}, {"category": "Sm", "mappings": {"default": {"default": "squared plus"}}, "key": "229E"}, {"category": "Sm", "mappings": {"default": {"default": "squared minus"}}, "key": "229F"}, {"category": "Sm", "mappings": {"default": {"default": "squared times"}}, "key": "22A0"}, {"category": "Sm", "mappings": {"default": {"default": "squared dot"}}, "key": "22A1"}, {"category": "Sm", "mappings": {"default": {"default": "right tack"}}, "key": "22A2"}, {"category": "Sm", "mappings": {"default": {"default": "left tack"}}, "key": "22A3"}, {"category": "Sm", "mappings": {"default": {"default": "down tack"}}, "key": "22A4"}, {"category": "Sm", "mappings": {"default": {"default": "up tack"}}, "key": "22A5"}, {"category": "Sm", "mappings": {"default": {"default": "assertion"}}, "key": "22A6"}, {"category": "Sm", "mappings": {"default": {"default": "models"}}, "key": "22A7"}, {"category": "Sm", "mappings": {"default": {"default": "true"}}, "key": "22A8"}, {"category": "Sm", "mappings": {"default": {"default": "forces"}}, "key": "22A9"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar right turnstile"}}, "key": "22AA"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar double right turnstile"}}, "key": "22AB"}, {"category": "Sm", "mappings": {"default": {"default": "does not prove"}}, "key": "22AC"}, {"category": "Sm", "mappings": {"default": {"default": "not true"}}, "key": "22AD"}, {"category": "Sm", "mappings": {"default": {"default": "does not force"}}, "key": "22AE"}, {"category": "Sm", "mappings": {"default": {"default": "negated double vertical bar double right turnstile"}}, "key": "22AF"}, {"category": "Sm", "mappings": {"default": {"default": "precedes under relation"}}, "key": "22B0"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds under relation"}}, "key": "22B1"}, {"category": "Sm", "mappings": {"default": {"default": "normal subgroup of"}}, "key": "22B2"}, {"category": "Sm", "mappings": {"default": {"default": "contains as normal subgroup"}}, "key": "22B3"}, {"category": "Sm", "mappings": {"default": {"default": "normal subgroup of or equal to"}}, "key": "22B4"}, {"category": "Sm", "mappings": {"default": {"default": "contains as normal subgroup or equal to"}}, "key": "22B5"}, {"category": "Sm", "mappings": {"default": {"default": "original of"}}, "key": "22B6"}, {"category": "Sm", "mappings": {"default": {"default": "image of"}}, "key": "22B7"}, {"category": "Sm", "mappings": {"default": {"default": "multimap"}}, "key": "22B8"}, {"category": "Sm", "mappings": {"default": {"default": "hermitian conjugate matrix"}}, "key": "22B9"}, {"category": "Sm", "mappings": {"default": {"default": "intercalate"}}, "key": "22BA"}, {"category": "Sm", "mappings": {"default": {"default": "xor"}}, "key": "22BB"}, {"category": "Sm", "mappings": {"default": {"default": "nand"}}, "key": "22BC"}, {"category": "Sm", "mappings": {"default": {"default": "nor"}}, "key": "22BD"}, {"category": "Sm", "mappings": {"default": {"default": "right triangle"}}, "key": "22BF"}, {"category": "Sm", "mappings": {"default": {"default": "n ary and"}, "mathspeak": {"default": "and"}}, "key": "22C0"}, {"category": "Sm", "mappings": {"default": {"default": "n ary or"}, "mathspeak": {"default": "or"}}, "key": "22C1"}, {"category": "Sm", "mappings": {"default": {"default": "n ary intersection"}, "mathspeak": {"default": "intersection"}}, "key": "22C2"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union"}, "mathspeak": {"default": "union"}}, "key": "22C3"}, {"category": "Sm", "mappings": {"default": {"default": "diamond"}}, "key": "22C4"}, {"category": "Sm", "mappings": {"default": {"default": "dot"}, "clearspeak": {"default": "times", "MultsymbolDot_Dot": "dot"}}, "key": "22C5"}, {"category": "Sm", "mappings": {"default": {"default": "star"}}, "key": "22C6"}, {"category": "Sm", "mappings": {"default": {"default": "division times"}}, "key": "22C7"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie"}}, "key": "22C8"}, {"category": "Sm", "mappings": {"default": {"default": "left normal factor semidirect product"}}, "key": "22C9"}, {"category": "Sm", "mappings": {"default": {"default": "right normal factor semidirect product"}}, "key": "22CA"}, {"category": "Sm", "mappings": {"default": {"default": "left semidirect product"}}, "key": "22CB"}, {"category": "Sm", "mappings": {"default": {"default": "right semidirect product"}}, "key": "22CC"}, {"category": "Sm", "mappings": {"default": {"default": "reversed tilde equals"}}, "key": "22CD"}, {"category": "Sm", "mappings": {"default": {"default": "curly or"}}, "key": "22CE"}, {"category": "Sm", "mappings": {"default": {"default": "curly and"}}, "key": "22CF"}, {"category": "Sm", "mappings": {"default": {"default": "double subset"}}, "key": "22D0"}, {"category": "Sm", "mappings": {"default": {"default": "double superset"}}, "key": "22D1"}, {"category": "Sm", "mappings": {"default": {"default": "double intersection"}}, "key": "22D2"}, {"category": "Sm", "mappings": {"default": {"default": "double union"}}, "key": "22D3"}, {"category": "Sm", "mappings": {"default": {"default": "pitchfork"}}, "key": "22D4"}, {"category": "Sm", "mappings": {"default": {"default": "equal and parallel to"}, "clearspeak": {"default": "is equal and parallel to"}}, "key": "22D5"}, {"category": "Sm", "mappings": {"default": {"default": "less than dot"}, "clearspeak": {"default": "is less than dot"}}, "key": "22D6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than dot"}, "clearspeak": {"default": "is greater than dot"}}, "key": "22D7"}, {"category": "Sm", "mappings": {"default": {"default": "very much less than"}, "clearspeak": {"default": "is very much less than"}}, "key": "22D8"}, {"category": "Sm", "mappings": {"default": {"default": "very much greater than"}, "clearspeak": {"default": "is very much greater than"}}, "key": "22D9"}, {"category": "Sm", "mappings": {"default": {"default": "less than equals or greater than"}, "clearspeak": {"default": "is less than equal to or greater than"}}, "key": "22DA"}, {"category": "Sm", "mappings": {"default": {"default": "greater than equals or less than"}, "clearspeak": {"default": "is greater than equal to or less than"}}, "key": "22DB"}, {"category": "Sm", "mappings": {"default": {"default": "equals or less than"}, "clearspeak": {"default": "is equal to or less than"}}, "key": "22DC"}, {"category": "Sm", "mappings": {"default": {"default": "equals or greater than"}, "clearspeak": {"default": "is equal to or greater than"}}, "key": "22DD"}, {"category": "Sm", "mappings": {"default": {"default": "equals or precedes"}, "clearspeak": {"default": "is equal to or precedes"}}, "key": "22DE"}, {"category": "Sm", "mappings": {"default": {"default": "equals or succeeds"}, "clearspeak": {"default": "is equal to or succeeds"}}, "key": "22DF"}, {"category": "Sm", "mappings": {"default": {"default": "does not precede or equal"}}, "key": "22E0"}, {"category": "Sm", "mappings": {"default": {"default": "does not succeed or equal"}}, "key": "22E1"}, {"category": "Sm", "mappings": {"default": {"default": "not square image of or equals"}, "clearspeak": {"default": "is not square image of or equal to"}}, "key": "22E2"}, {"category": "Sm", "mappings": {"default": {"default": "not square original of or equals"}, "clearspeak": {"default": "is not square original of or equal to"}}, "key": "22E3"}, {"category": "Sm", "mappings": {"default": {"default": "square image of or not equals"}, "clearspeak": {"default": "is square image of or not equal to"}}, "key": "22E4"}, {"category": "Sm", "mappings": {"default": {"default": "square original of or not equals"}, "clearspeak": {"default": "is square original of or not equal to"}}, "key": "22E5"}, {"category": "Sm", "mappings": {"default": {"default": "less than but not equivalent to"}, "clearspeak": {"default": "is less than but not equivalent to"}}, "key": "22E6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than but not equivalent to"}, "clearspeak": {"default": "is greater than but not equivalent to"}}, "key": "22E7"}, {"category": "Sm", "mappings": {"default": {"default": "precedes but not equivalent to"}}, "key": "22E8"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds but not equivalent to"}}, "key": "22E9"}, {"category": "Sm", "mappings": {"default": {"default": "not normal subgroup of"}, "clearspeak": {"default": "is not normal subgroup of"}}, "key": "22EA"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as normal subgroup"}}, "key": "22EB"}, {"category": "Sm", "mappings": {"default": {"default": "not normal subgroup of or equals"}, "clearspeak": {"default": "is not normal subgroup of or equal to"}}, "key": "22EC"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as normal subgroup or equal"}}, "key": "22ED"}, {"category": "Sm", "mappings": {"default": {"default": "vertical ellipsis"}}, "key": "22EE"}, {"category": "Sm", "mappings": {"default": {"default": "midline horizontal ellipsis"}, "clearspeak": {"default": "dot dot dot"}}, "key": "22EF"}, {"category": "Sm", "mappings": {"default": {"default": "up right diagonal ellipsis"}}, "key": "22F0"}, {"category": "Sm", "mappings": {"default": {"default": "down right diagonal ellipsis"}}, "key": "22F1"}, {"category": "Sm", "mappings": {"default": {"default": "element of with long horizontal stroke"}}, "key": "22F2"}, {"category": "Sm", "mappings": {"default": {"default": "element of with vertical bar at end of horizontal stroke"}}, "key": "22F3"}, {"category": "Sm", "mappings": {"default": {"default": "small element of with vertical bar at end of horizontal stroke"}}, "key": "22F4"}, {"category": "Sm", "mappings": {"default": {"default": "element of with dot above"}}, "key": "22F5"}, {"category": "Sm", "mappings": {"default": {"default": "element of with overbar"}}, "key": "22F6"}, {"category": "Sm", "mappings": {"default": {"default": "small element of with overbar"}}, "key": "22F7"}, {"category": "Sm", "mappings": {"default": {"default": "element of with underbar"}}, "key": "22F8"}, {"category": "Sm", "mappings": {"default": {"default": "element of with two horizontal strokes"}}, "key": "22F9"}, {"category": "Sm", "mappings": {"default": {"default": "contains with long horizontal stroke"}}, "key": "22FA"}, {"category": "Sm", "mappings": {"default": {"default": "contains with vertical bar at end of horizontal stroke"}}, "key": "22FB"}, {"category": "Sm", "mappings": {"default": {"default": "small contains with vertical bar at end of horizontal stroke"}}, "key": "22FC"}, {"category": "Sm", "mappings": {"default": {"default": "contains with overbar"}}, "key": "22FD"}, {"category": "Sm", "mappings": {"default": {"default": "small contains with overbar"}}, "key": "22FE"}, {"category": "Sm", "mappings": {"default": {"default": "z notation bag membership"}}, "key": "22FF"}, {"category": "So", "mappings": {"default": {"default": "diameter sign"}}, "key": "2300"}, {"category": "So", "mappings": {"default": {"default": "house"}}, "key": "2302"}, {"category": "So", "mappings": {"default": {"default": "projective"}}, "key": "2305"}, {"category": "So", "mappings": {"default": {"default": "perspective"}}, "key": "2306"}, {"category": "So", "mappings": {"default": {"default": "wavy line"}}, "key": "2307"}, {"category": "So", "mappings": {"default": {"default": "reversed not"}}, "key": "2310"}, {"category": "So", "mappings": {"default": {"default": "square lozenge"}}, "key": "2311"}, {"category": "So", "mappings": {"default": {"default": "arc"}}, "key": "2312"}, {"category": "So", "mappings": {"default": {"default": "segment"}}, "key": "2313"}, {"category": "So", "mappings": {"default": {"default": "sector"}}, "key": "2314"}, {"category": "So", "mappings": {"default": {"default": "bold plus"}}, "key": "2795"}, {"category": "So", "mappings": {"default": {"default": "bold minus"}}, "key": "2796"}, {"category": "So", "mappings": {"default": {"default": "bold division"}}, "key": "2797"}, {"category": "So", "mappings": {"default": {"default": "curly loop"}}, "key": "27B0"}, {"category": "So", "mappings": {"default": {"default": "double curly loop"}}, "key": "27BF"}, {"category": "Sm", "mappings": {"default": {"default": "white triangle containing small white triangle"}}, "key": "27C1"}, {"category": "Sm", "mappings": {"default": {"default": "perpendicular"}}, "key": "27C2"}, {"category": "Sm", "mappings": {"default": {"default": "open subset"}}, "key": "27C3"}, {"category": "Sm", "mappings": {"default": {"default": "open superset"}}, "key": "27C4"}, {"category": "Sm", "mappings": {"default": {"default": "or with dot inside"}}, "key": "27C7"}, {"category": "Sm", "mappings": {"default": {"default": "backslash preceding subset"}}, "key": "27C8"}, {"category": "Sm", "mappings": {"default": {"default": "superset preceding solidus"}}, "key": "27C9"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar with horizontal stroke"}}, "key": "27CA"}, {"category": "Sm", "mappings": {"default": {"default": "rising diagonal"}}, "key": "27CB"}, {"category": "Sm", "mappings": {"default": {"default": "long division"}}, "key": "27CC"}, {"category": "Sm", "mappings": {"default": {"default": "falling diagonal"}}, "key": "27CD"}, {"category": "Sm", "mappings": {"default": {"default": "squared and"}}, "key": "27CE"}, {"category": "Sm", "mappings": {"default": {"default": "squared or"}}, "key": "27CF"}, {"category": "Sm", "mappings": {"default": {"default": "white diamond with centered dot"}}, "key": "27D0"}, {"category": "Sm", "mappings": {"default": {"default": "and with dot"}}, "key": "27D1"}, {"category": "Sm", "mappings": {"default": {"default": "element of opening upwards"}}, "key": "27D2"}, {"category": "Sm", "mappings": {"default": {"default": "lower right corner with dot"}}, "key": "27D3"}, {"category": "Sm", "mappings": {"default": {"default": "upper left corner with dot"}}, "key": "27D4"}, {"category": "Sm", "mappings": {"default": {"default": "left outer join"}}, "key": "27D5"}, {"category": "Sm", "mappings": {"default": {"default": "right outer join"}}, "key": "27D6"}, {"category": "Sm", "mappings": {"default": {"default": "full outer join"}}, "key": "27D7"}, {"category": "Sm", "mappings": {"default": {"default": "large up tack"}}, "key": "27D8"}, {"category": "Sm", "mappings": {"default": {"default": "large down tack"}}, "key": "27D9"}, {"category": "Sm", "mappings": {"default": {"default": "left and right double turnstile"}}, "key": "27DA"}, {"category": "Sm", "mappings": {"default": {"default": "left and right tack"}}, "key": "27DB"}, {"category": "Sm", "mappings": {"default": {"default": "left multimap"}}, "key": "27DC"}, {"category": "Sm", "mappings": {"default": {"default": "long right tack"}}, "key": "27DD"}, {"category": "Sm", "mappings": {"default": {"default": "long left tack"}}, "key": "27DE"}, {"category": "Sm", "mappings": {"default": {"default": "up tack with circle above"}}, "key": "27DF"}, {"category": "Sm", "mappings": {"default": {"default": "lozenge divided by horizontal rule"}}, "key": "27E0"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond"}}, "key": "27E1"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond with leftwards tick"}}, "key": "27E2"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond with rightwards tick"}}, "key": "27E3"}, {"category": "Sm", "mappings": {"default": {"default": "white square with leftwards tick"}}, "key": "27E4"}, {"category": "Sm", "mappings": {"default": {"default": "white square with rightwards tick"}}, "key": "27E5"}, {"category": "Sm", "mappings": {"default": {"default": "rising diagonal crossing falling diagonal"}}, "key": "292B"}, {"category": "Sm", "mappings": {"default": {"default": "falling diagonal crossing rising diagonal"}}, "key": "292C"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar delimiter"}}, "key": "2980"}, {"category": "Sm", "mappings": {"default": {"default": "z notation spot"}}, "key": "2981"}, {"category": "Sm", "mappings": {"default": {"default": "z notation type colon"}}, "key": "2982"}, {"category": "Sm", "mappings": {"default": {"default": "dotted fence"}}, "key": "2999"}, {"category": "Sm", "mappings": {"default": {"default": "vertical zigzag line"}}, "key": "299A"}, {"category": "Sm", "mappings": {"default": {"default": "reversed empty set"}}, "key": "29B0"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with overbar"}}, "key": "29B1"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with small circle above"}}, "key": "29B2"}, {"category": "Sm", "mappings": {"default": {"default": "circle with horizontal bar"}}, "key": "29B5"}, {"category": "Sm", "mappings": {"default": {"default": "circled vertical bar"}}, "key": "29B6"}, {"category": "Sm", "mappings": {"default": {"default": "circled parallel"}}, "key": "29B7"}, {"category": "Sm", "mappings": {"default": {"default": "circled backslash"}}, "key": "29B8"}, {"category": "Sm", "mappings": {"default": {"default": "circled perpendicular"}}, "key": "29B9"}, {"category": "Sm", "mappings": {"default": {"default": "circle divided by horizontal bar and top half divided by vertical bar"}}, "key": "29BA"}, {"category": "Sm", "mappings": {"default": {"default": "circle with superimposed x"}}, "key": "29BB"}, {"category": "Sm", "mappings": {"default": {"default": "circled anticlockwise rotated division"}}, "key": "29BC"}, {"category": "Sm", "mappings": {"default": {"default": "circled white bullet"}}, "key": "29BE"}, {"category": "Sm", "mappings": {"default": {"default": "circled bullet"}}, "key": "29BF"}, {"category": "Sm", "mappings": {"default": {"default": "circled less than"}}, "key": "29C0"}, {"category": "Sm", "mappings": {"default": {"default": "circled greater than"}}, "key": "29C1"}, {"category": "Sm", "mappings": {"default": {"default": "circle with small circle to the right"}}, "key": "29C2"}, {"category": "Sm", "mappings": {"default": {"default": "circle with two horizontal strokes to the right"}}, "key": "29C3"}, {"category": "Sm", "mappings": {"default": {"default": "squared rising diagonal slash"}}, "key": "29C4"}, {"category": "Sm", "mappings": {"default": {"default": "squared falling diagonal slash"}}, "key": "29C5"}, {"category": "Sm", "mappings": {"default": {"default": "squared asterisk"}}, "key": "29C6"}, {"category": "Sm", "mappings": {"default": {"default": "squared small circle"}}, "key": "29C7"}, {"category": "Sm", "mappings": {"default": {"default": "squared square"}}, "key": "29C8"}, {"category": "Sm", "mappings": {"default": {"default": "two joined squares"}}, "key": "29C9"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with dot above"}}, "key": "29CA"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with underbar"}}, "key": "29CB"}, {"category": "Sm", "mappings": {"default": {"default": "s in triangle"}}, "key": "29CC"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with serifs at bottom"}}, "key": "29CD"}, {"category": "Sm", "mappings": {"default": {"default": "right triangle above left triangle"}}, "key": "29CE"}, {"category": "Sm", "mappings": {"default": {"default": "left triangle beside vertical bar"}}, "key": "29CF"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar beside right triangle"}}, "key": "29D0"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie with left half black"}}, "key": "29D1"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie with right half black"}}, "key": "29D2"}, {"category": "Sm", "mappings": {"default": {"default": "black bowtie"}}, "key": "29D3"}, {"category": "Sm", "mappings": {"default": {"default": "times with left half black"}}, "key": "29D4"}, {"category": "Sm", "mappings": {"default": {"default": "times with right half black"}}, "key": "29D5"}, {"category": "Sm", "mappings": {"default": {"default": "white hourglass"}}, "key": "29D6"}, {"category": "Sm", "mappings": {"default": {"default": "black hourglass"}}, "key": "29D7"}, {"category": "Sm", "mappings": {"default": {"default": "incomplete infinity"}}, "key": "29DC"}, {"category": "Sm", "mappings": {"default": {"default": "tie over infinity"}}, "key": "29DD"}, {"category": "Sm", "mappings": {"default": {"default": "infinity negated with vertical bar"}}, "key": "29DE"}, {"category": "Sm", "mappings": {"default": {"default": "double ended multimap"}}, "key": "29DF"}, {"category": "Sm", "mappings": {"default": {"default": "square with contoured outline"}}, "key": "29E0"}, {"category": "Sm", "mappings": {"default": {"default": "increases as"}}, "key": "29E1"}, {"category": "Sm", "mappings": {"default": {"default": "shuffle product"}}, "key": "29E2"}, {"category": "Sm", "mappings": {"default": {"default": "equals and slanted parallel"}}, "key": "29E3"}, {"category": "Sm", "mappings": {"default": {"default": "equals and slanted parallel with tilde above"}}, "key": "29E4"}, {"category": "Sm", "mappings": {"default": {"default": "identical to and slanted parallel"}}, "key": "29E5"}, {"category": "Sm", "mappings": {"default": {"default": "gleich stark"}}, "key": "29E6"}, {"category": "Sm", "mappings": {"default": {"default": "thermodynamic"}}, "key": "29E7"}, {"category": "Sm", "mappings": {"default": {"default": "down pointing triangle with left half black"}}, "key": "29E8"}, {"category": "Sm", "mappings": {"default": {"default": "down pointing triangle with right half black"}}, "key": "29E9"}, {"category": "Sm", "mappings": {"default": {"default": "black lozenge"}}, "key": "29EB"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white square"}}, "key": "29EE"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black square"}}, "key": "29EF"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white diamond"}}, "key": "29F0"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black diamond"}}, "key": "29F1"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white circle"}}, "key": "29F2"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black circle"}}, "key": "29F3"}, {"category": "Sm", "mappings": {"default": {"default": "rule delayed"}}, "key": "29F4"}, {"category": "Sm", "mappings": {"default": {"default": "backslash"}}, "key": "29F5"}, {"category": "Sm", "mappings": {"default": {"default": "solidus with overbar"}}, "key": "29F6"}, {"category": "Sm", "mappings": {"default": {"default": "backslash with horizontal stroke"}}, "key": "29F7"}, {"category": "Sm", "mappings": {"default": {"default": "big solidus"}, "mathspeak": {"default": "solidus"}}, "key": "29F8"}, {"category": "Sm", "mappings": {"default": {"default": "big backslash"}, "mathspeak": {"default": "backslash"}}, "key": "29F9"}, {"category": "Sm", "mappings": {"default": {"default": "double plus"}}, "key": "29FA"}, {"category": "Sm", "mappings": {"default": {"default": "triple plus"}}, "key": "29FB"}, {"category": "Sm", "mappings": {"default": {"default": "tiny"}}, "key": "29FE"}, {"category": "Sm", "mappings": {"default": {"default": "miny"}}, "key": "29FF"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled dot"}, "mathspeak": {"default": "circled dot"}}, "key": "2A00"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled plus"}, "mathspeak": {"default": "circled plus"}}, "key": "2A01"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled times"}, "mathspeak": {"default": "circled times"}}, "key": "2A02"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union with dot"}, "mathspeak": {"default": "union with dot"}}, "key": "2A03"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union operator with plus"}, "mathspeak": {"default": "union with plus"}}, "key": "2A04"}, {"category": "Sm", "mappings": {"default": {"default": "n ary square intersection"}, "mathspeak": {"default": "square intersection"}}, "key": "2A05"}, {"category": "Sm", "mappings": {"default": {"default": "n ary square union"}, "mathspeak": {"default": "square union"}}, "key": "2A06"}, {"category": "Sm", "mappings": {"default": {"default": "two and"}}, "key": "2A07"}, {"category": "Sm", "mappings": {"default": {"default": "two or"}}, "key": "2A08"}, {"category": "Sm", "mappings": {"default": {"default": "n ary times"}, "mathspeak": {"default": "times"}}, "key": "2A09"}, {"category": "Sm", "mappings": {"default": {"default": "modulo two sum"}}, "key": "2A0A"}, {"category": "Sm", "mappings": {"default": {"default": "summation with integral"}}, "key": "2A0B"}, {"category": "Sm", "mappings": {"default": {"default": "quadruple integral"}}, "key": "2A0C"}, {"category": "Sm", "mappings": {"default": {"default": "finite part integral"}}, "key": "2A0D"}, {"category": "Sm", "mappings": {"default": {"default": "integral with double stroke"}}, "key": "2A0E"}, {"category": "Sm", "mappings": {"default": {"default": "integral average with slash"}}, "key": "2A0F"}, {"category": "Sm", "mappings": {"default": {"default": "circulation function"}}, "key": "2A10"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise integration"}}, "key": "2A11"}, {"category": "Sm", "mappings": {"default": {"default": "line integration with rectangular path around pole"}}, "key": "2A12"}, {"category": "Sm", "mappings": {"default": {"default": "line integration with semicircular path around pole"}}, "key": "2A13"}, {"category": "Sm", "mappings": {"default": {"default": "line integration not including the pole"}}, "key": "2A14"}, {"category": "Sm", "mappings": {"default": {"default": "integral around a point"}}, "key": "2A15"}, {"category": "Sm", "mappings": {"default": {"default": "quaternion integral"}}, "key": "2A16"}, {"category": "Sm", "mappings": {"default": {"default": "integral with times"}}, "key": "2A18"}, {"category": "Sm", "mappings": {"default": {"default": "integral with intersection"}}, "key": "2A19"}, {"category": "Sm", "mappings": {"default": {"default": "integral with union"}}, "key": "2A1A"}, {"category": "Sm", "mappings": {"default": {"default": "integral with overbar"}}, "key": "2A1B"}, {"category": "Sm", "mappings": {"default": {"default": "integral with underbar"}}, "key": "2A1C"}, {"category": "Sm", "mappings": {"default": {"default": "join"}}, "key": "2A1D"}, {"category": "Sm", "mappings": {"default": {"default": "large left triangle"}}, "key": "2A1E"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema composition"}}, "key": "2A1F"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema piping"}}, "key": "2A20"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema projection"}}, "key": "2A21"}, {"category": "Sm", "mappings": {"default": {"default": "plus with circle above"}}, "key": "2A22"}, {"category": "Sm", "mappings": {"default": {"default": "plus hat"}}, "key": "2A23"}, {"category": "Sm", "mappings": {"default": {"default": "plus tilde"}}, "key": "2A24"}, {"category": "Sm", "mappings": {"default": {"default": "plus underdot"}}, "key": "2A25"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with tilde below"}}, "key": "2A26"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with subscript two"}}, "key": "2A27"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with black triangle"}}, "key": "2A28"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with comma above"}}, "key": "2A29"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with dot below"}}, "key": "2A2A"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with falling dots"}}, "key": "2A2B"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with rising dots"}}, "key": "2A2C"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in left half circle"}}, "key": "2A2D"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in right half circle"}}, "key": "2A2E"}, {"category": "Sm", "mappings": {"default": {"default": "vector or cross product"}}, "key": "2A2F"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign with dot above"}}, "key": "2A30"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign with underbar"}}, "key": "2A31"}, {"category": "Sm", "mappings": {"default": {"default": "semidirect product with bottom closed"}}, "key": "2A32"}, {"category": "Sm", "mappings": {"default": {"default": "smash product"}}, "key": "2A33"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in left half circle"}}, "key": "2A34"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in right half circle"}}, "key": "2A35"}, {"category": "Sm", "mappings": {"default": {"default": "circled multiplication sign with circumflex accent"}}, "key": "2A36"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in double circle"}}, "key": "2A37"}, {"category": "Sm", "mappings": {"default": {"default": "circled division"}}, "key": "2A38"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in triangle"}}, "key": "2A39"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign in triangle"}}, "key": "2A3A"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in triangle"}}, "key": "2A3B"}, {"category": "Sm", "mappings": {"default": {"default": "interior product"}}, "key": "2A3C"}, {"category": "Sm", "mappings": {"default": {"default": "righthand interior product"}}, "key": "2A3D"}, {"category": "Sm", "mappings": {"default": {"default": "z notation relational composition"}}, "key": "2A3E"}, {"category": "Sm", "mappings": {"default": {"default": "amalgamation or coproduct"}}, "key": "2A3F"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with dot"}}, "key": "2A40"}, {"category": "Sm", "mappings": {"default": {"default": "union with minus"}}, "key": "2A41"}, {"category": "Sm", "mappings": {"default": {"default": "union with overbar"}}, "key": "2A42"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with overbar"}}, "key": "2A43"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with and"}}, "key": "2A44"}, {"category": "Sm", "mappings": {"default": {"default": "union with or"}}, "key": "2A45"}, {"category": "Sm", "mappings": {"default": {"default": "union above intersection"}}, "key": "2A46"}, {"category": "Sm", "mappings": {"default": {"default": "intersection above union"}}, "key": "2A47"}, {"category": "Sm", "mappings": {"default": {"default": "union above bar above intersection"}}, "key": "2A48"}, {"category": "Sm", "mappings": {"default": {"default": "intersection above bar above union"}}, "key": "2A49"}, {"category": "Sm", "mappings": {"default": {"default": "union beside and joined with union"}}, "key": "2A4A"}, {"category": "Sm", "mappings": {"default": {"default": "intersection beside and joined with intersection"}}, "key": "2A4B"}, {"category": "Sm", "mappings": {"default": {"default": "closed union with serifs"}}, "key": "2A4C"}, {"category": "Sm", "mappings": {"default": {"default": "closed intersection with serifs"}}, "key": "2A4D"}, {"category": "Sm", "mappings": {"default": {"default": "double square intersection"}}, "key": "2A4E"}, {"category": "Sm", "mappings": {"default": {"default": "double square union"}}, "key": "2A4F"}, {"category": "Sm", "mappings": {"default": {"default": "closed union with serifs and smash product"}}, "key": "2A50"}, {"category": "Sm", "mappings": {"default": {"default": "and with dot above"}}, "key": "2A51"}, {"category": "Sm", "mappings": {"default": {"default": "or with dot above"}}, "key": "2A52"}, {"category": "Sm", "mappings": {"default": {"default": "double and"}}, "key": "2A53"}, {"category": "Sm", "mappings": {"default": {"default": "double or"}}, "key": "2A54"}, {"category": "Sm", "mappings": {"default": {"default": "two intersecting and"}}, "key": "2A55"}, {"category": "Sm", "mappings": {"default": {"default": "two intersecting or"}}, "key": "2A56"}, {"category": "Sm", "mappings": {"default": {"default": "sloping large or"}}, "key": "2A57"}, {"category": "Sm", "mappings": {"default": {"default": "sloping large and"}}, "key": "2A58"}, {"category": "Sm", "mappings": {"default": {"default": "or overlapping and"}}, "key": "2A59"}, {"category": "Sm", "mappings": {"default": {"default": "and with middle stem"}}, "key": "2A5A"}, {"category": "Sm", "mappings": {"default": {"default": "or with middle stem"}}, "key": "2A5B"}, {"category": "Sm", "mappings": {"default": {"default": "and with horizontal dash"}}, "key": "2A5C"}, {"category": "Sm", "mappings": {"default": {"default": "or with horizontal dash"}}, "key": "2A5D"}, {"category": "Sm", "mappings": {"default": {"default": "and with double overbar"}}, "key": "2A5E"}, {"category": "Sm", "mappings": {"default": {"default": "and with underbar"}}, "key": "2A5F"}, {"category": "Sm", "mappings": {"default": {"default": "and with double underbar"}}, "key": "2A60"}, {"category": "Sm", "mappings": {"default": {"default": "small vee with underbar"}}, "key": "2A61"}, {"category": "Sm", "mappings": {"default": {"default": "or with double overbar"}}, "key": "2A62"}, {"category": "Sm", "mappings": {"default": {"default": "or with double underbar"}}, "key": "2A63"}, {"category": "Sm", "mappings": {"default": {"default": "z notation domain antirestriction"}}, "key": "2A64"}, {"category": "Sm", "mappings": {"default": {"default": "z notation range antirestriction"}}, "key": "2A65"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign with dot below"}}, "key": "2A66"}, {"category": "Sm", "mappings": {"default": {"default": "identical with dot above"}}, "key": "2A67"}, {"category": "Sm", "mappings": {"default": {"default": "triple horizontal bar with double vertical stroke"}}, "key": "2A68"}, {"category": "Sm", "mappings": {"default": {"default": "triple horizontal bar with triple vertical stroke"}}, "key": "2A69"}, {"category": "Sm", "mappings": {"default": {"default": "tilde with dot above"}}, "key": "2A6A"}, {"category": "Sm", "mappings": {"default": {"default": "tilde with rising dots"}}, "key": "2A6B"}, {"category": "Sm", "mappings": {"default": {"default": "similar minus similar"}}, "key": "2A6C"}, {"category": "Sm", "mappings": {"default": {"default": "congruent with dot above"}}, "key": "2A6D"}, {"category": "Sm", "mappings": {"default": {"default": "equals with asterisk"}}, "key": "2A6E"}, {"category": "Sm", "mappings": {"default": {"default": "almost equal hat"}}, "key": "2A6F"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equal or equal to"}}, "key": "2A70"}, {"category": "Sm", "mappings": {"default": {"default": "equals above plus"}}, "key": "2A71"}, {"category": "Sm", "mappings": {"default": {"default": "plus above equals"}}, "key": "2A72"}, {"category": "Sm", "mappings": {"default": {"default": "equals above tilde"}}, "key": "2A73"}, {"category": "Sm", "mappings": {"default": {"default": "double colon equal"}}, "key": "2A74"}, {"category": "Sm", "mappings": {"default": {"default": "two consecutive equals"}}, "key": "2A75"}, {"category": "Sm", "mappings": {"default": {"default": "three consecutive equals"}}, "key": "2A76"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign with two dots above and two dots below"}}, "key": "2A77"}, {"category": "Sm", "mappings": {"default": {"default": "equivalent with four dots above"}}, "key": "2A78"}, {"category": "Sm", "mappings": {"default": {"default": "less than with circle inside"}}, "key": "2A79"}, {"category": "Sm", "mappings": {"default": {"default": "greater than with circle inside"}}, "key": "2A7A"}, {"category": "Sm", "mappings": {"default": {"default": "less than with question mark above"}}, "key": "2A7B"}, {"category": "Sm", "mappings": {"default": {"default": "greater than with question mark above"}}, "key": "2A7C"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals"}}, "key": "2A7D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals"}}, "key": "2A7E"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot inside"}}, "key": "2A7F"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot inside"}}, "key": "2A80"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot above"}}, "key": "2A81"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot above"}}, "key": "2A82"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot above right"}}, "key": "2A83"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot above left"}}, "key": "2A84"}, {"category": "Sm", "mappings": {"default": {"default": "less than or approximate"}}, "key": "2A85"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or approximate"}}, "key": "2A86"}, {"category": "Sm", "mappings": {"default": {"default": "less than and single line not equals"}}, "key": "2A87"}, {"category": "Sm", "mappings": {"default": {"default": "greater than and single line not equals"}}, "key": "2A88"}, {"category": "Sm", "mappings": {"default": {"default": "less than and not approximate"}}, "key": "2A89"}, {"category": "Sm", "mappings": {"default": {"default": "greater than and not approximate"}}, "key": "2A8A"}, {"category": "Sm", "mappings": {"default": {"default": "less than above double line equal above greater than"}}, "key": "2A8B"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above double line equal above less than"}}, "key": "2A8C"}, {"category": "Sm", "mappings": {"default": {"default": "less than above similar or equal"}}, "key": "2A8D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above similar or equal"}}, "key": "2A8E"}, {"category": "Sm", "mappings": {"default": {"default": "less than above similar above greater than"}}, "key": "2A8F"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above similar above less than"}}, "key": "2A90"}, {"category": "Sm", "mappings": {"default": {"default": "less than above greater than above double line equal"}}, "key": "2A91"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above less than above double line equal"}}, "key": "2A92"}, {"category": "Sm", "mappings": {"default": {"default": "less than above slanted equal above greater than above slanted equal"}}, "key": "2A93"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above slanted equal above less than above slanted equal"}}, "key": "2A94"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or less than"}}, "key": "2A95"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or greater than"}}, "key": "2A96"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or less than with dot inside"}}, "key": "2A97"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or greater than with dot inside"}}, "key": "2A98"}, {"category": "Sm", "mappings": {"default": {"default": "double line equals or less than"}}, "key": "2A99"}, {"category": "Sm", "mappings": {"default": {"default": "double line equals or greater than"}}, "key": "2A9A"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted equals or less than"}}, "key": "2A9B"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted equals or greater than"}}, "key": "2A9C"}, {"category": "Sm", "mappings": {"default": {"default": "similar or less than"}}, "key": "2A9D"}, {"category": "Sm", "mappings": {"default": {"default": "similar or greater than"}}, "key": "2A9E"}, {"category": "Sm", "mappings": {"default": {"default": "similar above less than above equals sign"}}, "key": "2A9F"}, {"category": "Sm", "mappings": {"default": {"default": "similar above greater than above equals sign"}}, "key": "2AA0"}, {"category": "Sm", "mappings": {"default": {"default": "double nested less than"}}, "key": "2AA1"}, {"category": "Sm", "mappings": {"default": {"default": "double nested greater than"}}, "key": "2AA2"}, {"category": "Sm", "mappings": {"default": {"default": "double nested less than with underbar"}}, "key": "2AA3"}, {"category": "Sm", "mappings": {"default": {"default": "greater than overlapping less than"}}, "key": "2AA4"}, {"category": "Sm", "mappings": {"default": {"default": "greater than beside less than"}}, "key": "2AA5"}, {"category": "Sm", "mappings": {"default": {"default": "less than closed by curve"}}, "key": "2AA6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than closed by curve"}}, "key": "2AA7"}, {"category": "Sm", "mappings": {"default": {"default": "less than closed by curve above slanted equal"}}, "key": "2AA8"}, {"category": "Sm", "mappings": {"default": {"default": "greater than closed by curve above slanted equal"}}, "key": "2AA9"}, {"category": "Sm", "mappings": {"default": {"default": "smaller than"}, "clearspeak": {"default": "is smaller than"}}, "key": "2AAA"}, {"category": "Sm", "mappings": {"default": {"default": "larger than"}, "clearspeak": {"default": "is larger than"}}, "key": "2AAB"}, {"category": "Sm", "mappings": {"default": {"default": "smaller than or equals"}, "clearspeak": {"default": "is smaller than or equal to"}}, "key": "2AAC"}, {"category": "Sm", "mappings": {"default": {"default": "larger than or equal"}, "clearspeak": {"default": "is larger than or equal to"}}, "key": "2AAD"}, {"category": "Sm", "mappings": {"default": {"default": "equals with bumpy above"}}, "key": "2AAE"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above single line equals sign"}}, "key": "2AAF"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above single line equals sign"}}, "key": "2AB0"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above single line not equals"}}, "key": "2AB1"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above single line not equals"}}, "key": "2AB2"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above equals"}}, "key": "2AB3"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above equals"}}, "key": "2AB4"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above not equals"}}, "key": "2AB5"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above not equals"}}, "key": "2AB6"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above almost equals"}}, "key": "2AB7"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above almost equals"}}, "key": "2AB8"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above not almost equals"}}, "key": "2AB9"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above not almost equals"}}, "key": "2ABA"}, {"category": "Sm", "mappings": {"default": {"default": "double precedes"}}, "key": "2ABB"}, {"category": "Sm", "mappings": {"default": {"default": "double succeeds"}}, "key": "2ABC"}, {"category": "Sm", "mappings": {"default": {"default": "subset with dot"}}, "key": "2ABD"}, {"category": "Sm", "mappings": {"default": {"default": "superset with dot"}}, "key": "2ABE"}, {"category": "Sm", "mappings": {"default": {"default": "subset with plus sign below"}}, "key": "2ABF"}, {"category": "Sm", "mappings": {"default": {"default": "superset with plus sign below"}}, "key": "2AC0"}, {"category": "Sm", "mappings": {"default": {"default": "subset with multiplication sign below"}}, "key": "2AC1"}, {"category": "Sm", "mappings": {"default": {"default": "superset with multiplication sign below"}}, "key": "2AC2"}, {"category": "Sm", "mappings": {"default": {"default": "subset of or equals with dot above"}}, "key": "2AC3"}, {"category": "Sm", "mappings": {"default": {"default": "superset of or equals with dot above"}}, "key": "2AC4"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above equals sign"}}, "key": "2AC5"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above equals sign"}}, "key": "2AC6"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above tilde"}}, "key": "2AC7"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above tilde"}}, "key": "2AC8"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above almost equals"}}, "key": "2AC9"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above almost equals"}}, "key": "2ACA"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above not equals"}}, "key": "2ACB"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above not equals"}}, "key": "2ACC"}, {"category": "Sm", "mappings": {"default": {"default": "square left open box"}}, "key": "2ACD"}, {"category": "Sm", "mappings": {"default": {"default": "square right open box"}}, "key": "2ACE"}, {"category": "Sm", "mappings": {"default": {"default": "closed subset"}}, "key": "2ACF"}, {"category": "Sm", "mappings": {"default": {"default": "closed superset"}}, "key": "2AD0"}, {"category": "Sm", "mappings": {"default": {"default": "closed subset or equals"}}, "key": "2AD1"}, {"category": "Sm", "mappings": {"default": {"default": "closed superset or equals"}}, "key": "2AD2"}, {"category": "Sm", "mappings": {"default": {"default": "subset above superset"}}, "key": "2AD3"}, {"category": "Sm", "mappings": {"default": {"default": "superset above subset"}}, "key": "2AD4"}, {"category": "Sm", "mappings": {"default": {"default": "subset above subset"}}, "key": "2AD5"}, {"category": "Sm", "mappings": {"default": {"default": "superset above superset"}}, "key": "2AD6"}, {"category": "Sm", "mappings": {"default": {"default": "superset beside subset"}}, "key": "2AD7"}, {"category": "Sm", "mappings": {"default": {"default": "superset beside and joined by dash with subset"}}, "key": "2AD8"}, {"category": "Sm", "mappings": {"default": {"default": "element of opening downwards"}}, "key": "2AD9"}, {"category": "Sm", "mappings": {"default": {"default": "pitchfork with tee top"}}, "key": "2ADA"}, {"category": "Sm", "mappings": {"default": {"default": "transversal intersection"}}, "key": "2ADB"}, {"category": "Sm", "mappings": {"default": {"default": "forking"}}, "key": "2ADC"}, {"category": "Sm", "mappings": {"default": {"default": "nonforking"}}, "key": "2ADD"}, {"category": "Sm", "mappings": {"default": {"default": "short left tack"}}, "key": "2ADE"}, {"category": "Sm", "mappings": {"default": {"default": "short down tack"}}, "key": "2ADF"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack"}}, "key": "2AE0"}, {"category": "Sm", "mappings": {"default": {"default": "perpendicular with s"}}, "key": "2AE1"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar triple right turnstile"}}, "key": "2AE2"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar left turnstile"}}, "key": "2AE3"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar double left turnstile"}}, "key": "2AE4"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar double left turnstile"}}, "key": "2AE5"}, {"category": "Sm", "mappings": {"default": {"default": "long dash from left member of double vertical"}}, "key": "2AE6"}, {"category": "Sm", "mappings": {"default": {"default": "short down tack with overbar"}}, "key": "2AE7"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack with underbar"}}, "key": "2AE8"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack above short down tack"}}, "key": "2AE9"}, {"category": "Sm", "mappings": {"default": {"default": "double down tack"}}, "key": "2AEA"}, {"category": "Sm", "mappings": {"default": {"default": "double up tack"}}, "key": "2AEB"}, {"category": "Sm", "mappings": {"default": {"default": "double stroke not sign"}}, "key": "2AEC"}, {"category": "Sm", "mappings": {"default": {"default": "reversed double stroke not sign"}}, "key": "2AED"}, {"category": "Sm", "mappings": {"default": {"default": "does not divide with reversed negation slash"}}, "key": "2AEE"}, {"category": "Sm", "mappings": {"default": {"default": "vertical line with circle above"}}, "key": "2AEF"}, {"category": "Sm", "mappings": {"default": {"default": "vertical line with circle below"}}, "key": "2AF0"}, {"category": "Sm", "mappings": {"default": {"default": "down tack with circle below"}}, "key": "2AF1"}, {"category": "Sm", "mappings": {"default": {"default": "parallel with horizontal stroke"}}, "key": "2AF2"}, {"category": "Sm", "mappings": {"default": {"default": "parallel with tilde"}}, "key": "2AF3"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar binary relation"}}, "key": "2AF4"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar with horizontal stroke"}}, "key": "2AF5"}, {"category": "Sm", "mappings": {"default": {"default": "triple colon"}}, "key": "2AF6"}, {"category": "Sm", "mappings": {"default": {"default": "triple nested less than"}}, "key": "2AF7"}, {"category": "Sm", "mappings": {"default": {"default": "triple nested greater than"}}, "key": "2AF8"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted less than or equals"}}, "key": "2AF9"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted greater than or equals"}}, "key": "2AFA"}, {"category": "Sm", "mappings": {"default": {"default": "triple solidus binary relation"}}, "key": "2AFB"}, {"category": "Sm", "mappings": {"default": {"default": "large triple vertical bar"}}, "key": "2AFC"}, {"category": "Sm", "mappings": {"default": {"default": "double solidus"}}, "key": "2AFD"}, {"category": "Sm", "mappings": {"default": {"default": "white vertical bar"}}, "key": "2AFE"}, {"category": "Sm", "mappings": {"default": {"default": "n ary white vertical bar"}, "mathspeak": {"default": "white vertical bar"}}, "key": "2AFF"}, {"category": "Pd", "mappings": {"default": {"default": "wave dash"}}, "key": "301C"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical comma"}}, "key": "FE10"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical colon"}}, "key": "FE13"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical semicolon"}}, "key": "FE14"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical exclamation mark"}}, "key": "FE15"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical question mark"}}, "key": "FE16"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical horizontal ellipsis"}}, "key": "FE19"}, {"category": "Po", "mappings": {"default": {"default": "glyph for vertical two dot leader"}}, "key": "FE30"}, {"category": "Pd", "mappings": {"default": {"default": "glyph for vertical em dash"}}, "key": "FE31"}, {"category": "Pd", "mappings": {"default": {"default": "glyph for vertical en dash"}}, "key": "FE32"}, {"category": "Pc", "mappings": {"default": {"default": "glyph for vertical underline"}}, "key": "FE33"}, {"category": "Pc", "mappings": {"default": {"default": "glyph for vertical wavy underline"}}, "key": "FE34"}, {"category": "Po", "mappings": {"default": {"default": "sesame dot"}}, "key": "FE45"}, {"category": "Po", "mappings": {"default": {"default": "white sesame dot"}}, "key": "FE46"}, {"category": "Po", "mappings": {"default": {"default": "dashed overline"}}, "key": "FE49"}, {"category": "Po", "mappings": {"default": {"default": "dash dot overline"}}, "key": "FE4A"}, {"category": "Po", "mappings": {"default": {"default": "wavy overline"}}, "key": "FE4B"}, {"category": "Po", "mappings": {"default": {"default": "double wavy overline"}}, "key": "FE4C"}, {"category": "Pc", "mappings": {"default": {"default": "dashed underline"}}, "key": "FE4D"}, {"category": "Pc", "mappings": {"default": {"default": "dash dot underline"}}, "key": "FE4E"}, {"category": "Pc", "mappings": {"default": {"default": "wavy underline"}}, "key": "FE4F"}, {"category": "Po", "mappings": {"default": {"default": "small comma"}}, "key": "FE50"}, {"category": "Po", "mappings": {"default": {"default": "small period"}}, "key": "FE52"}, {"category": "Po", "mappings": {"default": {"default": "small semicolon"}}, "key": "FE54"}, {"category": "Po", "mappings": {"default": {"default": "small colon"}}, "key": "FE55"}, {"category": "Po", "mappings": {"default": {"default": "small question mark"}}, "key": "FE56"}, {"category": "Po", "mappings": {"default": {"default": "small exclamation mark"}}, "key": "FE57"}, {"category": "Pd", "mappings": {"default": {"default": "small em dash"}}, "key": "FE58"}, {"category": "Po", "mappings": {"default": {"default": "small number sign"}}, "key": "FE5F"}, {"category": "Po", "mappings": {"default": {"default": "small ampersand"}}, "key": "FE60"}, {"category": "Po", "mappings": {"default": {"default": "small asterisk"}}, "key": "FE61"}, {"category": "Sm", "mappings": {"default": {"default": "small plus sign"}}, "key": "FE62"}, {"category": "Pd", "mappings": {"default": {"default": "small hyphen minus"}}, "key": "FE63"}, {"category": "Sm", "mappings": {"default": {"default": "small less than sign"}}, "key": "FE64"}, {"category": "Sm", "mappings": {"default": {"default": "small greater than sign"}}, "key": "FE65"}, {"category": "Sm", "mappings": {"default": {"default": "small equals"}}, "key": "FE66"}, {"category": "Po", "mappings": {"default": {"default": "small backslash", "alternative": "small reverse solidus"}}, "key": "FE68"}, {"category": "Sc", "mappings": {"default": {"default": "small dollar sign"}}, "key": "FE69"}, {"category": "Po", "mappings": {"default": {"default": "small percent sign"}}, "key": "FE6A"}, {"category": "Po", "mappings": {"default": {"default": "small commercial at"}}, "key": "FE6B"}, {"category": "Po", "mappings": {"default": {"default": "exclamation mark"}}, "key": "FF01"}, {"category": "Po", "mappings": {"default": {"default": "quotation mark"}}, "key": "FF02"}, {"category": "Po", "mappings": {"default": {"default": "number sign"}}, "key": "FF03"}, {"category": "Sc", "mappings": {"default": {"default": "dollar sign"}}, "key": "FF04"}, {"category": "Po", "mappings": {"default": {"default": "percent sign"}}, "key": "FF05"}, {"category": "Po", "mappings": {"default": {"default": "ampersand"}}, "key": "FF06"}, {"category": "Po", "mappings": {"default": {"default": "apostrophe"}}, "key": "FF07"}, {"category": "Po", "mappings": {"default": {"default": "asterisk"}}, "key": "FF0A"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign"}}, "key": "FF0B"}, {"category": "Po", "mappings": {"default": {"default": "comma"}}, "key": "FF0C"}, {"category": "Pd", "mappings": {"default": {"default": "hyphen minus"}}, "key": "FF0D"}, {"category": "Po", "mappings": {"default": {"default": "period"}}, "key": "FF0E"}, {"category": "Po", "mappings": {"default": {"default": "slash", "alternative": "solidus"}}, "key": "FF0F"}, {"category": "Po", "mappings": {"default": {"default": "colon"}}, "key": "FF1A"}, {"category": "Po", "mappings": {"default": {"default": "semicolon"}}, "key": "FF1B"}, {"category": "Sm", "mappings": {"default": {"default": "less than"}}, "key": "FF1C"}, {"category": "Sm", "mappings": {"default": {"default": "equals"}}, "key": "FF1D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than"}}, "key": "FF1E"}, {"category": "Po", "mappings": {"default": {"default": "question mark"}}, "key": "FF1F"}, {"category": "Po", "mappings": {"default": {"default": "commercial at"}}, "key": "FF20"}, {"category": "Po", "mappings": {"default": {"default": "backslash", "alternative": "reverse solidus"}}, "key": "FF3C"}, {"category": "Sk", "mappings": {"default": {"default": "caret", "alternative": "circumflex"}}, "key": "FF3E"}, {"category": "Pc", "mappings": {"default": {"default": "bar", "alternative": "underline"}}, "key": "FF3F"}, {"category": "Sk", "mappings": {"default": {"default": "grave"}}, "key": "FF40"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar"}}, "key": "FF5C"}, {"category": "Sm", "mappings": {"default": {"default": "tilde"}}, "key": "FF5E"}, {"category": "Sc", "mappings": {"default": {"default": "cent sign"}}, "key": "FFE0"}, {"category": "Sc", "mappings": {"default": {"default": "pound sign"}}, "key": "FFE1"}, {"category": "Sm", "mappings": {"default": {"default": "not sign"}}, "key": "FFE2"}, {"category": "Sk", "mappings": {"default": {"default": "macron"}, "mathspeak": {"default": "bar"}}, "key": "FFE3"}, {"category": "So", "mappings": {"default": {"default": "broken vertical bar"}}, "key": "FFE4"}, {"category": "Sc", "mappings": {"default": {"default": "yen sign"}}, "key": "FFE5"}, {"category": "Sc", "mappings": {"default": {"default": "won sign"}}, "key": "FFE6"}, {"category": "So", "mappings": {"default": {"default": "halfwidth forms light vertical"}}, "key": "FFE8"}, {"category": "So", "mappings": {"default": {"default": "halfwidth black square"}}, "key": "FFED"}, {"category": "So", "mappings": {"default": {"default": "halfwidth white circle"}}, "key": "FFEE"}], "en/symbols/math_whitespace.js": [{"locale": "en"}, {"category": "Zs", "mappings": {"default": {"default": "space"}}, "key": "0020"}, {"category": "Zs", "mappings": {"default": {"default": "no break space", "alternative": "non breaking space"}}, "key": "00A0"}, {"category": "Cf", "mappings": {"default": {"default": "soft hyphen"}}, "key": "00AD"}, {"category": "Zs", "mappings": {"default": {"default": "en quad"}}, "key": "2000"}, {"category": "Zs", "mappings": {"default": {"default": "em quad"}}, "key": "2001"}, {"category": "Zs", "mappings": {"default": {"default": "en space"}}, "key": "2002"}, {"category": "Zs", "mappings": {"default": {"default": "em space"}}, "key": "2003"}, {"category": "Zs", "mappings": {"default": {"default": "three per em space"}}, "key": "2004"}, {"category": "Zs", "mappings": {"default": {"default": "four per em space"}}, "key": "2005"}, {"category": "Zs", "mappings": {"default": {"default": "six per em space"}}, "key": "2006"}, {"category": "Zs", "mappings": {"default": {"default": "figure space"}}, "key": "2007"}, {"category": "Zs", "mappings": {"default": {"default": "punctuation space"}}, "key": "2008"}, {"category": "Zs", "mappings": {"default": {"default": "thin space"}}, "key": "2009"}, {"category": "Zs", "mappings": {"default": {"default": "hair space"}}, "key": "200A"}, {"category": "Cf", "mappings": {"default": {"default": "zero width space"}}, "key": "200B"}, {"category": "Cf", "mappings": {"default": {"default": "zero width non joiner"}}, "key": "200C"}, {"category": "Cf", "mappings": {"default": {"default": "zero width joiner"}}, "key": "200D"}, {"category": "Cf", "mappings": {"default": {"default": "left to right mark"}}, "key": "200E"}, {"category": "Cf", "mappings": {"default": {"default": "right to left mark"}}, "key": "200F"}, {"category": "Zl", "mappings": {"default": {"default": "line separator"}}, "key": "2028"}, {"category": "Zp", "mappings": {"default": {"default": "paragraph separator"}}, "key": "2029"}, {"category": "Cf", "mappings": {"default": {"default": "left to right embedding"}}, "key": "202A"}, {"category": "Cf", "mappings": {"default": {"default": "right to left embedding"}}, "key": "202B"}, {"category": "Cf", "mappings": {"default": {"default": "pop directional formatting"}}, "key": "202C"}, {"category": "Cf", "mappings": {"default": {"default": "left to right override"}}, "key": "202D"}, {"category": "Cf", "mappings": {"default": {"default": "right to left override"}}, "key": "202E"}, {"category": "Zs", "mappings": {"default": {"default": "narrow no break space"}}, "key": "202F"}, {"category": "Zs", "mappings": {"default": {"default": "medium mathematical space"}}, "key": "205F"}, {"category": "Cf", "mappings": {"default": {"default": "word joiner"}}, "key": "2060"}, {"category": "Cf", "mappings": {"default": {"default": "of", "alternative": "function application"}}, "key": "2061"}, {"category": "Cf", "mappings": {"default": {"default": "times", "alternative": "invisible times"}}, "key": "2062"}, {"category": "Cf", "mappings": {"default": {"default": "separator", "alternative": "invisible separator"}}, "key": "2063"}, {"category": "Cf", "mappings": {"default": {"default": "plus", "alternative": "invisible plus"}}, "key": "2064"}, {"category": "Cf", "mappings": {"default": {"default": "inhibit symmetric swapping"}}, "key": "206A"}, {"category": "Cf", "mappings": {"default": {"default": "activate symmetric swapping"}}, "key": "206B"}, {"category": "Cf", "mappings": {"default": {"default": "national digit shapes"}}, "key": "206E"}, {"category": "Cf", "mappings": {"default": {"default": "nominal digit shapes"}}, "key": "206F"}, {"category": "Cf", "mappings": {"default": {"default": "zero width no break space", "alternative": "byte order mark"}}, "key": "FEFF"}, {"category": "Cf", "mappings": {"default": {"default": "interlinear annotation anchor"}}, "key": "FFF9"}, {"category": "Cf", "mappings": {"default": {"default": "interlinear annotation separator"}}, "key": "FFFA"}, {"category": "Cf", "mappings": {"default": {"default": "interlinear annotation terminator"}}, "key": "FFFB"}], "en/symbols/other_stars.js": [{"locale": "en"}, {"category": "So", "mappings": {"default": {"default": "decimal exponent symbol"}}, "key": "23E8"}, {"category": "So", "mappings": {"default": {"default": "black star"}}, "key": "2605"}, {"category": "So", "mappings": {"default": {"default": "white star"}}, "key": "2606"}, {"category": "So", "mappings": {"default": {"default": "white circle", "alternative": "medium white circle"}}, "key": "26AA"}, {"category": "So", "mappings": {"default": {"default": "black circle", "alternative": "medium black circle"}}, "key": "26AB"}, {"category": "So", "mappings": {"default": {"default": "white check mark", "alternative": "white heavy check mark"}}, "key": "2705"}, {"category": "So", "mappings": {"default": {"default": "check mark"}}, "key": "2713"}, {"category": "So", "mappings": {"default": {"default": "heavy check mark"}}, "key": "2714"}, {"category": "So", "mappings": {"default": {"default": "multiplication x"}}, "key": "2715"}, {"category": "So", "mappings": {"default": {"default": "heavy multiplication x"}}, "key": "2716"}, {"category": "So", "mappings": {"default": {"default": "ballot x"}}, "key": "2717"}, {"category": "So", "mappings": {"default": {"default": "heavy ballot x"}}, "key": "2718"}, {"category": "So", "mappings": {"default": {"default": "open center cross"}}, "key": "271B"}, {"category": "So", "mappings": {"default": {"default": "heavy open center cross"}}, "key": "271C"}, {"category": "So", "mappings": {"default": {"default": "maltese cross"}}, "key": "2720"}, {"category": "So", "mappings": {"default": {"default": "star of david"}}, "key": "2721"}, {"category": "So", "mappings": {"default": {"default": "four teardrop spoked asterisk"}}, "key": "2722"}, {"category": "So", "mappings": {"default": {"default": "four balloon spoked asterisk"}}, "key": "2723"}, {"category": "So", "mappings": {"default": {"default": "heavy four balloon spoked asterisk"}}, "key": "2724"}, {"category": "So", "mappings": {"default": {"default": "four club spoked asterisk"}}, "key": "2725"}, {"category": "So", "mappings": {"default": {"default": "black four pointed star"}}, "key": "2726"}, {"category": "So", "mappings": {"default": {"default": "white four pointed star"}}, "key": "2727"}, {"category": "So", "mappings": {"default": {"default": "sparkles"}}, "key": "2728"}, {"category": "So", "mappings": {"default": {"default": "stress outlined white star"}}, "key": "2729"}, {"category": "So", "mappings": {"default": {"default": "circled white star"}}, "key": "272A"}, {"category": "So", "mappings": {"default": {"default": "open center black star"}}, "key": "272B"}, {"category": "So", "mappings": {"default": {"default": "black center white star"}}, "key": "272C"}, {"category": "So", "mappings": {"default": {"default": "outlined black star"}}, "key": "272D"}, {"category": "So", "mappings": {"default": {"default": "heavy outlined black star"}}, "key": "272E"}, {"category": "So", "mappings": {"default": {"default": "pinwheel star"}}, "key": "272F"}, {"category": "So", "mappings": {"default": {"default": "shadowed white star"}}, "key": "2730"}, {"category": "So", "mappings": {"default": {"default": "heavy asterisk"}}, "key": "2731"}, {"category": "So", "mappings": {"default": {"default": "open center asterisk"}}, "key": "2732"}, {"category": "So", "mappings": {"default": {"default": "eight spoked asterisk"}}, "key": "2733"}, {"category": "So", "mappings": {"default": {"default": "eight pointed black star"}}, "key": "2734"}, {"category": "So", "mappings": {"default": {"default": "eight pointed pinwheel star"}}, "key": "2735"}, {"category": "So", "mappings": {"default": {"default": "six pointed black star"}}, "key": "2736"}, {"category": "So", "mappings": {"default": {"default": "twelve pointed black star"}}, "key": "2739"}, {"category": "So", "mappings": {"default": {"default": "sixteen pointed asterisk"}}, "key": "273A"}, {"category": "So", "mappings": {"default": {"default": "teardrop spoked asterisk"}}, "key": "273B"}, {"category": "So", "mappings": {"default": {"default": "open center teardrop spoked asterisk"}}, "key": "273C"}, {"category": "So", "mappings": {"default": {"default": "heavy teardrop spoked asterisk"}}, "key": "273D"}, {"category": "So", "mappings": {"default": {"default": "six petalled black and white florette"}}, "key": "273E"}, {"category": "So", "mappings": {"default": {"default": "black florette"}}, "key": "273F"}, {"category": "So", "mappings": {"default": {"default": "white florette"}}, "key": "2740"}, {"category": "So", "mappings": {"default": {"default": "eight petalled outlined black florette"}}, "key": "2741"}, {"category": "So", "mappings": {"default": {"default": "circled open center eight pointed star"}}, "key": "2742"}, {"category": "So", "mappings": {"default": {"default": "heavy teardrop spoked pinwheel asterisk"}}, "key": "2743"}, {"category": "So", "mappings": {"default": {"default": "snowflake"}}, "key": "2744"}, {"category": "So", "mappings": {"default": {"default": "tight trifoliate snowflake"}}, "key": "2745"}, {"category": "So", "mappings": {"default": {"default": "heavy chevron snowflake"}}, "key": "2746"}, {"category": "So", "mappings": {"default": {"default": "sparkle"}}, "key": "2747"}, {"category": "So", "mappings": {"default": {"default": "heavy sparkle"}}, "key": "2748"}, {"category": "So", "mappings": {"default": {"default": "balloon spoked asterisk"}}, "key": "2749"}, {"category": "So", "mappings": {"default": {"default": "eight teardrop spoked propeller asterisk"}}, "key": "274A"}, {"category": "So", "mappings": {"default": {"default": "heavy eight teardrop spoked propeller asterisk"}}, "key": "274B"}, {"category": "So", "mappings": {"default": {"default": "cross mark"}}, "key": "274C"}, {"category": "So", "mappings": {"default": {"default": "shadowed white circle"}}, "key": "274D"}], "en/units/area.js": [{"locale": "en"}, {"category": "other", "mappings": {"default": {"default": "square"}}, "key": "sq", "names": ["sq", "sq."]}, {"category": "area", "mappings": {"default": {"default": "square inch"}}, "key": "sq inch", "names": ["sq inch", "sq. inch."]}, {"category": "area", "mappings": {"default": {"default": "square rod"}}, "key": "sq rd", "names": ["sq rd", "sq. rd."]}, {"category": "area", "mappings": {"default": {"default": "square foot", "plural": "square feet"}}, "key": "sq ft", "names": ["sq ft", "sq. ft."]}, {"category": "area", "mappings": {"default": {"default": "square yard"}}, "key": "sq yd", "names": ["sq yd", "sq. yd."]}, {"category": "area", "mappings": {"default": {"default": "square mile"}}, "key": "sq mi", "names": ["sq mi", "sq. mi."]}, {"category": "area", "mappings": {"default": {"default": "acre"}}, "key": "acr", "names": ["ac", "ac.", "acr", "acr."]}, {"category": "area", "mappings": {"default": {"default": "hectare"}}, "key": "ha", "names": ["ha"]}], "en/units/currency.js": [{"locale": "en"}, {"category": "currency", "key": "$", "mappings": {"default": {"default": "dollar"}}, "names": ["$", "💲", "＄", "﹩", "USD"]}, {"category": "currency", "key": "£", "mappings": {"default": {"default": "pound"}}, "names": ["£", "￡", "GBP"]}, {"category": "currency", "key": "¥", "mappings": {"default": {"default": "yen"}}, "names": ["¥", "￥", "JPY"]}, {"category": "currency", "key": "€", "mappings": {"default": {"default": "euro"}}, "names": ["€", "EUR"]}, {"category": "currency", "key": "₡", "mappings": {"default": {"default": "colon"}}, "names": ["₡", "CRC"]}, {"category": "currency", "key": "₢", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON>"}}, "names": ["₢"]}, {"category": "currency", "key": "₣", "mappings": {"default": {"default": "franc"}}, "names": ["₣"]}, {"category": "currency", "key": "₤", "mappings": {"default": {"default": "lira"}}, "names": ["₤"]}, {"category": "currency", "key": "₥", "mappings": {"default": {"default": "mill"}}, "names": ["₥"]}, {"category": "currency", "key": "₦", "mappings": {"default": {"default": "naira"}}, "names": ["₦", "NGN"]}, {"category": "currency", "key": "₧", "mappings": {"default": {"default": "peseta"}}, "names": ["₧"]}, {"category": "currency", "key": "₨", "mappings": {"default": {"default": "rupee"}}, "names": ["₨", "₹", "INR", "NPR", "PKR", "LKR"]}, {"category": "currency", "key": "₩", "mappings": {"default": {"default": "won"}}, "names": ["₩", "￦", "KRW"]}, {"category": "currency", "key": "₪", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}, "names": ["₪"]}, {"category": "currency", "key": "₫", "mappings": {"default": {"default": "dong"}}, "names": ["₫"]}, {"category": "currency", "key": "₭", "mappings": {"default": {"default": "kip"}}, "names": ["₭"]}, {"category": "currency", "key": "₮", "mappings": {"default": {"default": "<PERSON><PERSON>"}}, "names": ["₮"]}, {"category": "currency", "key": "₯", "mappings": {"default": {"default": "drachma"}}, "names": ["₯"]}, {"category": "currency", "key": "₰", "mappings": {"default": {"default": "Pfennig"}}, "names": ["₰"]}, {"category": "currency", "key": "₱", "mappings": {"default": {"default": "peso"}}, "names": ["₱"]}, {"category": "currency", "key": "₲", "mappings": {"default": {"default": "guaranis"}}, "names": ["₲"]}, {"category": "currency", "key": "₳", "mappings": {"default": {"default": "austral"}}, "names": ["₳"]}, {"category": "currency", "key": "₴", "mappings": {"default": {"default": "hryvnia"}}, "names": ["₴", "UAH"]}, {"category": "currency", "key": "₵", "mappings": {"default": {"default": "cedis"}}, "names": ["₵", "GHS"]}, {"category": "currency", "key": "₶", "mappings": {"default": {"default": "livre tournois"}}, "names": ["₶"]}, {"category": "currency", "key": "₷", "mappings": {"default": {"default": "spesmilo"}}, "names": ["₷"]}, {"category": "currency", "key": "₸", "mappings": {"default": {"default": "tenge"}}, "names": ["₸", "KZT"]}, {"category": "currency", "key": "₺", "mappings": {"default": {"default": "turkish lira"}}, "names": ["₺", "TRY"]}, {"category": "currency", "key": "元", "mappings": {"default": {"default": "yuan"}}, "names": ["元"]}, {"category": "currency", "key": "¢", "mappings": {"default": {"default": "cent"}}, "names": ["￠", "¢"]}], "en/units/energy.js": [{"locale": "en"}, {"category": "energy", "mappings": {"default": {"default": "watt"}}, "key": "W", "names": ["W", "w"]}, {"category": "energy", "mappings": {"default": {"default": "kilowatt"}}, "key": "kW", "names": ["kw", "kW"]}, {"category": "energy", "mappings": {"default": {"default": "milliwatt"}}, "key": "mW", "names": ["mw", "mW"]}, {"category": "energy", "mappings": {"default": {"default": "kilowatt hour"}}, "key": "kwh", "names": ["kwh", "kWh"]}, {"category": "energy", "mappings": {"default": {"default": "joule"}}, "key": "J", "names": ["J"]}, {"category": "energy", "mappings": {"default": {"default": "<PERSON>"}}, "key": "N", "names": ["N"]}, {"category": "energy", "mappings": {"default": {"default": "ampere"}}, "key": "A", "names": ["A"]}, {"category": "energy", "mappings": {"default": {"default": "volt"}}, "key": "V", "names": ["V"]}, {"category": "energy", "mappings": {"default": {"default": "microohm"}}, "key": "µΩ", "names": ["µΩ"]}, {"category": "energy", "mappings": {"default": {"default": "milliohm"}}, "key": "mΩ", "names": ["mΩ"]}, {"category": "energy", "mappings": {"default": {"default": "ohm"}}, "key": "Ω", "names": ["Ω", "Ohm"]}, {"category": "energy", "mappings": {"default": {"default": "kilohm"}}, "key": "kΩ", "names": ["kΩ", "KΩ"]}, {"category": "energy", "mappings": {"default": {"default": "megaohm"}}, "key": "MΩ", "names": ["MΩ"]}, {"category": "energy", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}, "key": "GΩ", "names": ["GΩ"]}], "en/units/length.js": [{"locale": "en"}, {"category": "length", "mappings": {"default": {"default": "foot"}}, "key": "ft", "names": ["ft", "ft."]}, {"category": "length", "mappings": {"default": {"default": "inch"}}, "key": "in", "names": ["in", "in."]}, {"category": "length", "mappings": {"default": {"default": "mile"}}, "key": "mi", "names": ["mi", "mi."]}, {"category": "length", "mappings": {"default": {"default": "yard"}}, "key": "yd", "names": ["yd", "yd."]}, {"category": "length", "mappings": {"default": {"default": "link"}}, "key": "link", "names": ["li", "li."]}, {"category": "length", "mappings": {"default": {"default": "rod"}}, "key": "rod", "names": ["rd", "rd."]}, {"category": "length", "mappings": {"default": {"default": "chain"}}, "key": "chain", "names": ["ch", "ch."]}, {"category": "length", "mappings": {"default": {"default": "furlong"}}, "key": "furlong", "names": ["fur", "fur."]}, {"category": "length", "mappings": {"default": {"default": "nautical mile"}}, "key": "n.m.", "names": ["n.m."]}, {"category": "length", "mappings": {"default": {"default": "millimeter"}}, "key": "mm", "names": ["mm"]}, {"category": "length", "mappings": {"default": {"default": "centimeter"}}, "key": "cm", "names": ["cm"]}, {"category": "length", "mappings": {"default": {"default": "meter"}}, "key": "m", "names": ["m"]}, {"category": "length", "mappings": {"default": {"default": "kilometer"}}, "key": "km", "names": ["km"]}], "en/units/memory.js": [{"locale": "en"}, {"category": "memory", "mappings": {"default": {"default": "bit"}}, "key": "b", "names": ["b"]}, {"category": "memory", "mappings": {"default": {"default": "byte"}}, "key": "B", "names": ["B"]}, {"category": "memory", "mappings": {"default": {"default": "kilobyte"}}, "key": "KB", "names": ["KB"]}, {"category": "memory", "mappings": {"default": {"default": "megabyte"}}, "key": "MB", "names": ["MB"]}, {"category": "memory", "mappings": {"default": {"default": "gigabyte"}}, "key": "GB", "names": ["GB"]}, {"category": "memory", "mappings": {"default": {"default": "terabyte"}}, "key": "TB", "names": ["TB"]}], "en/units/other.js": [{"locale": "en"}, {"category": "other", "mappings": {"default": {"default": "dozen"}}, "key": "doz", "names": ["doz", "doz.", "dz", "dz."]}], "en/units/speed.js": [{"locale": "en"}, {"category": "speed", "mappings": {"default": {"default": "knot"}}, "key": "kt", "names": ["kt", "kt."]}, {"category": "speed", "mappings": {"default": {"default": "miles per hour"}}, "key": "mph", "names": ["mph"]}, {"category": "speed", "mappings": {"default": {"default": "revolutions per minute"}}, "key": "rpm", "names": ["rpm"]}, {"category": "speed", "mappings": {"default": {"default": "kilometers per hour"}}, "key": "kmh", "names": ["kmh"]}], "en/units/temperature.js": [{"locale": "en"}, {"category": "temperature", "mappings": {"default": {"default": "Fahrenheit"}}, "key": "F", "names": ["F", "F.", "°F"]}, {"category": "temperature", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>", "alternative": "Centigrade"}}, "key": "C", "names": ["C", "°C"]}, {"category": "temperature", "mappings": {"default": {"default": "<PERSON><PERSON>"}}, "key": "K", "names": ["K", "°K"]}], "en/units/time.js": [{"locale": "en"}, {"category": "time", "mappings": {"default": {"default": "nanosecond"}}, "key": "ns", "names": ["ns"]}, {"category": "time", "mappings": {"default": {"default": "microsecond"}}, "key": "µs", "names": ["µs"]}, {"category": "time", "mappings": {"default": {"default": "millisecond"}}, "key": "ms", "names": ["ms"]}, {"category": "time", "mappings": {"default": {"default": "second"}}, "key": "s", "names": ["s"]}, {"category": "time", "mappings": {"default": {"default": "minute"}}, "key": "min", "names": ["min"]}, {"category": "time", "mappings": {"default": {"default": "hour"}}, "key": "h", "names": ["h", "hr"]}], "en/units/volume.js": [{"locale": "en"}, {"category": "volume", "mappings": {"default": {"default": "cubic"}}, "key": "cu", "names": ["cu", "cu."]}, {"category": "volume", "mappings": {"default": {"default": "cubic inch"}}, "key": "cu inch", "names": ["cu in", "cu. in."]}, {"category": "volume", "mappings": {"default": {"default": "cubic foot", "plural": "cubic feet"}}, "key": "cu ft", "names": ["cu ft", "cu. ft."]}, {"category": "volume", "mappings": {"default": {"default": "cubic yard"}}, "key": "cu yd", "names": ["cu yd", "cu. yd."]}, {"category": "volume", "mappings": {"default": {"default": "barrel"}}, "key": "bbl", "names": ["bbl.", "bbl"]}, {"category": "volume", "mappings": {"default": {"default": "fluid ounce"}}, "key": "fl. oz.", "names": ["fl. oz.", "fl oz"]}, {"category": "volume", "mappings": {"default": {"default": "gallon"}}, "key": "gal", "names": ["gal", "gal."]}, {"category": "volume", "mappings": {"default": {"default": "pint"}}, "key": "pt", "names": ["pt", "pt."]}, {"category": "volume", "mappings": {"default": {"default": "quart"}}, "key": "qt", "names": ["qt", "qt."]}, {"category": "volume", "mappings": {"default": {"default": "fluid dram"}}, "key": "fluid dram", "names": ["fl dr", "fl. dr."]}, {"category": "volume", "mappings": {"default": {"default": "tablespoon"}}, "key": "tbsp", "names": ["tbsp", "tbsp.", "Tbsp", "Tbsp."]}, {"category": "volume", "mappings": {"default": {"default": "teaspoon"}}, "key": "tsp", "names": ["tsp", "tsp."]}, {"category": "volume", "mappings": {"default": {"default": "cup"}}, "key": "cup", "names": ["cp", "cp."]}, {"category": "volume", "mappings": {"default": {"default": "cubic centimeter"}}, "key": "cc", "names": ["cc", "ccm"]}, {"category": "volume", "mappings": {"default": {"default": "kiloliter"}}, "key": "kl", "names": ["kl"]}, {"category": "volume", "mappings": {"default": {"default": "liter"}}, "key": "l", "names": ["l"]}, {"category": "volume", "mappings": {"default": {"default": "milliliter"}}, "key": "ml", "names": ["ml"]}], "en/units/weight.js": [{"locale": "en"}, {"category": "weight", "mappings": {"default": {"default": "dram"}}, "key": "dram", "names": ["dr", "dr."]}, {"category": "weight", "mappings": {"default": {"default": "ounce"}}, "key": "oz", "names": ["oz", "oz."]}, {"category": "weight", "mappings": {"default": {"default": "pound"}}, "key": "lb", "names": ["lb", "lb."]}, {"category": "weight", "mappings": {"default": {"default": "stone"}}, "key": "st", "names": ["st", "st."]}, {"category": "weight", "mappings": {"default": {"default": "quarter"}}, "key": "qtr", "names": ["qtr", "qtr."]}, {"category": "weight", "mappings": {"default": {"default": "hundredweight"}}, "key": "cwt", "names": ["cwt", "cwt."]}, {"category": "weight", "mappings": {"default": {"default": "long ton"}}, "key": "LT", "names": ["LT", "L.T."]}, {"category": "weight", "mappings": {"default": {"default": "gram"}}, "key": "gr", "names": ["g", "gr"]}, {"category": "weight", "mappings": {"default": {"default": "kilogram"}}, "key": "kg", "names": ["kg"]}, {"category": "weight", "mappings": {"default": {"default": "microgram"}}, "key": "mcg", "names": ["mcg", "µg"]}, {"category": "weight", "mappings": {"default": {"default": "milligram"}}, "key": "mg", "names": ["mg"]}, {"category": "weight", "mappings": {"default": {"default": "ton"}}, "key": "t", "names": ["t", "T"]}]}