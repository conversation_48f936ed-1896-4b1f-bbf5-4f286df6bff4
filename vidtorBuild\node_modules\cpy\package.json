{"name": "cpy", "version": "12.0.1", "description": "Copy files", "license": "MIT", "repository": "sindresorhus/cpy", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["cpy-error.js", "glob-pattern.js", "index.js", "index.d.ts"], "keywords": ["copy", "cp", "cpy", "file", "files", "clone", "fs", "stream", "glob", "file-system", "ncp", "fast", "quick", "data", "content", "contents", "cpx", "directory", "directories"], "dependencies": {"copy-file": "^11.1.0", "globby": "^14.1.0", "junk": "^4.0.1", "micromatch": "^4.0.8", "p-filter": "^4.1.0", "p-map": "^7.0.3"}, "devDependencies": {"ava": "^6.4.1", "proxyquire": "^2.1.3", "rimraf": "^6.0.1", "tempy": "^3.1.0", "tsd": "^0.33.0", "xo": "^1.2.1"}, "xo": {"rules": {"unicorn/prefer-event-target": "off"}}}