{"name": "vidtorbuild", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "prebuild": "node scripts/copy-vditor-assets.cjs", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vditor": "^3.11.1", "vue": "^3.5.18"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "cpy-cli": "^6.0.0", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}