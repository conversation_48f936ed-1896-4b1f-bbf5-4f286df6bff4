/*!
  Theme: One Light
  Author: <PERSON> (http://github.com/purple<PERSON>rot)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#383a42;background:#fafafa}.hljs ::selection,.hljs::selection{background-color:#e5e5e6;color:#383a42}.hljs-comment{color:#a0a1a7}.hljs-tag{color:#696c77}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#383a42}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ca1243}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#d75f00}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#c18401}.hljs-strong{font-weight:700;color:#c18401}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#50a14f}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#0184bc}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#4078f2}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#a626a4}.hljs-emphasis{color:#a626a4;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#986801}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
