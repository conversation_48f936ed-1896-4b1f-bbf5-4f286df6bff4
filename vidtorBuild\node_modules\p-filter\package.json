{"name": "p-filter", "version": "4.1.0", "description": "Filter promises concurrently", "license": "MIT", "repository": "sindresorhus/p-filter", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "filter", "collection", "iterable", "iterator", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel"], "dependencies": {"p-map": "^7.0.1"}, "devDependencies": {"ava": "^6.0.1", "tsd": "^0.30.1", "xo": "^0.56.0"}}