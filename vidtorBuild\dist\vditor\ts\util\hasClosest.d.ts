export declare const hasTopClosestByClassName: (element: Node, className: string) => false | HTMLElement;
export declare const hasTopClosestByAttribute: (element: Node, attr: string, value: string) => false | HTMLElement;
export declare const hasTopClosestByTag: (element: Node, nodeName: string) => false | HTMLElement;
export declare const getTopList: (element: Node) => false | HTMLElement;
export declare const hasClosestByAttribute: (element: Node, attr: string, value: string) => false | HTMLElement;
export declare const hasClosestBlock: (element: Node) => false | HTMLElement;
export declare const hasClosestByMatchTag: (element: Node, nodeName: string) => false | HTMLElement;
export declare const hasClosestByClassName: (element: Node, className: string) => false | HTMLElement;
export declare const getLastNode: (node: Node) => Node;
