/*!
  Theme: Green Screen
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#0b0;background:#010}.hljs ::selection,.hljs::selection{background-color:#050;color:#0b0}.hljs-comment{color:#070}.hljs-tag{color:#090}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#0b0}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#070}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#090}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#070}.hljs-strong{font-weight:700;color:#070}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#0b0}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#050}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#090}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#0b0}.hljs-emphasis{color:#0b0;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#050}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
