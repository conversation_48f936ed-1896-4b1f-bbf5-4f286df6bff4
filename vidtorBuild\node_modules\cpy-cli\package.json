{"name": "cpy-cli", "version": "6.0.0", "description": "Copy files", "license": "MIT", "repository": "sindresorhus/cpy-cli", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "maintainers": [{"name": "<PERSON>", "email": "micha<PERSON>@schnittstabil.de", "url": "schnittstabil.de"}], "type": "module", "bin": {"cpy": "./cli.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava"}, "files": ["cli.js"], "keywords": ["cli-app", "cli", "copy", "cp", "cpy", "file", "files", "clone", "fs", "stream", "glob", "file-system", "ncp", "fast", "quick", "data", "content", "contents"], "dependencies": {"cpy": "^12.0.0", "meow": "^13.2.0"}, "devDependencies": {"ava": "^6.4.1", "execa": "^9.6.0", "path-exists": "^5.0.0", "tempfile": "^5.0.0", "xo": "^1.2.1"}}