/*!
  Theme: IR Black
  Author: <PERSON><PERSON><PERSON> (http://timotheepoisot.fr)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#b5b3aa;background:#000}.hljs ::selection,.hljs::selection{background-color:#484844;color:#b5b3aa}.hljs-comment{color:#6c6c66}.hljs-tag{color:#918f88}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#b5b3aa}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ff6c60}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#e9c062}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ffffb6}.hljs-strong{font-weight:700;color:#ffffb6}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#a8ff60}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#c6c5fe}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#96cbfe}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ff73fd}.hljs-emphasis{color:#ff73fd;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b18a3d}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
