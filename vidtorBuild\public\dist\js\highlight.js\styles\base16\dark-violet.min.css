/*!
  Theme: Dark Violet
  Author: ruler501 (https://github.com/ruler501/base16-darkviolet)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#b08ae6;background:#000}.hljs ::selection,.hljs::selection{background-color:#432d59;color:#b08ae6}.hljs-comment{color:#593380}.hljs-tag{color:#0f0}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#b08ae6}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#a82ee6}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#b6c}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#f29df2}.hljs-strong{font-weight:700;color:#f29df2}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#4595e6}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#40dfff}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#4136d9}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#7e5ce6}.hljs-emphasis{color:#7e5ce6;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#a886bf}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
