/*!
  Theme: Nova
  Author: <PERSON> (https://github.com/gessig), <PERSON> (https://trevordmiller.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#c5d4dd;background:#3c4c55}.hljs ::selection,.hljs::selection{background-color:#6a7d89;color:#c5d4dd}.hljs-comment,.hljs-tag{color:#899ba6}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#c5d4dd}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#83afe5}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#7fc1ca}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#a8ce93}.hljs-strong{font-weight:700;color:#a8ce93}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#7fc1ca}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#f2c38f}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#83afe5}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#9a93e1}.hljs-emphasis{color:#9a93e1;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#f2c38f}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
