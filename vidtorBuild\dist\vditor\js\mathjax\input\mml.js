!function(t){var e={};function r(o){if(e[o])return e[o].exports;var a=e[o]={i:o,l:!1,exports:{}};return t[o].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=t,r.c=e,r.d=function(t,e,o){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)r.d(o,a,function(e){return t[e]}.bind(null,a));return o},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=10)}([function(t,e,r){"use strict";var o,a=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,a,i=r.call(t),n=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)n.push(o.value)}catch(t){a={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return n};Object.defineProperty(e,"__esModule",{value:!0}),e.MathML=void 0;var n=r(5),s=r(3),l=r(6),c=r(1),u=r(2),h=function(t){function e(e){void 0===e&&(e={});var r=this,o=i(s.separateOptions(e,c.FindMathML.OPTIONS,u.MathMLCompile.OPTIONS),3),a=o[0],n=o[1],h=o[2];return(r=t.call(this,a)||this).findMathML=r.options.FindMathML||new c.FindMathML(n),r.mathml=r.options.MathMLCompile||new u.MathMLCompile(h),r.mmlFilters=new l.FunctionList,r}return a(e,t),e.prototype.setAdaptor=function(e){t.prototype.setAdaptor.call(this,e),this.findMathML.adaptor=e,this.mathml.adaptor=e},e.prototype.setMmlFactory=function(e){t.prototype.setMmlFactory.call(this,e),this.mathml.setMmlFactory(e)},Object.defineProperty(e.prototype,"processStrings",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.compile=function(t,e){var r=t.start.node;if(!r||!t.end.node||this.options.forceReparse||"#text"===this.adaptor.kind(r)){var o=this.executeFilters(this.preFilters,t,e,t.math||"<math></math>"),a=this.checkForErrors(this.adaptor.parse(o,"text/"+this.options.parseAs)),i=this.adaptor.body(a);1!==this.adaptor.childNodes(i).length&&this.error("MathML must consist of a single element"),r=this.adaptor.remove(this.adaptor.firstChild(i)),"math"!==this.adaptor.kind(r).replace(/^[a-z]+:/,"")&&this.error("MathML must be formed by a <math> element, not <"+this.adaptor.kind(r)+">")}return r=this.executeFilters(this.mmlFilters,t,e,r),this.executeFilters(this.postFilters,t,e,this.mathml.compile(r))},e.prototype.checkForErrors=function(t){var e=this.adaptor.tags(this.adaptor.body(t),"parsererror")[0];return e&&(""===this.adaptor.textContent(e)&&this.error("Error processing MathML"),this.options.parseError.call(this,e)),t},e.prototype.error=function(t){throw new Error(t)},e.prototype.findMath=function(t){return this.findMathML.findMath(t)},e.NAME="MathML",e.OPTIONS=s.defaultOptions({parseAs:"html",forceReparse:!1,FindMathML:null,MathMLCompile:null,parseError:function(t){this.error(this.adaptor.textContent(t).replace(/\n.*/g,""))}},n.AbstractInputJax.OPTIONS),e}(n.AbstractInputJax);e.MathML=h},function(t,e,r){"use strict";var o,a=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.FindMathML=void 0;var n=r(7),s="http://www.w3.org/1998/Math/MathML",l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.findMath=function(t){var e=new Set;this.findMathNodes(t,e),this.findMathPrefixed(t,e);var r=this.adaptor.root(this.adaptor.document);return"html"===this.adaptor.kind(r)&&0===e.size&&this.findMathNS(t,e),this.processMath(e)},e.prototype.findMathNodes=function(t,e){var r,o;try{for(var a=i(this.adaptor.tags(t,"math")),n=a.next();!n.done;n=a.next()){var s=n.value;e.add(s)}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}},e.prototype.findMathPrefixed=function(t,e){var r,o,a,n,l=this.adaptor.root(this.adaptor.document);try{for(var c=i(this.adaptor.allAttributes(l)),u=c.next();!u.done;u=c.next()){var h=u.value;if("xmlns:"===h.name.substr(0,6)&&h.value===s){var p=h.name.substr(6);try{for(var d=(a=void 0,i(this.adaptor.tags(t,p+":math"))),f=d.next();!f.done;f=d.next()){var M=f.value;e.add(M)}}catch(t){a={error:t}}finally{try{f&&!f.done&&(n=d.return)&&n.call(d)}finally{if(a)throw a.error}}}}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}},e.prototype.findMathNS=function(t,e){var r,o;try{for(var a=i(this.adaptor.tags(t,"math",s)),n=a.next();!n.done;n=a.next()){var l=n.value;e.add(l)}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}},e.prototype.processMath=function(t){var e,r,o=[];try{for(var a=i(Array.from(t)),n=a.next();!n.done;n=a.next()){var s=n.value,l="block"===this.adaptor.getAttribute(s,"display")||"display"===this.adaptor.getAttribute(s,"mode"),c={node:s,n:0,delim:""},u={node:s,n:0,delim:""};o.push({math:this.adaptor.outerHTML(s),start:c,end:u,display:l})}}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return o},e.OPTIONS={},e}(n.AbstractFindMath);e.FindMathML=l},function(t,e,r){"use strict";var o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MathMLCompile=void 0;var i=r(8),n=r(3),s=r(9),l=function(){function t(t){void 0===t&&(t={});var e=this.constructor;this.options=n.userOptions(n.defaultOptions({},e.OPTIONS),t)}return t.prototype.setMmlFactory=function(t){this.factory=t},t.prototype.compile=function(t){var e=this.makeNode(t);return e.verifyTree(this.options.verify),e.setInheritedAttributes({},!1,0,!1),e.walkTree(this.markMrows),e},t.prototype.makeNode=function(t){var e,r,o=this.adaptor,n=!1,s=o.kind(t).replace(/^.*:/,""),l=o.getAttribute(t,"data-mjx-texclass")||"";l&&(l=this.filterAttribute("data-mjx-texclass",l)||"");var c=l&&"mrow"===s?"TeXAtom":s;try{for(var u=a(this.filterClassList(o.allClasses(t))),h=u.next();!h.done;h=u.next()){var p=h.value;p.match(/^MJX-TeXAtom-/)?(l=p.substr(12),c="TeXAtom"):"MJX-fixedlimits"===p&&(n=!0)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}this.factory.getNodeClass(c)||this.error('Unknown node type "'+c+'"');var d=this.factory.create(c);return"TeXAtom"===c?this.texAtom(d,l,n):l&&(d.texClass=i.TEXCLASS[l],d.setProperty("texClass",d.texClass)),this.addAttributes(d,t),this.checkClass(d,t),this.addChildren(d,t),d},t.prototype.addAttributes=function(t,e){var r,o,i=!1;try{for(var n=a(this.adaptor.allAttributes(e)),s=n.next();!s.done;s=n.next()){var l=s.value,c=l.name,u=this.filterAttribute(c,l.value);if(null===u)return;if("data-mjx-"===c.substr(0,9))"data-mjx-alternate"===c?t.setProperty("variantForm",!0):"data-mjx-variant"===c&&(t.attributes.set("mathvariant",u),i=!0);else if("class"!==c){var h=u.toLowerCase();"true"===h||"false"===h?t.attributes.set(c,"true"===h):i&&"mathvariant"===c||t.attributes.set(c,u)}}}catch(t){r={error:t}}finally{try{s&&!s.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}},t.prototype.filterAttribute=function(t,e){return e},t.prototype.filterClassList=function(t){return t},t.prototype.addChildren=function(t,e){var r,o;if(0!==t.arity){var i=this.adaptor;try{for(var n=a(i.childNodes(e)),s=n.next();!s.done;s=n.next()){var l=s.value,c=i.kind(l);if("#comment"!==c)if("#text"===c)this.addText(t,l);else if(t.isKind("annotation-xml"))t.appendChild(this.factory.create("XML").setXML(l,i));else{var u=t.appendChild(this.makeNode(l));0===u.arity&&i.childNodes(l).length&&(this.options.fixMisplacedChildren?this.addChildren(t,l):u.mError("There should not be children for "+u.kind+" nodes",this.options.verify,!0))}}}catch(t){r={error:t}}finally{try{s&&!s.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}}},t.prototype.addText=function(t,e){var r=this.adaptor.value(e);(t.isToken||t.getProperty("isChars"))&&t.arity?(t.isToken&&(r=s.translate(r),r=this.trimSpace(r)),t.appendChild(this.factory.create("text").setText(r))):r.match(/\S/)&&this.error('Unexpected text node "'+r+'"')},t.prototype.checkClass=function(t,e){var r,o,i=[];try{for(var n=a(this.filterClassList(this.adaptor.allClasses(e))),s=n.next();!s.done;s=n.next()){var l=s.value;"MJX-"===l.substr(0,4)?"MJX-variant"===l?t.setProperty("variantForm",!0):"MJX-TeXAtom"!==l.substr(0,11)&&t.attributes.set("mathvariant",this.fixCalligraphic(l.substr(3))):i.push(l)}}catch(t){r={error:t}}finally{try{s&&!s.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}i.length&&t.attributes.set("class",i.join(" "))},t.prototype.fixCalligraphic=function(t){return t.replace(/caligraphic/,"calligraphic")},t.prototype.texAtom=function(t,e,r){t.texClass=i.TEXCLASS[e],t.setProperty("texClass",t.texClass),"OP"!==e||r||(t.setProperty("movesupsub",!0),t.attributes.setInherited("movablelimits",!0))},t.prototype.markMrows=function(t){if(t.isKind("mrow")&&!t.isInferred&&t.childNodes.length>=2){var e=t.childNodes[0],r=t.childNodes[t.childNodes.length-1];e.isKind("mo")&&e.attributes.get("fence")&&r.isKind("mo")&&r.attributes.get("fence")&&(e.childNodes.length&&t.setProperty("open",e.getText()),r.childNodes.length&&t.setProperty("close",r.getText()))}},t.prototype.trimSpace=function(t){return t.replace(/[\t\n\r]/g," ").replace(/^ +/,"").replace(/ +$/,"").replace(/  +/g," ")},t.prototype.error=function(t){throw new Error(t)},t.OPTIONS={MmlFactory:null,fixMisplacedChildren:!0,verify:o({},i.AbstractMmlNode.verifyDefaults),translateEntities:!0},t}();e.MathMLCompile=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractInputJax=MathJax._.core.InputJax.AbstractInputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FunctionList=MathJax._.util.FunctionList.FunctionList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFindMath=MathJax._.core.FindMath.AbstractFindMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.options=MathJax._.util.Entities.options,e.entities=MathJax._.util.Entities.entities,e.add=MathJax._.util.Entities.add,e.remove=MathJax._.util.Entities.remove,e.translate=MathJax._.util.Entities.translate,e.numeric=MathJax._.util.Entities.numeric},function(t,e,r){"use strict";r.r(e);var o=r(4),a=r(0),i=r(1),n=r(2);Object(o.combineWithMathJax)({_:{input:{mathml_ts:a,mathml:{FindMathML:i,MathMLCompile:n}}}}),MathJax.startup&&(MathJax.startup.registerConstructor("mml",a.MathML),MathJax.startup.useInput("mml"))}]);