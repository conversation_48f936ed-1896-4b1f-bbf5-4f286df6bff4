(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))p(h);new MutationObserver(h=>{for(const S of h)if(S.type==="childList")for(const M of S.addedNodes)M.tagName==="LINK"&&M.rel==="modulepreload"&&p(M)}).observe(document,{childList:!0,subtree:!0});function l(h){const S={};return h.integrity&&(S.integrity=h.integrity),h.referrerPolicy&&(S.referrerPolicy=h.referrerPolicy),h.crossOrigin==="use-credentials"?S.credentials="include":h.crossOrigin==="anonymous"?S.credentials="omit":S.credentials="same-origin",S}function p(h){if(h.ep)return;h.ep=!0;const S=l(h);fetch(h.href,S)}})();/**
* @vue/shared v3.5.19
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function hi(r){const s=Object.create(null);for(const l of r.split(","))s[l]=1;return l=>l in s}const Oe={},gn=[],_t=()=>{},Ja=()=>!1,wr=r=>r.charCodeAt(0)===111&&r.charCodeAt(1)===110&&(r.charCodeAt(2)>122||r.charCodeAt(2)<97),mi=r=>r.startsWith("onUpdate:"),Ye=Object.assign,gi=(r,s)=>{const l=r.indexOf(s);l>-1&&r.splice(l,1)},Ol=Object.prototype.hasOwnProperty,xe=(r,s)=>Ol.call(r,s),be=Array.isArray,Dn=r=>vr(r)==="[object Map]",Dl=r=>vr(r)==="[object Set]",ve=r=>typeof r=="function",$e=r=>typeof r=="string",En=r=>typeof r=="symbol",We=r=>r!==null&&typeof r=="object",Za=r=>(We(r)||ve(r))&&ve(r.then)&&ve(r.catch),Hl=Object.prototype.toString,vr=r=>Hl.call(r),Rl=r=>vr(r).slice(8,-1),Nl=r=>vr(r)==="[object Object]",yi=r=>$e(r)&&r!=="NaN"&&r[0]!=="-"&&""+parseInt(r,10)===r,Hn=hi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Er=r=>{const s=Object.create(null);return(l=>s[l]||(s[l]=r(l)))},Il=/-(\w)/g,Bt=Er(r=>r.replace(Il,(s,l)=>l?l.toUpperCase():"")),Pl=/\B([A-Z])/g,Zt=Er(r=>r.replace(Pl,"-$1").toLowerCase()),Xa=Er(r=>r.charAt(0).toUpperCase()+r.slice(1)),zr=Er(r=>r?`on${Xa(r)}`:""),ft=(r,s)=>!Object.is(r,s),Gr=(r,...s)=>{for(let l=0;l<r.length;l++)r[l](...s)},Ya=(r,s,l,p=!1)=>{Object.defineProperty(r,s,{configurable:!0,enumerable:!1,writable:p,value:l})},jl=r=>{const s=parseFloat(r);return isNaN(s)?r:s};let _a;const _r=()=>_a||(_a=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function bi(r){if(be(r)){const s={};for(let l=0;l<r.length;l++){const p=r[l],h=$e(p)?Fl(p):bi(p);if(h)for(const S in h)s[S]=h[S]}return s}else if($e(r)||We(r))return r}const Bl=/;(?![^(]*\))/g,Ul=/:([^]+)/,Vl=/\/\*[^]*?\*\//g;function Fl(r){const s={};return r.replace(Vl,"").split(Bl).forEach(l=>{if(l){const p=l.split(Ul);p.length>1&&(s[p[0].trim()]=p[1].trim())}}),s}function wi(r){let s="";if($e(r))s=r;else if(be(r))for(let l=0;l<r.length;l++){const p=wi(r[l]);p&&(s+=p+" ")}else if(We(r))for(const l in r)r[l]&&(s+=l+" ");return s.trim()}const Wl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Kl=hi(Wl);function Qa(r){return!!r||r===""}function ql(r){return r==null?"initial":typeof r=="string"?r===""?" ":r:String(r)}/**
* @vue/reactivity v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ut;class zl{constructor(s=!1){this.detached=s,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ut,!s&&ut&&(this.index=(ut.scopes||(ut.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let s,l;if(this.scopes)for(s=0,l=this.scopes.length;s<l;s++)this.scopes[s].pause();for(s=0,l=this.effects.length;s<l;s++)this.effects[s].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let s,l;if(this.scopes)for(s=0,l=this.scopes.length;s<l;s++)this.scopes[s].resume();for(s=0,l=this.effects.length;s<l;s++)this.effects[s].resume()}}run(s){if(this._active){const l=ut;try{return ut=this,s()}finally{ut=l}}}on(){++this._on===1&&(this.prevScope=ut,ut=this)}off(){this._on>0&&--this._on===0&&(ut=this.prevScope,this.prevScope=void 0)}stop(s){if(this._active){this._active=!1;let l,p;for(l=0,p=this.effects.length;l<p;l++)this.effects[l].stop();for(this.effects.length=0,l=0,p=this.cleanups.length;l<p;l++)this.cleanups[l]();if(this.cleanups.length=0,this.scopes){for(l=0,p=this.scopes.length;l<p;l++)this.scopes[l].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!s){const h=this.parent.scopes.pop();h&&h!==this&&(this.parent.scopes[this.index]=h,h.index=this.index)}this.parent=void 0}}}function Gl(){return ut}let je;const $r=new WeakSet;class es{constructor(s){this.fn=s,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ut&&ut.active&&ut.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,$r.has(this)&&($r.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ns(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Sa(this),rs(this);const s=je,l=St;je=this,St=!0;try{return this.fn()}finally{is(this),je=s,St=l,this.flags&=-3}}stop(){if(this.flags&1){for(let s=this.deps;s;s=s.nextDep)_i(s);this.deps=this.depsTail=void 0,Sa(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?$r.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ni(this)&&this.run()}get dirty(){return ni(this)}}let ts=0,Rn,Nn;function ns(r,s=!1){if(r.flags|=8,s){r.next=Nn,Nn=r;return}r.next=Rn,Rn=r}function vi(){ts++}function Ei(){if(--ts>0)return;if(Nn){let s=Nn;for(Nn=void 0;s;){const l=s.next;s.next=void 0,s.flags&=-9,s=l}}let r;for(;Rn;){let s=Rn;for(Rn=void 0;s;){const l=s.next;if(s.next=void 0,s.flags&=-9,s.flags&1)try{s.trigger()}catch(p){r||(r=p)}s=l}}if(r)throw r}function rs(r){for(let s=r.deps;s;s=s.nextDep)s.version=-1,s.prevActiveLink=s.dep.activeLink,s.dep.activeLink=s}function is(r){let s,l=r.depsTail,p=l;for(;p;){const h=p.prevDep;p.version===-1?(p===l&&(l=h),_i(p),$l(p)):s=p,p.dep.activeLink=p.prevActiveLink,p.prevActiveLink=void 0,p=h}r.deps=s,r.depsTail=l}function ni(r){for(let s=r.deps;s;s=s.nextDep)if(s.dep.version!==s.version||s.dep.computed&&(as(s.dep.computed)||s.dep.version!==s.version))return!0;return!!r._dirty}function as(r){if(r.flags&4&&!(r.flags&16)||(r.flags&=-17,r.globalVersion===Fn)||(r.globalVersion=Fn,!r.isSSR&&r.flags&128&&(!r.deps&&!r._dirty||!ni(r))))return;r.flags|=2;const s=r.dep,l=je,p=St;je=r,St=!0;try{rs(r);const h=r.fn(r._value);(s.version===0||ft(h,r._value))&&(r.flags|=128,r._value=h,s.version++)}catch(h){throw s.version++,h}finally{je=l,St=p,is(r),r.flags&=-3}}function _i(r,s=!1){const{dep:l,prevSub:p,nextSub:h}=r;if(p&&(p.nextSub=h,r.prevSub=void 0),h&&(h.prevSub=p,r.nextSub=void 0),l.subs===r&&(l.subs=p,!p&&l.computed)){l.computed.flags&=-5;for(let S=l.computed.deps;S;S=S.nextDep)_i(S,!0)}!s&&!--l.sc&&l.map&&l.map.delete(l.key)}function $l(r){const{prevDep:s,nextDep:l}=r;s&&(s.nextDep=l,r.prevDep=void 0),l&&(l.prevDep=s,r.nextDep=void 0)}let St=!0;const ss=[];function Ut(){ss.push(St),St=!1}function Vt(){const r=ss.pop();St=r===void 0?!0:r}function Sa(r){const{cleanup:s}=r;if(r.cleanup=void 0,s){const l=je;je=void 0;try{s()}finally{je=l}}}let Fn=0;class Jl{constructor(s,l){this.sub=s,this.dep=l,this.version=l.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Sr{constructor(s){this.computed=s,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(s){if(!je||!St||je===this.computed)return;let l=this.activeLink;if(l===void 0||l.sub!==je)l=this.activeLink=new Jl(je,this),je.deps?(l.prevDep=je.depsTail,je.depsTail.nextDep=l,je.depsTail=l):je.deps=je.depsTail=l,os(l);else if(l.version===-1&&(l.version=this.version,l.nextDep)){const p=l.nextDep;p.prevDep=l.prevDep,l.prevDep&&(l.prevDep.nextDep=p),l.prevDep=je.depsTail,l.nextDep=void 0,je.depsTail.nextDep=l,je.depsTail=l,je.deps===l&&(je.deps=p)}return l}trigger(s){this.version++,Fn++,this.notify(s)}notify(s){vi();try{for(let l=this.subs;l;l=l.prevSub)l.sub.notify()&&l.sub.dep.notify()}finally{Ei()}}}function os(r){if(r.dep.sc++,r.sub.flags&4){const s=r.dep.computed;if(s&&!r.dep.subs){s.flags|=20;for(let p=s.deps;p;p=p.nextDep)os(p)}const l=r.dep.subs;l!==r&&(r.prevSub=l,l&&(l.nextSub=r)),r.dep.subs=r}}const ri=new WeakMap,tn=Symbol(""),ii=Symbol(""),Wn=Symbol("");function et(r,s,l){if(St&&je){let p=ri.get(r);p||ri.set(r,p=new Map);let h=p.get(l);h||(p.set(l,h=new Sr),h.map=p,h.key=l),h.track()}}function jt(r,s,l,p,h,S){const M=ri.get(r);if(!M){Fn++;return}const m=_=>{_&&_.trigger()};if(vi(),s==="clear")M.forEach(m);else{const _=be(r),D=_&&yi(l);if(_&&l==="length"){const k=Number(p);M.forEach((u,c)=>{(c==="length"||c===Wn||!En(c)&&c>=k)&&m(u)})}else switch((l!==void 0||M.has(void 0))&&m(M.get(l)),D&&m(M.get(Wn)),s){case"add":_?D&&m(M.get("length")):(m(M.get(tn)),Dn(r)&&m(M.get(ii)));break;case"delete":_||(m(M.get(tn)),Dn(r)&&m(M.get(ii)));break;case"set":Dn(r)&&m(M.get(tn));break}}Ei()}function hn(r){const s=ke(r);return s===r?s:(et(s,"iterate",Wn),Ct(r)?s:s.map(rt))}function Si(r){return et(r=ke(r),"iterate",Wn),r}const Zl={__proto__:null,[Symbol.iterator](){return Jr(this,Symbol.iterator,rt)},concat(...r){return hn(this).concat(...r.map(s=>be(s)?hn(s):s))},entries(){return Jr(this,"entries",r=>(r[1]=rt(r[1]),r))},every(r,s){return It(this,"every",r,s,void 0,arguments)},filter(r,s){return It(this,"filter",r,s,l=>l.map(rt),arguments)},find(r,s){return It(this,"find",r,s,rt,arguments)},findIndex(r,s){return It(this,"findIndex",r,s,void 0,arguments)},findLast(r,s){return It(this,"findLast",r,s,rt,arguments)},findLastIndex(r,s){return It(this,"findLastIndex",r,s,void 0,arguments)},forEach(r,s){return It(this,"forEach",r,s,void 0,arguments)},includes(...r){return Zr(this,"includes",r)},indexOf(...r){return Zr(this,"indexOf",r)},join(r){return hn(this).join(r)},lastIndexOf(...r){return Zr(this,"lastIndexOf",r)},map(r,s){return It(this,"map",r,s,void 0,arguments)},pop(){return kn(this,"pop")},push(...r){return kn(this,"push",r)},reduce(r,...s){return Ca(this,"reduce",r,s)},reduceRight(r,...s){return Ca(this,"reduceRight",r,s)},shift(){return kn(this,"shift")},some(r,s){return It(this,"some",r,s,void 0,arguments)},splice(...r){return kn(this,"splice",r)},toReversed(){return hn(this).toReversed()},toSorted(r){return hn(this).toSorted(r)},toSpliced(...r){return hn(this).toSpliced(...r)},unshift(...r){return kn(this,"unshift",r)},values(){return Jr(this,"values",rt)}};function Jr(r,s,l){const p=Si(r),h=p[s]();return p!==r&&!Ct(r)&&(h._next=h.next,h.next=()=>{const S=h._next();return S.value&&(S.value=l(S.value)),S}),h}const Xl=Array.prototype;function It(r,s,l,p,h,S){const M=Si(r),m=M!==r&&!Ct(r),_=M[s];if(_!==Xl[s]){const u=_.apply(r,S);return m?rt(u):u}let D=l;M!==r&&(m?D=function(u,c){return l.call(this,rt(u),c,r)}:l.length>2&&(D=function(u,c){return l.call(this,u,c,r)}));const k=_.call(M,D,p);return m&&h?h(k):k}function Ca(r,s,l,p){const h=Si(r);let S=l;return h!==r&&(Ct(r)?l.length>3&&(S=function(M,m,_){return l.call(this,M,m,_,r)}):S=function(M,m,_){return l.call(this,M,rt(m),_,r)}),h[s](S,...p)}function Zr(r,s,l){const p=ke(r);et(p,"iterate",Wn);const h=p[s](...l);return(h===-1||h===!1)&&Li(l[0])?(l[0]=ke(l[0]),p[s](...l)):h}function kn(r,s,l=[]){Ut(),vi();const p=ke(r)[s].apply(r,l);return Ei(),Vt(),p}const Yl=hi("__proto__,__v_isRef,__isVue"),ls=new Set(Object.getOwnPropertyNames(Symbol).filter(r=>r!=="arguments"&&r!=="caller").map(r=>Symbol[r]).filter(En));function Ql(r){En(r)||(r=String(r));const s=ke(this);return et(s,"has",r),s.hasOwnProperty(r)}class cs{constructor(s=!1,l=!1){this._isReadonly=s,this._isShallow=l}get(s,l,p){if(l==="__v_skip")return s.__v_skip;const h=this._isReadonly,S=this._isShallow;if(l==="__v_isReactive")return!h;if(l==="__v_isReadonly")return h;if(l==="__v_isShallow")return S;if(l==="__v_raw")return p===(h?S?cc:ps:S?ds:fs).get(s)||Object.getPrototypeOf(s)===Object.getPrototypeOf(p)?s:void 0;const M=be(s);if(!h){let _;if(M&&(_=Zl[l]))return _;if(l==="hasOwnProperty")return Ql}const m=Reflect.get(s,l,tt(s)?s:p);return(En(l)?ls.has(l):Yl(l))||(h||et(s,"get",l),S)?m:tt(m)?M&&yi(l)?m:m.value:We(m)?h?hs(m):Ti(m):m}}class us extends cs{constructor(s=!1){super(!1,s)}set(s,l,p,h){let S=s[l];if(!this._isShallow){const _=nn(S);if(!Ct(p)&&!nn(p)&&(S=ke(S),p=ke(p)),!be(s)&&tt(S)&&!tt(p))return _||(S.value=p),!0}const M=be(s)&&yi(l)?Number(l)<s.length:xe(s,l),m=Reflect.set(s,l,p,tt(s)?s:h);return s===ke(h)&&(M?ft(p,S)&&jt(s,"set",l,p):jt(s,"add",l,p)),m}deleteProperty(s,l){const p=xe(s,l);s[l];const h=Reflect.deleteProperty(s,l);return h&&p&&jt(s,"delete",l,void 0),h}has(s,l){const p=Reflect.has(s,l);return(!En(l)||!ls.has(l))&&et(s,"has",l),p}ownKeys(s){return et(s,"iterate",be(s)?"length":tn),Reflect.ownKeys(s)}}class ec extends cs{constructor(s=!1){super(!0,s)}set(s,l){return!0}deleteProperty(s,l){return!0}}const tc=new us,nc=new ec,rc=new us(!0);const ai=r=>r,ir=r=>Reflect.getPrototypeOf(r);function ic(r,s,l){return function(...p){const h=this.__v_raw,S=ke(h),M=Dn(S),m=r==="entries"||r===Symbol.iterator&&M,_=r==="keys"&&M,D=h[r](...p),k=l?ai:s?si:rt;return!s&&et(S,"iterate",_?ii:tn),{next(){const{value:u,done:c}=D.next();return c?{value:u,done:c}:{value:m?[k(u[0]),k(u[1])]:k(u),done:c}},[Symbol.iterator](){return this}}}}function ar(r){return function(...s){return r==="delete"?!1:r==="clear"?void 0:this}}function ac(r,s){const l={get(h){const S=this.__v_raw,M=ke(S),m=ke(h);r||(ft(h,m)&&et(M,"get",h),et(M,"get",m));const{has:_}=ir(M),D=s?ai:r?si:rt;if(_.call(M,h))return D(S.get(h));if(_.call(M,m))return D(S.get(m));S!==M&&S.get(h)},get size(){const h=this.__v_raw;return!r&&et(ke(h),"iterate",tn),Reflect.get(h,"size",h)},has(h){const S=this.__v_raw,M=ke(S),m=ke(h);return r||(ft(h,m)&&et(M,"has",h),et(M,"has",m)),h===m?S.has(h):S.has(h)||S.has(m)},forEach(h,S){const M=this,m=M.__v_raw,_=ke(m),D=s?ai:r?si:rt;return!r&&et(_,"iterate",tn),m.forEach((k,u)=>h.call(S,D(k),D(u),M))}};return Ye(l,r?{add:ar("add"),set:ar("set"),delete:ar("delete"),clear:ar("clear")}:{add(h){!s&&!Ct(h)&&!nn(h)&&(h=ke(h));const S=ke(this);return ir(S).has.call(S,h)||(S.add(h),jt(S,"add",h,h)),this},set(h,S){!s&&!Ct(S)&&!nn(S)&&(S=ke(S));const M=ke(this),{has:m,get:_}=ir(M);let D=m.call(M,h);D||(h=ke(h),D=m.call(M,h));const k=_.call(M,h);return M.set(h,S),D?ft(S,k)&&jt(M,"set",h,S):jt(M,"add",h,S),this},delete(h){const S=ke(this),{has:M,get:m}=ir(S);let _=M.call(S,h);_||(h=ke(h),_=M.call(S,h)),m&&m.call(S,h);const D=S.delete(h);return _&&jt(S,"delete",h,void 0),D},clear(){const h=ke(this),S=h.size!==0,M=h.clear();return S&&jt(h,"clear",void 0,void 0),M}}),["keys","values","entries",Symbol.iterator].forEach(h=>{l[h]=ic(h,r,s)}),l}function Ci(r,s){const l=ac(r,s);return(p,h,S)=>h==="__v_isReactive"?!r:h==="__v_isReadonly"?r:h==="__v_raw"?p:Reflect.get(xe(l,h)&&h in p?l:p,h,S)}const sc={get:Ci(!1,!1)},oc={get:Ci(!1,!0)},lc={get:Ci(!0,!1)};const fs=new WeakMap,ds=new WeakMap,ps=new WeakMap,cc=new WeakMap;function uc(r){switch(r){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function fc(r){return r.__v_skip||!Object.isExtensible(r)?0:uc(Rl(r))}function Ti(r){return nn(r)?r:Mi(r,!1,tc,sc,fs)}function dc(r){return Mi(r,!1,rc,oc,ds)}function hs(r){return Mi(r,!0,nc,lc,ps)}function Mi(r,s,l,p,h){if(!We(r)||r.__v_raw&&!(s&&r.__v_isReactive))return r;const S=fc(r);if(S===0)return r;const M=h.get(r);if(M)return M;const m=new Proxy(r,S===2?p:l);return h.set(r,m),m}function In(r){return nn(r)?In(r.__v_raw):!!(r&&r.__v_isReactive)}function nn(r){return!!(r&&r.__v_isReadonly)}function Ct(r){return!!(r&&r.__v_isShallow)}function Li(r){return r?!!r.__v_raw:!1}function ke(r){const s=r&&r.__v_raw;return s?ke(s):r}function pc(r){return!xe(r,"__v_skip")&&Object.isExtensible(r)&&Ya(r,"__v_skip",!0),r}const rt=r=>We(r)?Ti(r):r,si=r=>We(r)?hs(r):r;function tt(r){return r?r.__v_isRef===!0:!1}function Xr(r){return hc(r,!1)}function hc(r,s){return tt(r)?r:new mc(r,s)}class mc{constructor(s,l){this.dep=new Sr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=l?s:ke(s),this._value=l?s:rt(s),this.__v_isShallow=l}get value(){return this.dep.track(),this._value}set value(s){const l=this._rawValue,p=this.__v_isShallow||Ct(s)||nn(s);s=p?s:ke(s),ft(s,l)&&(this._rawValue=s,this._value=p?s:rt(s),this.dep.trigger())}}function gc(r){return tt(r)?r.value:r}const yc={get:(r,s,l)=>s==="__v_raw"?r:gc(Reflect.get(r,s,l)),set:(r,s,l,p)=>{const h=r[s];return tt(h)&&!tt(l)?(h.value=l,!0):Reflect.set(r,s,l,p)}};function ms(r){return In(r)?r:new Proxy(r,yc)}class bc{constructor(s){this.__v_isRef=!0,this._value=void 0;const l=this.dep=new Sr,{get:p,set:h}=s(l.track.bind(l),l.trigger.bind(l));this._get=p,this._set=h}get value(){return this._value=this._get()}set value(s){this._set(s)}}function wc(r){return new bc(r)}class vc{constructor(s,l,p){this.fn=s,this.setter=l,this._value=void 0,this.dep=new Sr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Fn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!l,this.isSSR=p}notify(){if(this.flags|=16,!(this.flags&8)&&je!==this)return ns(this,!0),!0}get value(){const s=this.dep.track();return as(this),s&&(s.version=this.dep.version),this._value}set value(s){this.setter&&this.setter(s)}}function Ec(r,s,l=!1){let p,h;return ve(r)?p=r:(p=r.get,h=r.set),new vc(p,h,l)}const sr={},dr=new WeakMap;let en;function _c(r,s=!1,l=en){if(l){let p=dr.get(l);p||dr.set(l,p=[]),p.push(r)}}function Sc(r,s,l=Oe){const{immediate:p,deep:h,once:S,scheduler:M,augmentJob:m,call:_}=l,D=T=>h?T:Ct(T)||h===!1||h===0?$t(T,1):$t(T);let k,u,c,b,g=!1,E=!1;if(tt(r)?(u=()=>r.value,g=Ct(r)):In(r)?(u=()=>D(r),g=!0):be(r)?(E=!0,g=r.some(T=>In(T)||Ct(T)),u=()=>r.map(T=>{if(tt(T))return T.value;if(In(T))return D(T);if(ve(T))return _?_(T,2):T()})):ve(r)?s?u=_?()=>_(r,2):r:u=()=>{if(c){Ut();try{c()}finally{Vt()}}const T=en;en=k;try{return _?_(r,3,[b]):r(b)}finally{en=T}}:u=_t,s&&h){const T=u,f=h===!0?1/0:h;u=()=>$t(T(),f)}const y=Gl(),O=()=>{k.stop(),y&&y.active&&gi(y.effects,k)};if(S&&s){const T=s;s=(...f)=>{T(...f),O()}}let L=E?new Array(r.length).fill(sr):sr;const A=T=>{if(!(!(k.flags&1)||!k.dirty&&!T))if(s){const f=k.run();if(h||g||(E?f.some((R,V)=>ft(R,L[V])):ft(f,L))){c&&c();const R=en;en=k;try{const V=[f,L===sr?void 0:E&&L[0]===sr?[]:L,b];L=f,_?_(s,3,V):s(...V)}finally{en=R}}}else k.run()};return m&&m(A),k=new es(u),k.scheduler=M?()=>M(A,!1):A,b=T=>_c(T,!1,k),c=k.onStop=()=>{const T=dr.get(k);if(T){if(_)_(T,4);else for(const f of T)f();dr.delete(k)}},s?p?A(!0):L=k.run():M?M(A.bind(null,!0),!0):k.run(),O.pause=k.pause.bind(k),O.resume=k.resume.bind(k),O.stop=O,O}function $t(r,s=1/0,l){if(s<=0||!We(r)||r.__v_skip||(l=l||new Set,l.has(r)))return r;if(l.add(r),s--,tt(r))$t(r.value,s,l);else if(be(r))for(let p=0;p<r.length;p++)$t(r[p],s,l);else if(Dl(r)||Dn(r))r.forEach(p=>{$t(p,s,l)});else if(Nl(r)){for(const p in r)$t(r[p],s,l);for(const p of Object.getOwnPropertySymbols(r))Object.prototype.propertyIsEnumerable.call(r,p)&&$t(r[p],s,l)}return r}/**
* @vue/runtime-core v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Gn(r,s,l,p){try{return p?r(...p):r()}catch(h){Cr(h,s,l)}}function Ht(r,s,l,p){if(ve(r)){const h=Gn(r,s,l,p);return h&&Za(h)&&h.catch(S=>{Cr(S,s,l)}),h}if(be(r)){const h=[];for(let S=0;S<r.length;S++)h.push(Ht(r[S],s,l,p));return h}}function Cr(r,s,l,p=!0){const h=s?s.vnode:null,{errorHandler:S,throwUnhandledErrorInProduction:M}=s&&s.appContext.config||Oe;if(s){let m=s.parent;const _=s.proxy,D=`https://vuejs.org/error-reference/#runtime-${l}`;for(;m;){const k=m.ec;if(k){for(let u=0;u<k.length;u++)if(k[u](r,_,D)===!1)return}m=m.parent}if(S){Ut(),Gn(S,null,10,[r,_,D]),Vt();return}}Cc(r,l,h,p,M)}function Cc(r,s,l,p=!0,h=!1){if(h)throw r;console.error(r)}const it=[];let xt=-1;const yn=[];let zt=null,mn=0;const gs=Promise.resolve();let pr=null;function Tc(r){const s=pr||gs;return r?s.then(this?r.bind(this):r):s}function Mc(r){let s=xt+1,l=it.length;for(;s<l;){const p=s+l>>>1,h=it[p],S=Kn(h);S<r||S===r&&h.flags&2?s=p+1:l=p}return s}function Ai(r){if(!(r.flags&1)){const s=Kn(r),l=it[it.length-1];!l||!(r.flags&2)&&s>=Kn(l)?it.push(r):it.splice(Mc(s),0,r),r.flags|=1,ys()}}function ys(){pr||(pr=gs.then(vs))}function bs(r){be(r)?yn.push(...r):zt&&r.id===-1?zt.splice(mn+1,0,r):r.flags&1||(yn.push(r),r.flags|=1),ys()}function Ta(r,s,l=xt+1){for(;l<it.length;l++){const p=it[l];if(p&&p.flags&2){if(r&&p.id!==r.uid)continue;it.splice(l,1),l--,p.flags&4&&(p.flags&=-2),p(),p.flags&4||(p.flags&=-2)}}}function ws(r){if(yn.length){const s=[...new Set(yn)].sort((l,p)=>Kn(l)-Kn(p));if(yn.length=0,zt){zt.push(...s);return}for(zt=s,mn=0;mn<zt.length;mn++){const l=zt[mn];l.flags&4&&(l.flags&=-2),l.flags&8||l(),l.flags&=-2}zt=null,mn=0}}const Kn=r=>r.id==null?r.flags&2?-1:1/0:r.id;function vs(r){try{for(xt=0;xt<it.length;xt++){const s=it[xt];s&&!(s.flags&8)&&(s.flags&4&&(s.flags&=-2),Gn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2))}}finally{for(;xt<it.length;xt++){const s=it[xt];s&&(s.flags&=-2)}xt=-1,it.length=0,ws(),pr=null,(it.length||yn.length)&&vs()}}let Dt=null,Es=null;function hr(r){const s=Dt;return Dt=r,Es=r&&r.type.__scopeId||null,s}function Lc(r,s=Dt,l){if(!s||r._n)return r;const p=(...h)=>{p._d&&Ha(-1);const S=hr(s);let M;try{M=r(...h)}finally{hr(S),p._d&&Ha(1)}return M};return p._n=!0,p._c=!0,p._d=!0,p}function Yt(r,s,l,p){const h=r.dirs,S=s&&s.dirs;for(let M=0;M<h.length;M++){const m=h[M];S&&(m.oldValue=S[M].value);let _=m.dir[p];_&&(Ut(),Ht(_,l,8,[r.el,m,r,s]),Vt())}}const Ac=Symbol("_vte"),kc=r=>r.__isTeleport,xc=Symbol("_leaveCb");function ki(r,s){r.shapeFlag&6&&r.component?(r.transition=s,ki(r.component.subTree,s)):r.shapeFlag&128?(r.ssContent.transition=s.clone(r.ssContent),r.ssFallback.transition=s.clone(r.ssFallback)):r.transition=s}/*! #__NO_SIDE_EFFECTS__ */function _s(r,s){return ve(r)?Ye({name:r.name},s,{setup:r}):r}function Ss(r){r.ids=[r.ids[0]+r.ids[2]+++"-",0,0]}function Pn(r,s,l,p,h=!1){if(be(r)){r.forEach((g,E)=>Pn(g,s&&(be(s)?s[E]:s),l,p,h));return}if(jn(p)&&!h){p.shapeFlag&512&&p.type.__asyncResolved&&p.component.subTree.component&&Pn(r,s,l,p.component.subTree);return}const S=p.shapeFlag&4?ji(p.component):p.el,M=h?null:S,{i:m,r:_}=r,D=s&&s.r,k=m.refs===Oe?m.refs={}:m.refs,u=m.setupState,c=ke(u),b=u===Oe?Ja:g=>xe(c,g);if(D!=null&&D!==_){if($e(D))k[D]=null,b(D)&&(u[D]=null);else if(tt(D)){D.value=null;const g=s;g.k&&(k[g.k]=null)}}if(ve(_))Gn(_,m,12,[M,k]);else{const g=$e(_),E=tt(_);if(g||E){const y=()=>{if(r.f){const O=g?b(_)?u[_]:k[_]:_.value;if(h)be(O)&&gi(O,S);else if(be(O))O.includes(S)||O.push(S);else if(g)k[_]=[S],b(_)&&(u[_]=k[_]);else{const L=[S];_.value=L,r.k&&(k[r.k]=L)}}else g?(k[_]=M,b(_)&&(u[_]=M)):E&&(_.value=M,r.k&&(k[r.k]=M))};M?(y.id=-1,ht(y,l)):y()}}}_r().requestIdleCallback;_r().cancelIdleCallback;const jn=r=>!!r.type.__asyncLoader,Cs=r=>r.type.__isKeepAlive;function Oc(r,s){Ts(r,"a",s)}function Dc(r,s){Ts(r,"da",s)}function Ts(r,s,l=at){const p=r.__wdc||(r.__wdc=()=>{let h=l;for(;h;){if(h.isDeactivated)return;h=h.parent}return r()});if(Tr(s,p,l),l){let h=l.parent;for(;h&&h.parent;)Cs(h.parent.vnode)&&Hc(p,s,l,h),h=h.parent}}function Hc(r,s,l,p){const h=Tr(s,r,p,!0);Oi(()=>{gi(p[s],h)},l)}function Tr(r,s,l=at,p=!1){if(l){const h=l[r]||(l[r]=[]),S=s.__weh||(s.__weh=(...M)=>{Ut();const m=$n(l),_=Ht(s,l,r,M);return m(),Vt(),_});return p?h.unshift(S):h.push(S),S}}const Ft=r=>(s,l=at)=>{(!zn||r==="sp")&&Tr(r,(...p)=>s(...p),l)},Rc=Ft("bm"),xi=Ft("m"),Ms=Ft("bu"),Nc=Ft("u"),Ic=Ft("bum"),Oi=Ft("um"),Pc=Ft("sp"),jc=Ft("rtg"),Bc=Ft("rtc");function Uc(r,s=at){Tr("ec",r,s)}const Vc=Symbol.for("v-ndc"),oi=r=>r?$s(r)?ji(r):oi(r.parent):null,Bn=Ye(Object.create(null),{$:r=>r,$el:r=>r.vnode.el,$data:r=>r.data,$props:r=>r.props,$attrs:r=>r.attrs,$slots:r=>r.slots,$refs:r=>r.refs,$parent:r=>oi(r.parent),$root:r=>oi(r.root),$host:r=>r.ce,$emit:r=>r.emit,$options:r=>As(r),$forceUpdate:r=>r.f||(r.f=()=>{Ai(r.update)}),$nextTick:r=>r.n||(r.n=Tc.bind(r.proxy)),$watch:r=>fu.bind(r)}),Yr=(r,s)=>r!==Oe&&!r.__isScriptSetup&&xe(r,s),Fc={get({_:r},s){if(s==="__v_skip")return!0;const{ctx:l,setupState:p,data:h,props:S,accessCache:M,type:m,appContext:_}=r;let D;if(s[0]!=="$"){const b=M[s];if(b!==void 0)switch(b){case 1:return p[s];case 2:return h[s];case 4:return l[s];case 3:return S[s]}else{if(Yr(p,s))return M[s]=1,p[s];if(h!==Oe&&xe(h,s))return M[s]=2,h[s];if((D=r.propsOptions[0])&&xe(D,s))return M[s]=3,S[s];if(l!==Oe&&xe(l,s))return M[s]=4,l[s];li&&(M[s]=0)}}const k=Bn[s];let u,c;if(k)return s==="$attrs"&&et(r.attrs,"get",""),k(r);if((u=m.__cssModules)&&(u=u[s]))return u;if(l!==Oe&&xe(l,s))return M[s]=4,l[s];if(c=_.config.globalProperties,xe(c,s))return c[s]},set({_:r},s,l){const{data:p,setupState:h,ctx:S}=r;return Yr(h,s)?(h[s]=l,!0):p!==Oe&&xe(p,s)?(p[s]=l,!0):xe(r.props,s)||s[0]==="$"&&s.slice(1)in r?!1:(S[s]=l,!0)},has({_:{data:r,setupState:s,accessCache:l,ctx:p,appContext:h,propsOptions:S,type:M}},m){let _,D;return!!(l[m]||r!==Oe&&m[0]!=="$"&&xe(r,m)||Yr(s,m)||(_=S[0])&&xe(_,m)||xe(p,m)||xe(Bn,m)||xe(h.config.globalProperties,m)||(D=M.__cssModules)&&D[m])},defineProperty(r,s,l){return l.get!=null?r._.accessCache[s]=0:xe(l,"value")&&this.set(r,s,l.value,null),Reflect.defineProperty(r,s,l)}};function mr(r){return be(r)?r.reduce((s,l)=>(s[l]=null,s),{}):r}function Wc(r,s){return!r||!s?r||s:be(r)&&be(s)?r.concat(s):Ye({},mr(r),mr(s))}let li=!0;function Kc(r){const s=As(r),l=r.proxy,p=r.ctx;li=!1,s.beforeCreate&&Ma(s.beforeCreate,r,"bc");const{data:h,computed:S,methods:M,watch:m,provide:_,inject:D,created:k,beforeMount:u,mounted:c,beforeUpdate:b,updated:g,activated:E,deactivated:y,beforeDestroy:O,beforeUnmount:L,destroyed:A,unmounted:T,render:f,renderTracked:R,renderTriggered:V,errorCaptured:W,serverPrefetch:P,expose:$,inheritAttrs:re,components:ue,directives:ye,filters:Se}=s;if(D&&qc(D,p,null),M)for(const ge in M){const pe=M[ge];ve(pe)&&(p[ge]=pe.bind(l))}if(h){const ge=h.call(l,l);We(ge)&&(r.data=Ti(ge))}if(li=!0,S)for(const ge in S){const pe=S[ge],Re=ve(pe)?pe.bind(l,l):ve(pe.get)?pe.get.bind(l,l):_t,Ke=!ve(pe)&&ve(pe.set)?pe.set.bind(l):_t,Ne=Hu({get:Re,set:Ke});Object.defineProperty(p,ge,{enumerable:!0,configurable:!0,get:()=>Ne.value,set:I=>Ne.value=I})}if(m)for(const ge in m)Ls(m[ge],p,l,ge);if(_){const ge=ve(_)?_.call(l):_;Reflect.ownKeys(ge).forEach(pe=>{Xc(pe,ge[pe])})}k&&Ma(k,r,"c");function Ce(ge,pe){be(pe)?pe.forEach(Re=>ge(Re.bind(l))):pe&&ge(pe.bind(l))}if(Ce(Rc,u),Ce(xi,c),Ce(Ms,b),Ce(Nc,g),Ce(Oc,E),Ce(Dc,y),Ce(Uc,W),Ce(Bc,R),Ce(jc,V),Ce(Ic,L),Ce(Oi,T),Ce(Pc,P),be($))if($.length){const ge=r.exposed||(r.exposed={});$.forEach(pe=>{Object.defineProperty(ge,pe,{get:()=>l[pe],set:Re=>l[pe]=Re,enumerable:!0})})}else r.exposed||(r.exposed={});f&&r.render===_t&&(r.render=f),re!=null&&(r.inheritAttrs=re),ue&&(r.components=ue),ye&&(r.directives=ye),P&&Ss(r)}function qc(r,s,l=_t){be(r)&&(r=ci(r));for(const p in r){const h=r[p];let S;We(h)?"default"in h?S=or(h.from||p,h.default,!0):S=or(h.from||p):S=or(h),tt(S)?Object.defineProperty(s,p,{enumerable:!0,configurable:!0,get:()=>S.value,set:M=>S.value=M}):s[p]=S}}function Ma(r,s,l){Ht(be(r)?r.map(p=>p.bind(s.proxy)):r.bind(s.proxy),s,l)}function Ls(r,s,l,p){let h=p.includes(".")?Us(l,p):()=>l[p];if($e(r)){const S=s[r];ve(S)&&Un(h,S)}else if(ve(r))Un(h,r.bind(l));else if(We(r))if(be(r))r.forEach(S=>Ls(S,s,l,p));else{const S=ve(r.handler)?r.handler.bind(l):s[r.handler];ve(S)&&Un(h,S,r)}}function As(r){const s=r.type,{mixins:l,extends:p}=s,{mixins:h,optionsCache:S,config:{optionMergeStrategies:M}}=r.appContext,m=S.get(s);let _;return m?_=m:!h.length&&!l&&!p?_=s:(_={},h.length&&h.forEach(D=>gr(_,D,M,!0)),gr(_,s,M)),We(s)&&S.set(s,_),_}function gr(r,s,l,p=!1){const{mixins:h,extends:S}=s;S&&gr(r,S,l,!0),h&&h.forEach(M=>gr(r,M,l,!0));for(const M in s)if(!(p&&M==="expose")){const m=zc[M]||l&&l[M];r[M]=m?m(r[M],s[M]):s[M]}return r}const zc={data:La,props:Aa,emits:Aa,methods:On,computed:On,beforeCreate:nt,created:nt,beforeMount:nt,mounted:nt,beforeUpdate:nt,updated:nt,beforeDestroy:nt,beforeUnmount:nt,destroyed:nt,unmounted:nt,activated:nt,deactivated:nt,errorCaptured:nt,serverPrefetch:nt,components:On,directives:On,watch:$c,provide:La,inject:Gc};function La(r,s){return s?r?function(){return Ye(ve(r)?r.call(this,this):r,ve(s)?s.call(this,this):s)}:s:r}function Gc(r,s){return On(ci(r),ci(s))}function ci(r){if(be(r)){const s={};for(let l=0;l<r.length;l++)s[r[l]]=r[l];return s}return r}function nt(r,s){return r?[...new Set([].concat(r,s))]:s}function On(r,s){return r?Ye(Object.create(null),r,s):s}function Aa(r,s){return r?be(r)&&be(s)?[...new Set([...r,...s])]:Ye(Object.create(null),mr(r),mr(s??{})):s}function $c(r,s){if(!r)return s;if(!s)return r;const l=Ye(Object.create(null),r);for(const p in s)l[p]=nt(r[p],s[p]);return l}function ks(){return{app:null,config:{isNativeTag:Ja,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Jc=0;function Zc(r,s){return function(p,h=null){ve(p)||(p=Ye({},p)),h!=null&&!We(h)&&(h=null);const S=ks(),M=new WeakSet,m=[];let _=!1;const D=S.app={_uid:Jc++,_component:p,_props:h,_container:null,_context:S,_instance:null,version:Ru,get config(){return S.config},set config(k){},use(k,...u){return M.has(k)||(k&&ve(k.install)?(M.add(k),k.install(D,...u)):ve(k)&&(M.add(k),k(D,...u))),D},mixin(k){return S.mixins.includes(k)||S.mixins.push(k),D},component(k,u){return u?(S.components[k]=u,D):S.components[k]},directive(k,u){return u?(S.directives[k]=u,D):S.directives[k]},mount(k,u,c){if(!_){const b=D._ceVNode||Jt(p,h);return b.appContext=S,c===!0?c="svg":c===!1&&(c=void 0),r(b,k,c),_=!0,D._container=k,k.__vue_app__=D,ji(b.component)}},onUnmount(k){m.push(k)},unmount(){_&&(Ht(m,D._instance,16),r(null,D._container),delete D._container.__vue_app__)},provide(k,u){return S.provides[k]=u,D},runWithContext(k){const u=bn;bn=D;try{return k()}finally{bn=u}}};return D}}let bn=null;function Xc(r,s){if(at){let l=at.provides;const p=at.parent&&at.parent.provides;p===l&&(l=at.provides=Object.create(p)),l[r]=s}}function or(r,s,l=!1){const p=Pi();if(p||bn){let h=bn?bn._context.provides:p?p.parent==null||p.ce?p.vnode.appContext&&p.vnode.appContext.provides:p.parent.provides:void 0;if(h&&r in h)return h[r];if(arguments.length>1)return l&&ve(s)?s.call(p&&p.proxy):s}}const xs={},Os=()=>Object.create(xs),Ds=r=>Object.getPrototypeOf(r)===xs;function Yc(r,s,l,p=!1){const h={},S=Os();r.propsDefaults=Object.create(null),Hs(r,s,h,S);for(const M in r.propsOptions[0])M in h||(h[M]=void 0);l?r.props=p?h:dc(h):r.type.props?r.props=h:r.props=S,r.attrs=S}function Qc(r,s,l,p){const{props:h,attrs:S,vnode:{patchFlag:M}}=r,m=ke(h),[_]=r.propsOptions;let D=!1;if((p||M>0)&&!(M&16)){if(M&8){const k=r.vnode.dynamicProps;for(let u=0;u<k.length;u++){let c=k[u];if(Mr(r.emitsOptions,c))continue;const b=s[c];if(_)if(xe(S,c))b!==S[c]&&(S[c]=b,D=!0);else{const g=Bt(c);h[g]=ui(_,m,g,b,r,!1)}else b!==S[c]&&(S[c]=b,D=!0)}}}else{Hs(r,s,h,S)&&(D=!0);let k;for(const u in m)(!s||!xe(s,u)&&((k=Zt(u))===u||!xe(s,k)))&&(_?l&&(l[u]!==void 0||l[k]!==void 0)&&(h[u]=ui(_,m,u,void 0,r,!0)):delete h[u]);if(S!==m)for(const u in S)(!s||!xe(s,u))&&(delete S[u],D=!0)}D&&jt(r.attrs,"set","")}function Hs(r,s,l,p){const[h,S]=r.propsOptions;let M=!1,m;if(s)for(let _ in s){if(Hn(_))continue;const D=s[_];let k;h&&xe(h,k=Bt(_))?!S||!S.includes(k)?l[k]=D:(m||(m={}))[k]=D:Mr(r.emitsOptions,_)||(!(_ in p)||D!==p[_])&&(p[_]=D,M=!0)}if(S){const _=ke(l),D=m||Oe;for(let k=0;k<S.length;k++){const u=S[k];l[u]=ui(h,_,u,D[u],r,!xe(D,u))}}return M}function ui(r,s,l,p,h,S){const M=r[l];if(M!=null){const m=xe(M,"default");if(m&&p===void 0){const _=M.default;if(M.type!==Function&&!M.skipFactory&&ve(_)){const{propsDefaults:D}=h;if(l in D)p=D[l];else{const k=$n(h);p=D[l]=_.call(null,s),k()}}else p=_;h.ce&&h.ce._setProp(l,p)}M[0]&&(S&&!m?p=!1:M[1]&&(p===""||p===Zt(l))&&(p=!0))}return p}const eu=new WeakMap;function Rs(r,s,l=!1){const p=l?eu:s.propsCache,h=p.get(r);if(h)return h;const S=r.props,M={},m=[];let _=!1;if(!ve(r)){const k=u=>{_=!0;const[c,b]=Rs(u,s,!0);Ye(M,c),b&&m.push(...b)};!l&&s.mixins.length&&s.mixins.forEach(k),r.extends&&k(r.extends),r.mixins&&r.mixins.forEach(k)}if(!S&&!_)return We(r)&&p.set(r,gn),gn;if(be(S))for(let k=0;k<S.length;k++){const u=Bt(S[k]);ka(u)&&(M[u]=Oe)}else if(S)for(const k in S){const u=Bt(k);if(ka(u)){const c=S[k],b=M[u]=be(c)||ve(c)?{type:c}:Ye({},c),g=b.type;let E=!1,y=!0;if(be(g))for(let O=0;O<g.length;++O){const L=g[O],A=ve(L)&&L.name;if(A==="Boolean"){E=!0;break}else A==="String"&&(y=!1)}else E=ve(g)&&g.name==="Boolean";b[0]=E,b[1]=y,(E||xe(b,"default"))&&m.push(u)}}const D=[M,m];return We(r)&&p.set(r,D),D}function ka(r){return r[0]!=="$"&&!Hn(r)}const Di=r=>r==="_"||r==="_ctx"||r==="$stable",Hi=r=>be(r)?r.map(Ot):[Ot(r)],tu=(r,s,l)=>{if(s._n)return s;const p=Lc((...h)=>Hi(s(...h)),l);return p._c=!1,p},Ns=(r,s,l)=>{const p=r._ctx;for(const h in r){if(Di(h))continue;const S=r[h];if(ve(S))s[h]=tu(h,S,p);else if(S!=null){const M=Hi(S);s[h]=()=>M}}},Is=(r,s)=>{const l=Hi(s);r.slots.default=()=>l},Ps=(r,s,l)=>{for(const p in s)(l||!Di(p))&&(r[p]=s[p])},nu=(r,s,l)=>{const p=r.slots=Os();if(r.vnode.shapeFlag&32){const h=s._;h?(Ps(p,s,l),l&&Ya(p,"_",h,!0)):Ns(s,p)}else s&&Is(r,s)},ru=(r,s,l)=>{const{vnode:p,slots:h}=r;let S=!0,M=Oe;if(p.shapeFlag&32){const m=s._;m?l&&m===1?S=!1:Ps(h,s,l):(S=!s.$stable,Ns(s,h)),M=s}else s&&(Is(r,s),M={default:1});if(S)for(const m in h)!Di(m)&&M[m]==null&&delete h[m]},ht=bu;function iu(r){return au(r)}function au(r,s){const l=_r();l.__VUE__=!0;const{insert:p,remove:h,patchProp:S,createElement:M,createText:m,createComment:_,setText:D,setElementText:k,parentNode:u,nextSibling:c,setScopeId:b=_t,insertStaticContent:g}=r,E=(x,N,B,K=null,U=null,F=null,ee=void 0,Y=null,te=!!N.dynamicChildren)=>{if(x===N)return;x&&!xn(x,N)&&(K=q(x),I(x,U,F,!0),x=null),N.patchFlag===-2&&(te=!1,N.dynamicChildren=null);const{type:J,ref:he,shapeFlag:se}=N;switch(J){case Lr:y(x,N,B,K);break;case wn:O(x,N,B,K);break;case lr:x==null&&L(N,B,K,ee);break;case Et:ue(x,N,B,K,U,F,ee,Y,te);break;default:se&1?f(x,N,B,K,U,F,ee,Y,te):se&6?ye(x,N,B,K,U,F,ee,Y,te):(se&64||se&128)&&J.process(x,N,B,K,U,F,ee,Y,te,Q)}he!=null&&U?Pn(he,x&&x.ref,F,N||x,!N):he==null&&x&&x.ref!=null&&Pn(x.ref,null,F,x,!0)},y=(x,N,B,K)=>{if(x==null)p(N.el=m(N.children),B,K);else{const U=N.el=x.el;N.children!==x.children&&D(U,N.children)}},O=(x,N,B,K)=>{x==null?p(N.el=_(N.children||""),B,K):N.el=x.el},L=(x,N,B,K)=>{[x.el,x.anchor]=g(x.children,N,B,K,x.el,x.anchor)},A=({el:x,anchor:N},B,K)=>{let U;for(;x&&x!==N;)U=c(x),p(x,B,K),x=U;p(N,B,K)},T=({el:x,anchor:N})=>{let B;for(;x&&x!==N;)B=c(x),h(x),x=B;h(N)},f=(x,N,B,K,U,F,ee,Y,te)=>{N.type==="svg"?ee="svg":N.type==="math"&&(ee="mathml"),x==null?R(N,B,K,U,F,ee,Y,te):P(x,N,U,F,ee,Y,te)},R=(x,N,B,K,U,F,ee,Y)=>{let te,J;const{props:he,shapeFlag:se,transition:fe,dirs:ae}=x;if(te=x.el=M(x.type,F,he&&he.is,he),se&8?k(te,x.children):se&16&&W(x.children,te,null,K,U,Qr(x,F),ee,Y),ae&&Yt(x,null,K,"created"),V(te,x,x.scopeId,ee,K),he){for(const He in he)He!=="value"&&!Hn(He)&&S(te,He,null,he[He],F,K);"value"in he&&S(te,"value",null,he.value,F),(J=he.onVnodeBeforeMount)&&kt(J,K,x)}ae&&Yt(x,null,K,"beforeMount");const Te=su(U,fe);Te&&fe.beforeEnter(te),p(te,N,B),((J=he&&he.onVnodeMounted)||Te||ae)&&ht(()=>{J&&kt(J,K,x),Te&&fe.enter(te),ae&&Yt(x,null,K,"mounted")},U)},V=(x,N,B,K,U)=>{if(B&&b(x,B),K)for(let F=0;F<K.length;F++)b(x,K[F]);if(U){let F=U.subTree;if(N===F||Ws(F.type)&&(F.ssContent===N||F.ssFallback===N)){const ee=U.vnode;V(x,ee,ee.scopeId,ee.slotScopeIds,U.parent)}}},W=(x,N,B,K,U,F,ee,Y,te=0)=>{for(let J=te;J<x.length;J++){const he=x[J]=Y?Gt(x[J]):Ot(x[J]);E(null,he,N,B,K,U,F,ee,Y)}},P=(x,N,B,K,U,F,ee)=>{const Y=N.el=x.el;let{patchFlag:te,dynamicChildren:J,dirs:he}=N;te|=x.patchFlag&16;const se=x.props||Oe,fe=N.props||Oe;let ae;if(B&&Qt(B,!1),(ae=fe.onVnodeBeforeUpdate)&&kt(ae,B,N,x),he&&Yt(N,x,B,"beforeUpdate"),B&&Qt(B,!0),(se.innerHTML&&fe.innerHTML==null||se.textContent&&fe.textContent==null)&&k(Y,""),J?$(x.dynamicChildren,J,Y,B,K,Qr(N,U),F):ee||pe(x,N,Y,null,B,K,Qr(N,U),F,!1),te>0){if(te&16)re(Y,se,fe,B,U);else if(te&2&&se.class!==fe.class&&S(Y,"class",null,fe.class,U),te&4&&S(Y,"style",se.style,fe.style,U),te&8){const Te=N.dynamicProps;for(let He=0;He<Te.length;He++){const Le=Te[He],Je=se[Le],Qe=fe[Le];(Qe!==Je||Le==="value")&&S(Y,Le,Je,Qe,U,B)}}te&1&&x.children!==N.children&&k(Y,N.children)}else!ee&&J==null&&re(Y,se,fe,B,U);((ae=fe.onVnodeUpdated)||he)&&ht(()=>{ae&&kt(ae,B,N,x),he&&Yt(N,x,B,"updated")},K)},$=(x,N,B,K,U,F,ee)=>{for(let Y=0;Y<N.length;Y++){const te=x[Y],J=N[Y],he=te.el&&(te.type===Et||!xn(te,J)||te.shapeFlag&198)?u(te.el):B;E(te,J,he,null,K,U,F,ee,!0)}},re=(x,N,B,K,U)=>{if(N!==B){if(N!==Oe)for(const F in N)!Hn(F)&&!(F in B)&&S(x,F,N[F],null,U,K);for(const F in B){if(Hn(F))continue;const ee=B[F],Y=N[F];ee!==Y&&F!=="value"&&S(x,F,Y,ee,U,K)}"value"in B&&S(x,"value",N.value,B.value,U)}},ue=(x,N,B,K,U,F,ee,Y,te)=>{const J=N.el=x?x.el:m(""),he=N.anchor=x?x.anchor:m("");let{patchFlag:se,dynamicChildren:fe,slotScopeIds:ae}=N;ae&&(Y=Y?Y.concat(ae):ae),x==null?(p(J,B,K),p(he,B,K),W(N.children||[],B,he,U,F,ee,Y,te)):se>0&&se&64&&fe&&x.dynamicChildren?($(x.dynamicChildren,fe,B,U,F,ee,Y),(N.key!=null||U&&N===U.subTree)&&js(x,N,!0)):pe(x,N,B,he,U,F,ee,Y,te)},ye=(x,N,B,K,U,F,ee,Y,te)=>{N.slotScopeIds=Y,x==null?N.shapeFlag&512?U.ctx.activate(N,B,K,ee,te):Se(N,B,K,U,F,ee,te):Ae(x,N,te)},Se=(x,N,B,K,U,F,ee)=>{const Y=x.component=Lu(x,K,U);if(Cs(x)&&(Y.ctx.renderer=Q),Au(Y,!1,ee),Y.asyncDep){if(U&&U.registerDep(Y,Ce,ee),!x.el){const te=Y.subTree=Jt(wn);O(null,te,N,B),x.placeholder=te.el}}else Ce(Y,x,N,B,U,F,ee)},Ae=(x,N,B)=>{const K=N.component=x.component;if(gu(x,N,B))if(K.asyncDep&&!K.asyncResolved){ge(K,N,B);return}else K.next=N,K.update();else N.el=x.el,K.vnode=N},Ce=(x,N,B,K,U,F,ee)=>{const Y=()=>{if(x.isMounted){let{next:se,bu:fe,u:ae,parent:Te,vnode:He}=x;{const ot=Bs(x);if(ot){se&&(se.el=He.el,ge(x,se,ee)),ot.asyncDep.then(()=>{x.isUnmounted||Y()});return}}let Le=se,Je;Qt(x,!1),se?(se.el=He.el,ge(x,se,ee)):se=He,fe&&Gr(fe),(Je=se.props&&se.props.onVnodeBeforeUpdate)&&kt(Je,Te,se,He),Qt(x,!0);const Qe=Oa(x),gt=x.subTree;x.subTree=Qe,E(gt,Qe,u(gt.el),q(gt),x,U,F),se.el=Qe.el,Le===null&&yu(x,Qe.el),ae&&ht(ae,U),(Je=se.props&&se.props.onVnodeUpdated)&&ht(()=>kt(Je,Te,se,He),U)}else{let se;const{el:fe,props:ae}=N,{bm:Te,m:He,parent:Le,root:Je,type:Qe}=x,gt=jn(N);Qt(x,!1),Te&&Gr(Te),!gt&&(se=ae&&ae.onVnodeBeforeMount)&&kt(se,Le,N),Qt(x,!0);{Je.ce&&Je.ce._def.shadowRoot!==!1&&Je.ce._injectChildStyle(Qe);const ot=x.subTree=Oa(x);E(null,ot,B,K,x,U,F),N.el=ot.el}if(He&&ht(He,U),!gt&&(se=ae&&ae.onVnodeMounted)){const ot=N;ht(()=>kt(se,Le,ot),U)}(N.shapeFlag&256||Le&&jn(Le.vnode)&&Le.vnode.shapeFlag&256)&&x.a&&ht(x.a,U),x.isMounted=!0,N=B=K=null}};x.scope.on();const te=x.effect=new es(Y);x.scope.off();const J=x.update=te.run.bind(te),he=x.job=te.runIfDirty.bind(te);he.i=x,he.id=x.uid,te.scheduler=()=>Ai(he),Qt(x,!0),J()},ge=(x,N,B)=>{N.component=x;const K=x.vnode.props;x.vnode=N,x.next=null,Qc(x,N.props,K,B),ru(x,N.children,B),Ut(),Ta(x),Vt()},pe=(x,N,B,K,U,F,ee,Y,te=!1)=>{const J=x&&x.children,he=x?x.shapeFlag:0,se=N.children,{patchFlag:fe,shapeFlag:ae}=N;if(fe>0){if(fe&128){Ke(J,se,B,K,U,F,ee,Y,te);return}else if(fe&256){Re(J,se,B,K,U,F,ee,Y,te);return}}ae&8?(he&16&&st(J,U,F),se!==J&&k(B,se)):he&16?ae&16?Ke(J,se,B,K,U,F,ee,Y,te):st(J,U,F,!0):(he&8&&k(B,""),ae&16&&W(se,B,K,U,F,ee,Y,te))},Re=(x,N,B,K,U,F,ee,Y,te)=>{x=x||gn,N=N||gn;const J=x.length,he=N.length,se=Math.min(J,he);let fe;for(fe=0;fe<se;fe++){const ae=N[fe]=te?Gt(N[fe]):Ot(N[fe]);E(x[fe],ae,B,null,U,F,ee,Y,te)}J>he?st(x,U,F,!0,!1,se):W(N,B,K,U,F,ee,Y,te,se)},Ke=(x,N,B,K,U,F,ee,Y,te)=>{let J=0;const he=N.length;let se=x.length-1,fe=he-1;for(;J<=se&&J<=fe;){const ae=x[J],Te=N[J]=te?Gt(N[J]):Ot(N[J]);if(xn(ae,Te))E(ae,Te,B,null,U,F,ee,Y,te);else break;J++}for(;J<=se&&J<=fe;){const ae=x[se],Te=N[fe]=te?Gt(N[fe]):Ot(N[fe]);if(xn(ae,Te))E(ae,Te,B,null,U,F,ee,Y,te);else break;se--,fe--}if(J>se){if(J<=fe){const ae=fe+1,Te=ae<he?N[ae].el:K;for(;J<=fe;)E(null,N[J]=te?Gt(N[J]):Ot(N[J]),B,Te,U,F,ee,Y,te),J++}}else if(J>fe)for(;J<=se;)I(x[J],U,F,!0),J++;else{const ae=J,Te=J,He=new Map;for(J=Te;J<=fe;J++){const qe=N[J]=te?Gt(N[J]):Ot(N[J]);qe.key!=null&&He.set(qe.key,J)}let Le,Je=0;const Qe=fe-Te+1;let gt=!1,ot=0;const Tt=new Array(Qe);for(J=0;J<Qe;J++)Tt[J]=0;for(J=ae;J<=se;J++){const qe=x[J];if(Je>=Qe){I(qe,U,F,!0);continue}let yt;if(qe.key!=null)yt=He.get(qe.key);else for(Le=Te;Le<=fe;Le++)if(Tt[Le-Te]===0&&xn(qe,N[Le])){yt=Le;break}yt===void 0?I(qe,U,F,!0):(Tt[yt-Te]=J+1,yt>=ot?ot=yt:gt=!0,E(qe,N[yt],B,null,U,F,ee,Y,te),Je++)}const sn=gt?ou(Tt):gn;for(Le=sn.length-1,J=Qe-1;J>=0;J--){const qe=Te+J,yt=N[qe],Ze=N[qe+1],Mt=qe+1<he?Ze.el||Ze.placeholder:K;Tt[J]===0?E(null,yt,B,Mt,U,F,ee,Y,te):gt&&(Le<0||J!==sn[Le]?Ne(yt,B,Mt,2):Le--)}}},Ne=(x,N,B,K,U=null)=>{const{el:F,type:ee,transition:Y,children:te,shapeFlag:J}=x;if(J&6){Ne(x.component.subTree,N,B,K);return}if(J&128){x.suspense.move(N,B,K);return}if(J&64){ee.move(x,N,B,Q);return}if(ee===Et){p(F,N,B);for(let se=0;se<te.length;se++)Ne(te[se],N,B,K);p(x.anchor,N,B);return}if(ee===lr){A(x,N,B);return}if(K!==2&&J&1&&Y)if(K===0)Y.beforeEnter(F),p(F,N,B),ht(()=>Y.enter(F),U);else{const{leave:se,delayLeave:fe,afterLeave:ae}=Y,Te=()=>{x.ctx.isUnmounted?h(F):p(F,N,B)},He=()=>{F._isLeaving&&F[xc](!0),se(F,()=>{Te(),ae&&ae()})};fe?fe(F,Te,He):He()}else p(F,N,B)},I=(x,N,B,K=!1,U=!1)=>{const{type:F,props:ee,ref:Y,children:te,dynamicChildren:J,shapeFlag:he,patchFlag:se,dirs:fe,cacheIndex:ae}=x;if(se===-2&&(U=!1),Y!=null&&(Ut(),Pn(Y,null,B,x,!0),Vt()),ae!=null&&(N.renderCache[ae]=void 0),he&256){N.ctx.deactivate(x);return}const Te=he&1&&fe,He=!jn(x);let Le;if(He&&(Le=ee&&ee.onVnodeBeforeUnmount)&&kt(Le,N,x),he&6)an(x.component,B,K);else{if(he&128){x.suspense.unmount(B,K);return}Te&&Yt(x,null,N,"beforeUnmount"),he&64?x.type.remove(x,N,B,Q,K):J&&!J.hasOnce&&(F!==Et||se>0&&se&64)?st(J,N,B,!1,!0):(F===Et&&se&384||!U&&he&16)&&st(te,N,B),K&&dt(x)}(He&&(Le=ee&&ee.onVnodeUnmounted)||Te)&&ht(()=>{Le&&kt(Le,N,x),Te&&Yt(x,null,N,"unmounted")},B)},dt=x=>{const{type:N,el:B,anchor:K,transition:U}=x;if(N===Et){rn(B,K);return}if(N===lr){T(x);return}const F=()=>{h(B),U&&!U.persisted&&U.afterLeave&&U.afterLeave()};if(x.shapeFlag&1&&U&&!U.persisted){const{leave:ee,delayLeave:Y}=U,te=()=>ee(B,F);Y?Y(x.el,F,te):te()}else F()},rn=(x,N)=>{let B;for(;x!==N;)B=c(x),h(x),x=B;h(N)},an=(x,N,B)=>{const{bum:K,scope:U,job:F,subTree:ee,um:Y,m:te,a:J}=x;xa(te),xa(J),K&&Gr(K),U.stop(),F&&(F.flags|=8,I(ee,x,N,B)),Y&&ht(Y,N),ht(()=>{x.isUnmounted=!0},N)},st=(x,N,B,K=!1,U=!1,F=0)=>{for(let ee=F;ee<x.length;ee++)I(x[ee],N,B,K,U)},q=x=>{if(x.shapeFlag&6)return q(x.component.subTree);if(x.shapeFlag&128)return x.suspense.next();const N=c(x.anchor||x.el),B=N&&N[Ac];return B?c(B):N};let Me=!1;const ie=(x,N,B)=>{x==null?N._vnode&&I(N._vnode,null,null,!0):E(N._vnode||null,x,N,null,null,null,B),N._vnode=x,Me||(Me=!0,Ta(),ws(),Me=!1)},Q={p:E,um:I,m:Ne,r:dt,mt:Se,mc:W,pc:pe,pbc:$,n:q,o:r};return{render:ie,hydrate:void 0,createApp:Zc(ie)}}function Qr({type:r,props:s},l){return l==="svg"&&r==="foreignObject"||l==="mathml"&&r==="annotation-xml"&&s&&s.encoding&&s.encoding.includes("html")?void 0:l}function Qt({effect:r,job:s},l){l?(r.flags|=32,s.flags|=4):(r.flags&=-33,s.flags&=-5)}function su(r,s){return(!r||r&&!r.pendingBranch)&&s&&!s.persisted}function js(r,s,l=!1){const p=r.children,h=s.children;if(be(p)&&be(h))for(let S=0;S<p.length;S++){const M=p[S];let m=h[S];m.shapeFlag&1&&!m.dynamicChildren&&((m.patchFlag<=0||m.patchFlag===32)&&(m=h[S]=Gt(h[S]),m.el=M.el),!l&&m.patchFlag!==-2&&js(M,m)),m.type===Lr&&m.patchFlag!==-1&&(m.el=M.el),m.type===wn&&!m.el&&(m.el=M.el)}}function ou(r){const s=r.slice(),l=[0];let p,h,S,M,m;const _=r.length;for(p=0;p<_;p++){const D=r[p];if(D!==0){if(h=l[l.length-1],r[h]<D){s[p]=h,l.push(p);continue}for(S=0,M=l.length-1;S<M;)m=S+M>>1,r[l[m]]<D?S=m+1:M=m;D<r[l[S]]&&(S>0&&(s[p]=l[S-1]),l[S]=p)}}for(S=l.length,M=l[S-1];S-- >0;)l[S]=M,M=s[M];return l}function Bs(r){const s=r.subTree.component;if(s)return s.asyncDep&&!s.asyncResolved?s:Bs(s)}function xa(r){if(r)for(let s=0;s<r.length;s++)r[s].flags|=8}const lu=Symbol.for("v-scx"),cu=()=>or(lu);function uu(r,s){return Ri(r,null,{flush:"sync"})}function Un(r,s,l){return Ri(r,s,l)}function Ri(r,s,l=Oe){const{immediate:p,deep:h,flush:S,once:M}=l,m=Ye({},l),_=s&&p||!s&&S!=="post";let D;if(zn){if(S==="sync"){const b=cu();D=b.__watcherHandles||(b.__watcherHandles=[])}else if(!_){const b=()=>{};return b.stop=_t,b.resume=_t,b.pause=_t,b}}const k=at;m.call=(b,g,E)=>Ht(b,k,g,E);let u=!1;S==="post"?m.scheduler=b=>{ht(b,k&&k.suspense)}:S!=="sync"&&(u=!0,m.scheduler=(b,g)=>{g?b():Ai(b)}),m.augmentJob=b=>{s&&(b.flags|=4),u&&(b.flags|=2,k&&(b.id=k.uid,b.i=k))};const c=Sc(r,s,m);return zn&&(D?D.push(c):_&&c()),c}function fu(r,s,l){const p=this.proxy,h=$e(r)?r.includes(".")?Us(p,r):()=>p[r]:r.bind(p,p);let S;ve(s)?S=s:(S=s.handler,l=s);const M=$n(this),m=Ri(h,S.bind(p),l);return M(),m}function Us(r,s){const l=s.split(".");return()=>{let p=r;for(let h=0;h<l.length&&p;h++)p=p[l[h]];return p}}function du(r,s,l=Oe){const p=Pi(),h=Bt(s),S=Zt(s),M=Vs(r,h),m=wc((_,D)=>{let k,u=Oe,c;return uu(()=>{const b=r[h];ft(k,b)&&(k=b,D())}),{get(){return _(),l.get?l.get(k):k},set(b){const g=l.set?l.set(b):b;if(!ft(g,k)&&!(u!==Oe&&ft(b,u)))return;const E=p.vnode.props;E&&(s in E||h in E||S in E)&&(`onUpdate:${s}`in E||`onUpdate:${h}`in E||`onUpdate:${S}`in E)||(k=b,D()),p.emit(`update:${s}`,g),ft(b,g)&&ft(b,u)&&!ft(g,c)&&D(),u=b,c=g}}});return m[Symbol.iterator]=()=>{let _=0;return{next(){return _<2?{value:_++?M||Oe:m,done:!1}:{done:!0}}}},m}const Vs=(r,s)=>s==="modelValue"||s==="model-value"?r.modelModifiers:r[`${s}Modifiers`]||r[`${Bt(s)}Modifiers`]||r[`${Zt(s)}Modifiers`];function pu(r,s,...l){if(r.isUnmounted)return;const p=r.vnode.props||Oe;let h=l;const S=s.startsWith("update:"),M=S&&Vs(p,s.slice(7));M&&(M.trim&&(h=l.map(k=>$e(k)?k.trim():k)),M.number&&(h=l.map(jl)));let m,_=p[m=zr(s)]||p[m=zr(Bt(s))];!_&&S&&(_=p[m=zr(Zt(s))]),_&&Ht(_,r,6,h);const D=p[m+"Once"];if(D){if(!r.emitted)r.emitted={};else if(r.emitted[m])return;r.emitted[m]=!0,Ht(D,r,6,h)}}function Fs(r,s,l=!1){const p=s.emitsCache,h=p.get(r);if(h!==void 0)return h;const S=r.emits;let M={},m=!1;if(!ve(r)){const _=D=>{const k=Fs(D,s,!0);k&&(m=!0,Ye(M,k))};!l&&s.mixins.length&&s.mixins.forEach(_),r.extends&&_(r.extends),r.mixins&&r.mixins.forEach(_)}return!S&&!m?(We(r)&&p.set(r,null),null):(be(S)?S.forEach(_=>M[_]=null):Ye(M,S),We(r)&&p.set(r,M),M)}function Mr(r,s){return!r||!wr(s)?!1:(s=s.slice(2).replace(/Once$/,""),xe(r,s[0].toLowerCase()+s.slice(1))||xe(r,Zt(s))||xe(r,s))}function Oa(r){const{type:s,vnode:l,proxy:p,withProxy:h,propsOptions:[S],slots:M,attrs:m,emit:_,render:D,renderCache:k,props:u,data:c,setupState:b,ctx:g,inheritAttrs:E}=r,y=hr(r);let O,L;try{if(l.shapeFlag&4){const T=h||p,f=T;O=Ot(D.call(f,T,k,u,b,c,g)),L=m}else{const T=s;O=Ot(T.length>1?T(u,{attrs:m,slots:M,emit:_}):T(u,null)),L=s.props?m:hu(m)}}catch(T){Vn.length=0,Cr(T,r,1),O=Jt(wn)}let A=O;if(L&&E!==!1){const T=Object.keys(L),{shapeFlag:f}=A;T.length&&f&7&&(S&&T.some(mi)&&(L=mu(L,S)),A=vn(A,L,!1,!0))}return l.dirs&&(A=vn(A,null,!1,!0),A.dirs=A.dirs?A.dirs.concat(l.dirs):l.dirs),l.transition&&ki(A,l.transition),O=A,hr(y),O}const hu=r=>{let s;for(const l in r)(l==="class"||l==="style"||wr(l))&&((s||(s={}))[l]=r[l]);return s},mu=(r,s)=>{const l={};for(const p in r)(!mi(p)||!(p.slice(9)in s))&&(l[p]=r[p]);return l};function gu(r,s,l){const{props:p,children:h,component:S}=r,{props:M,children:m,patchFlag:_}=s,D=S.emitsOptions;if(s.dirs||s.transition)return!0;if(l&&_>=0){if(_&1024)return!0;if(_&16)return p?Da(p,M,D):!!M;if(_&8){const k=s.dynamicProps;for(let u=0;u<k.length;u++){const c=k[u];if(M[c]!==p[c]&&!Mr(D,c))return!0}}}else return(h||m)&&(!m||!m.$stable)?!0:p===M?!1:p?M?Da(p,M,D):!0:!!M;return!1}function Da(r,s,l){const p=Object.keys(s);if(p.length!==Object.keys(r).length)return!0;for(let h=0;h<p.length;h++){const S=p[h];if(s[S]!==r[S]&&!Mr(l,S))return!0}return!1}function yu({vnode:r,parent:s},l){for(;s;){const p=s.subTree;if(p.suspense&&p.suspense.activeBranch===r&&(p.el=r.el),p===r)(r=s.vnode).el=l,s=s.parent;else break}}const Ws=r=>r.__isSuspense;function bu(r,s){s&&s.pendingBranch?be(r)?s.effects.push(...r):s.effects.push(r):bs(r)}const Et=Symbol.for("v-fgt"),Lr=Symbol.for("v-txt"),wn=Symbol.for("v-cmt"),lr=Symbol.for("v-stc"),Vn=[];let mt=null;function Ks(r=!1){Vn.push(mt=r?null:[])}function wu(){Vn.pop(),mt=Vn[Vn.length-1]||null}let qn=1;function Ha(r,s=!1){qn+=r,r<0&&mt&&s&&(mt.hasOnce=!0)}function vu(r){return r.dynamicChildren=qn>0?mt||gn:null,wu(),qn>0&&mt&&mt.push(r),r}function qs(r,s,l,p,h,S){return vu(Ni(r,s,l,p,h,S,!0))}function zs(r){return r?r.__v_isVNode===!0:!1}function xn(r,s){return r.type===s.type&&r.key===s.key}const Gs=({key:r})=>r??null,cr=({ref:r,ref_key:s,ref_for:l})=>(typeof r=="number"&&(r=""+r),r!=null?$e(r)||tt(r)||ve(r)?{i:Dt,r,k:s,f:!!l}:r:null);function Ni(r,s=null,l=null,p=0,h=null,S=r===Et?0:1,M=!1,m=!1){const _={__v_isVNode:!0,__v_skip:!0,type:r,props:s,key:s&&Gs(s),ref:s&&cr(s),scopeId:Es,slotScopeIds:null,children:l,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:S,patchFlag:p,dynamicProps:h,dynamicChildren:null,appContext:null,ctx:Dt};return m?(Ii(_,l),S&128&&r.normalize(_)):l&&(_.shapeFlag|=$e(l)?8:16),qn>0&&!M&&mt&&(_.patchFlag>0||S&6)&&_.patchFlag!==32&&mt.push(_),_}const Jt=Eu;function Eu(r,s=null,l=null,p=0,h=null,S=!1){if((!r||r===Vc)&&(r=wn),zs(r)){const m=vn(r,s,!0);return l&&Ii(m,l),qn>0&&!S&&mt&&(m.shapeFlag&6?mt[mt.indexOf(r)]=m:mt.push(m)),m.patchFlag=-2,m}if(Du(r)&&(r=r.__vccOpts),s){s=_u(s);let{class:m,style:_}=s;m&&!$e(m)&&(s.class=wi(m)),We(_)&&(Li(_)&&!be(_)&&(_=Ye({},_)),s.style=bi(_))}const M=$e(r)?1:Ws(r)?128:kc(r)?64:We(r)?4:ve(r)?2:0;return Ni(r,s,l,p,h,M,S,!0)}function _u(r){return r?Li(r)||Ds(r)?Ye({},r):r:null}function vn(r,s,l=!1,p=!1){const{props:h,ref:S,patchFlag:M,children:m,transition:_}=r,D=s?Cu(h||{},s):h,k={__v_isVNode:!0,__v_skip:!0,type:r.type,props:D,key:D&&Gs(D),ref:s&&s.ref?l&&S?be(S)?S.concat(cr(s)):[S,cr(s)]:cr(s):S,scopeId:r.scopeId,slotScopeIds:r.slotScopeIds,children:m,target:r.target,targetStart:r.targetStart,targetAnchor:r.targetAnchor,staticCount:r.staticCount,shapeFlag:r.shapeFlag,patchFlag:s&&r.type!==Et?M===-1?16:M|16:M,dynamicProps:r.dynamicProps,dynamicChildren:r.dynamicChildren,appContext:r.appContext,dirs:r.dirs,transition:_,component:r.component,suspense:r.suspense,ssContent:r.ssContent&&vn(r.ssContent),ssFallback:r.ssFallback&&vn(r.ssFallback),placeholder:r.placeholder,el:r.el,anchor:r.anchor,ctx:r.ctx,ce:r.ce};return _&&p&&ki(k,_.clone(k)),k}function Su(r=" ",s=0){return Jt(Lr,null,r,s)}function Ot(r){return r==null||typeof r=="boolean"?Jt(wn):be(r)?Jt(Et,null,r.slice()):zs(r)?Gt(r):Jt(Lr,null,String(r))}function Gt(r){return r.el===null&&r.patchFlag!==-1||r.memo?r:vn(r)}function Ii(r,s){let l=0;const{shapeFlag:p}=r;if(s==null)s=null;else if(be(s))l=16;else if(typeof s=="object")if(p&65){const h=s.default;h&&(h._c&&(h._d=!1),Ii(r,h()),h._c&&(h._d=!0));return}else{l=32;const h=s._;!h&&!Ds(s)?s._ctx=Dt:h===3&&Dt&&(Dt.slots._===1?s._=1:(s._=2,r.patchFlag|=1024))}else ve(s)?(s={default:s,_ctx:Dt},l=32):(s=String(s),p&64?(l=16,s=[Su(s)]):l=8);r.children=s,r.shapeFlag|=l}function Cu(...r){const s={};for(let l=0;l<r.length;l++){const p=r[l];for(const h in p)if(h==="class")s.class!==p.class&&(s.class=wi([s.class,p.class]));else if(h==="style")s.style=bi([s.style,p.style]);else if(wr(h)){const S=s[h],M=p[h];M&&S!==M&&!(be(S)&&S.includes(M))&&(s[h]=S?[].concat(S,M):M)}else h!==""&&(s[h]=p[h])}return s}function kt(r,s,l,p=null){Ht(r,s,7,[l,p])}const Tu=ks();let Mu=0;function Lu(r,s,l){const p=r.type,h=(s?s.appContext:r.appContext)||Tu,S={uid:Mu++,vnode:r,type:p,parent:s,appContext:h,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new zl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:s?s.provides:Object.create(h.provides),ids:s?s.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Rs(p,h),emitsOptions:Fs(p,h),emit:null,emitted:null,propsDefaults:Oe,inheritAttrs:p.inheritAttrs,ctx:Oe,data:Oe,props:Oe,attrs:Oe,slots:Oe,refs:Oe,setupState:Oe,setupContext:null,suspense:l,suspenseId:l?l.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return S.ctx={_:S},S.root=s?s.root:S,S.emit=pu.bind(null,S),r.ce&&r.ce(S),S}let at=null;const Pi=()=>at||Dt;let yr,fi;{const r=_r(),s=(l,p)=>{let h;return(h=r[l])||(h=r[l]=[]),h.push(p),S=>{h.length>1?h.forEach(M=>M(S)):h[0](S)}};yr=s("__VUE_INSTANCE_SETTERS__",l=>at=l),fi=s("__VUE_SSR_SETTERS__",l=>zn=l)}const $n=r=>{const s=at;return yr(r),r.scope.on(),()=>{r.scope.off(),yr(s)}},Ra=()=>{at&&at.scope.off(),yr(null)};function $s(r){return r.vnode.shapeFlag&4}let zn=!1;function Au(r,s=!1,l=!1){s&&fi(s);const{props:p,children:h}=r.vnode,S=$s(r);Yc(r,p,S,s),nu(r,h,l||s);const M=S?ku(r,s):void 0;return s&&fi(!1),M}function ku(r,s){const l=r.type;r.accessCache=Object.create(null),r.proxy=new Proxy(r.ctx,Fc);const{setup:p}=l;if(p){Ut();const h=r.setupContext=p.length>1?Ou(r):null,S=$n(r),M=Gn(p,r,0,[r.props,h]),m=Za(M);if(Vt(),S(),(m||r.sp)&&!jn(r)&&Ss(r),m){if(M.then(Ra,Ra),s)return M.then(_=>{Na(r,_)}).catch(_=>{Cr(_,r,0)});r.asyncDep=M}else Na(r,M)}else Js(r)}function Na(r,s,l){ve(s)?r.type.__ssrInlineRender?r.ssrRender=s:r.render=s:We(s)&&(r.setupState=ms(s)),Js(r)}function Js(r,s,l){const p=r.type;r.render||(r.render=p.render||_t);{const h=$n(r);Ut();try{Kc(r)}finally{Vt(),h()}}}const xu={get(r,s){return et(r,"get",""),r[s]}};function Ou(r){const s=l=>{r.exposed=l||{}};return{attrs:new Proxy(r.attrs,xu),slots:r.slots,emit:r.emit,expose:s}}function ji(r){return r.exposed?r.exposeProxy||(r.exposeProxy=new Proxy(ms(pc(r.exposed)),{get(s,l){if(l in s)return s[l];if(l in Bn)return Bn[l](r)},has(s,l){return l in s||l in Bn}})):r.proxy}function Du(r){return ve(r)&&"__vccOpts"in r}const Hu=(r,s)=>Ec(r,s,zn),Ru="3.5.19";/**
* @vue/runtime-dom v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let di;const Ia=typeof window<"u"&&window.trustedTypes;if(Ia)try{di=Ia.createPolicy("vue",{createHTML:r=>r})}catch{}const Zs=di?r=>di.createHTML(r):r=>r,Nu="http://www.w3.org/2000/svg",Iu="http://www.w3.org/1998/Math/MathML",Pt=typeof document<"u"?document:null,Pa=Pt&&Pt.createElement("template"),Pu={insert:(r,s,l)=>{s.insertBefore(r,l||null)},remove:r=>{const s=r.parentNode;s&&s.removeChild(r)},createElement:(r,s,l,p)=>{const h=s==="svg"?Pt.createElementNS(Nu,r):s==="mathml"?Pt.createElementNS(Iu,r):l?Pt.createElement(r,{is:l}):Pt.createElement(r);return r==="select"&&p&&p.multiple!=null&&h.setAttribute("multiple",p.multiple),h},createText:r=>Pt.createTextNode(r),createComment:r=>Pt.createComment(r),setText:(r,s)=>{r.nodeValue=s},setElementText:(r,s)=>{r.textContent=s},parentNode:r=>r.parentNode,nextSibling:r=>r.nextSibling,querySelector:r=>Pt.querySelector(r),setScopeId(r,s){r.setAttribute(s,"")},insertStaticContent(r,s,l,p,h,S){const M=l?l.previousSibling:s.lastChild;if(h&&(h===S||h.nextSibling))for(;s.insertBefore(h.cloneNode(!0),l),!(h===S||!(h=h.nextSibling)););else{Pa.innerHTML=Zs(p==="svg"?`<svg>${r}</svg>`:p==="mathml"?`<math>${r}</math>`:r);const m=Pa.content;if(p==="svg"||p==="mathml"){const _=m.firstChild;for(;_.firstChild;)m.appendChild(_.firstChild);m.removeChild(_)}s.insertBefore(m,l)}return[M?M.nextSibling:s.firstChild,l?l.previousSibling:s.lastChild]}},ju=Symbol("_vtc");function Bu(r,s,l){const p=r[ju];p&&(s=(s?[s,...p]:[...p]).join(" ")),s==null?r.removeAttribute("class"):l?r.setAttribute("class",s):r.className=s}const ja=Symbol("_vod"),Uu=Symbol("_vsh"),Xs=Symbol("");function Vu(r){const s=Pi();if(!s)return;const l=s.ut=(h=r(s.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${s.uid}"]`)).forEach(S=>br(S,h))},p=()=>{const h=r(s.proxy);s.ce?br(s.ce,h):pi(s.subTree,h),l(h)};Ms(()=>{bs(p)}),xi(()=>{Un(p,_t,{flush:"post"});const h=new MutationObserver(p);h.observe(s.subTree.el.parentNode,{childList:!0}),Oi(()=>h.disconnect())})}function pi(r,s){if(r.shapeFlag&128){const l=r.suspense;r=l.activeBranch,l.pendingBranch&&!l.isHydrating&&l.effects.push(()=>{pi(l.activeBranch,s)})}for(;r.component;)r=r.component.subTree;if(r.shapeFlag&1&&r.el)br(r.el,s);else if(r.type===Et)r.children.forEach(l=>pi(l,s));else if(r.type===lr){let{el:l,anchor:p}=r;for(;l&&(br(l,s),l!==p);)l=l.nextSibling}}function br(r,s){if(r.nodeType===1){const l=r.style;let p="";for(const h in s){const S=ql(s[h]);l.setProperty(`--${h}`,S),p+=`--${h}: ${S};`}l[Xs]=p}}const Fu=/(^|;)\s*display\s*:/;function Wu(r,s,l){const p=r.style,h=$e(l);let S=!1;if(l&&!h){if(s)if($e(s))for(const M of s.split(";")){const m=M.slice(0,M.indexOf(":")).trim();l[m]==null&&ur(p,m,"")}else for(const M in s)l[M]==null&&ur(p,M,"");for(const M in l)M==="display"&&(S=!0),ur(p,M,l[M])}else if(h){if(s!==l){const M=p[Xs];M&&(l+=";"+M),p.cssText=l,S=Fu.test(l)}}else s&&r.removeAttribute("style");ja in r&&(r[ja]=S?p.display:"",r[Uu]&&(p.display="none"))}const Ba=/\s*!important$/;function ur(r,s,l){if(be(l))l.forEach(p=>ur(r,s,p));else if(l==null&&(l=""),s.startsWith("--"))r.setProperty(s,l);else{const p=Ku(r,s);Ba.test(l)?r.setProperty(Zt(p),l.replace(Ba,""),"important"):r[p]=l}}const Ua=["Webkit","Moz","ms"],ei={};function Ku(r,s){const l=ei[s];if(l)return l;let p=Bt(s);if(p!=="filter"&&p in r)return ei[s]=p;p=Xa(p);for(let h=0;h<Ua.length;h++){const S=Ua[h]+p;if(S in r)return ei[s]=S}return s}const Va="http://www.w3.org/1999/xlink";function Fa(r,s,l,p,h,S=Kl(s)){p&&s.startsWith("xlink:")?l==null?r.removeAttributeNS(Va,s.slice(6,s.length)):r.setAttributeNS(Va,s,l):l==null||S&&!Qa(l)?r.removeAttribute(s):r.setAttribute(s,S?"":En(l)?String(l):l)}function Wa(r,s,l,p,h){if(s==="innerHTML"||s==="textContent"){l!=null&&(r[s]=s==="innerHTML"?Zs(l):l);return}const S=r.tagName;if(s==="value"&&S!=="PROGRESS"&&!S.includes("-")){const m=S==="OPTION"?r.getAttribute("value")||"":r.value,_=l==null?r.type==="checkbox"?"on":"":String(l);(m!==_||!("_value"in r))&&(r.value=_),l==null&&r.removeAttribute(s),r._value=l;return}let M=!1;if(l===""||l==null){const m=typeof r[s];m==="boolean"?l=Qa(l):l==null&&m==="string"?(l="",M=!0):m==="number"&&(l=0,M=!0)}try{r[s]=l}catch{}M&&r.removeAttribute(h||s)}function qu(r,s,l,p){r.addEventListener(s,l,p)}function zu(r,s,l,p){r.removeEventListener(s,l,p)}const Ka=Symbol("_vei");function Gu(r,s,l,p,h=null){const S=r[Ka]||(r[Ka]={}),M=S[s];if(p&&M)M.value=p;else{const[m,_]=$u(s);if(p){const D=S[s]=Xu(p,h);qu(r,m,D,_)}else M&&(zu(r,m,M,_),S[s]=void 0)}}const qa=/(?:Once|Passive|Capture)$/;function $u(r){let s;if(qa.test(r)){s={};let p;for(;p=r.match(qa);)r=r.slice(0,r.length-p[0].length),s[p[0].toLowerCase()]=!0}return[r[2]===":"?r.slice(3):Zt(r.slice(2)),s]}let ti=0;const Ju=Promise.resolve(),Zu=()=>ti||(Ju.then(()=>ti=0),ti=Date.now());function Xu(r,s){const l=p=>{if(!p._vts)p._vts=Date.now();else if(p._vts<=l.attached)return;Ht(Yu(p,l.value),s,5,[p])};return l.value=r,l.attached=Zu(),l}function Yu(r,s){if(be(s)){const l=r.stopImmediatePropagation;return r.stopImmediatePropagation=()=>{l.call(r),r._stopped=!0},s.map(p=>h=>!h._stopped&&p&&p(h))}else return s}const za=r=>r.charCodeAt(0)===111&&r.charCodeAt(1)===110&&r.charCodeAt(2)>96&&r.charCodeAt(2)<123,Qu=(r,s,l,p,h,S)=>{const M=h==="svg";s==="class"?Bu(r,p,M):s==="style"?Wu(r,l,p):wr(s)?mi(s)||Gu(r,s,l,p,S):(s[0]==="."?(s=s.slice(1),!0):s[0]==="^"?(s=s.slice(1),!1):ef(r,s,p,M))?(Wa(r,s,p),!r.tagName.includes("-")&&(s==="value"||s==="checked"||s==="selected")&&Fa(r,s,p,M,S,s!=="value")):r._isVueCE&&(/[A-Z]/.test(s)||!$e(p))?Wa(r,Bt(s),p,S,s):(s==="true-value"?r._trueValue=p:s==="false-value"&&(r._falseValue=p),Fa(r,s,p,M))};function ef(r,s,l,p){if(p)return!!(s==="innerHTML"||s==="textContent"||s in r&&za(s)&&ve(l));if(s==="spellcheck"||s==="draggable"||s==="translate"||s==="autocorrect"||s==="form"||s==="list"&&r.tagName==="INPUT"||s==="type"&&r.tagName==="TEXTAREA")return!1;if(s==="width"||s==="height"){const h=r.tagName;if(h==="IMG"||h==="VIDEO"||h==="CANVAS"||h==="SOURCE")return!1}return za(s)&&$e(l)?!1:s in r}const tf=Ye({patchProp:Qu},Pu);let Ga;function nf(){return Ga||(Ga=iu(tf))}const rf=((...r)=>{const s=nf().createApp(...r),{mount:l}=s;return s.mount=p=>{const h=sf(p);if(!h)return;const S=s._component;!ve(S)&&!S.render&&!S.template&&(S.template=h.innerHTML),h.nodeType===1&&(h.textContent="");const M=l(h,!1,af(h));return h instanceof Element&&(h.removeAttribute("v-cloak"),h.setAttribute("data-v-app","")),M},s});function af(r){if(r instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&r instanceof MathMLElement)return"mathml"}function sf(r){return $e(r)?document.querySelector(r):r}function of(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var fr={exports:{}};/*!
 * Vditor v3.11.1 - A markdown editor written in TypeScript.
 *
 * MIT License
 *
 * Copyright (c) 2018-present B3log 开源, b3log.org
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 */var lf=fr.exports,$a;function cf(){return $a||($a=1,(function(r,s){(function(p,h){r.exports=h()})(lf,function(){return(()=>{var l={173:(M=>{var m=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32},_=-1,D=1,k=0;m.Diff=function(u,c){return[u,c]},m.prototype.diff_main=function(u,c,b,g){typeof g>"u"&&(this.Diff_Timeout<=0?g=Number.MAX_VALUE:g=new Date().getTime()+this.Diff_Timeout*1e3);var E=g;if(u==null||c==null)throw new Error("Null input. (diff_main)");if(u==c)return u?[new m.Diff(k,u)]:[];typeof b>"u"&&(b=!0);var y=b,O=this.diff_commonPrefix(u,c),L=u.substring(0,O);u=u.substring(O),c=c.substring(O),O=this.diff_commonSuffix(u,c);var A=u.substring(u.length-O);u=u.substring(0,u.length-O),c=c.substring(0,c.length-O);var T=this.diff_compute_(u,c,y,E);return L&&T.unshift(new m.Diff(k,L)),A&&T.push(new m.Diff(k,A)),this.diff_cleanupMerge(T),T},m.prototype.diff_compute_=function(u,c,b,g){var E;if(!u)return[new m.Diff(D,c)];if(!c)return[new m.Diff(_,u)];var y=u.length>c.length?u:c,O=u.length>c.length?c:u,L=y.indexOf(O);if(L!=-1)return E=[new m.Diff(D,y.substring(0,L)),new m.Diff(k,O),new m.Diff(D,y.substring(L+O.length))],u.length>c.length&&(E[0][0]=E[2][0]=_),E;if(O.length==1)return[new m.Diff(_,u),new m.Diff(D,c)];var A=this.diff_halfMatch_(u,c);if(A){var T=A[0],f=A[1],R=A[2],V=A[3],W=A[4],P=this.diff_main(T,R,b,g),$=this.diff_main(f,V,b,g);return P.concat([new m.Diff(k,W)],$)}return b&&u.length>100&&c.length>100?this.diff_lineMode_(u,c,g):this.diff_bisect_(u,c,g)},m.prototype.diff_lineMode_=function(u,c,b){var g=this.diff_linesToChars_(u,c);u=g.chars1,c=g.chars2;var E=g.lineArray,y=this.diff_main(u,c,!1,b);this.diff_charsToLines_(y,E),this.diff_cleanupSemantic(y),y.push(new m.Diff(k,""));for(var O=0,L=0,A=0,T="",f="";O<y.length;){switch(y[O][0]){case D:A++,f+=y[O][1];break;case _:L++,T+=y[O][1];break;case k:if(L>=1&&A>=1){y.splice(O-L-A,L+A),O=O-L-A;for(var R=this.diff_main(T,f,!1,b),V=R.length-1;V>=0;V--)y.splice(O,0,R[V]);O=O+R.length}A=0,L=0,T="",f="";break}O++}return y.pop(),y},m.prototype.diff_bisect_=function(u,c,b){for(var g=u.length,E=c.length,y=Math.ceil((g+E)/2),O=y,L=2*y,A=new Array(L),T=new Array(L),f=0;f<L;f++)A[f]=-1,T[f]=-1;A[O+1]=0,T[O+1]=0;for(var R=g-E,V=R%2!=0,W=0,P=0,$=0,re=0,ue=0;ue<y&&!(new Date().getTime()>b);ue++){for(var ye=-ue+W;ye<=ue-P;ye+=2){var Se=O+ye,Ae;ye==-ue||ye!=ue&&A[Se-1]<A[Se+1]?Ae=A[Se+1]:Ae=A[Se-1]+1;for(var Ce=Ae-ye;Ae<g&&Ce<E&&u.charAt(Ae)==c.charAt(Ce);)Ae++,Ce++;if(A[Se]=Ae,Ae>g)P+=2;else if(Ce>E)W+=2;else if(V){var ge=O+R-ye;if(ge>=0&&ge<L&&T[ge]!=-1){var pe=g-T[ge];if(Ae>=pe)return this.diff_bisectSplit_(u,c,Ae,Ce,b)}}}for(var Re=-ue+$;Re<=ue-re;Re+=2){var ge=O+Re,pe;Re==-ue||Re!=ue&&T[ge-1]<T[ge+1]?pe=T[ge+1]:pe=T[ge-1]+1;for(var Ke=pe-Re;pe<g&&Ke<E&&u.charAt(g-pe-1)==c.charAt(E-Ke-1);)pe++,Ke++;if(T[ge]=pe,pe>g)re+=2;else if(Ke>E)$+=2;else if(!V){var Se=O+R-Re;if(Se>=0&&Se<L&&A[Se]!=-1){var Ae=A[Se],Ce=O+Ae-Se;if(pe=g-pe,Ae>=pe)return this.diff_bisectSplit_(u,c,Ae,Ce,b)}}}}return[new m.Diff(_,u),new m.Diff(D,c)]},m.prototype.diff_bisectSplit_=function(u,c,b,g,E){var y=u.substring(0,b),O=c.substring(0,g),L=u.substring(b),A=c.substring(g),T=this.diff_main(y,O,!1,E),f=this.diff_main(L,A,!1,E);return T.concat(f)},m.prototype.diff_linesToChars_=function(u,c){var b=[],g={};b[0]="";function E(A){for(var T="",f=0,R=-1,V=b.length;R<A.length-1;){R=A.indexOf(`
`,f),R==-1&&(R=A.length-1);var W=A.substring(f,R+1);(g.hasOwnProperty?g.hasOwnProperty(W):g[W]!==void 0)?T+=String.fromCharCode(g[W]):(V==y&&(W=A.substring(f),R=A.length),T+=String.fromCharCode(V),g[W]=V,b[V++]=W),f=R+1}return T}var y=4e4,O=E(u);y=65535;var L=E(c);return{chars1:O,chars2:L,lineArray:b}},m.prototype.diff_charsToLines_=function(u,c){for(var b=0;b<u.length;b++){for(var g=u[b][1],E=[],y=0;y<g.length;y++)E[y]=c[g.charCodeAt(y)];u[b][1]=E.join("")}},m.prototype.diff_commonPrefix=function(u,c){if(!u||!c||u.charAt(0)!=c.charAt(0))return 0;for(var b=0,g=Math.min(u.length,c.length),E=g,y=0;b<E;)u.substring(y,E)==c.substring(y,E)?(b=E,y=b):g=E,E=Math.floor((g-b)/2+b);return E},m.prototype.diff_commonSuffix=function(u,c){if(!u||!c||u.charAt(u.length-1)!=c.charAt(c.length-1))return 0;for(var b=0,g=Math.min(u.length,c.length),E=g,y=0;b<E;)u.substring(u.length-E,u.length-y)==c.substring(c.length-E,c.length-y)?(b=E,y=b):g=E,E=Math.floor((g-b)/2+b);return E},m.prototype.diff_commonOverlap_=function(u,c){var b=u.length,g=c.length;if(b==0||g==0)return 0;b>g?u=u.substring(b-g):b<g&&(c=c.substring(0,b));var E=Math.min(b,g);if(u==c)return E;for(var y=0,O=1;;){var L=u.substring(E-O),A=c.indexOf(L);if(A==-1)return y;O+=A,(A==0||u.substring(E-O)==c.substring(0,O))&&(y=O,O++)}},m.prototype.diff_halfMatch_=function(u,c){if(this.Diff_Timeout<=0)return null;var b=u.length>c.length?u:c,g=u.length>c.length?c:u;if(b.length<4||g.length*2<b.length)return null;var E=this;function y(P,$,re){for(var ue=P.substring(re,re+Math.floor(P.length/4)),ye=-1,Se="",Ae,Ce,ge,pe;(ye=$.indexOf(ue,ye+1))!=-1;){var Re=E.diff_commonPrefix(P.substring(re),$.substring(ye)),Ke=E.diff_commonSuffix(P.substring(0,re),$.substring(0,ye));Se.length<Ke+Re&&(Se=$.substring(ye-Ke,ye)+$.substring(ye,ye+Re),Ae=P.substring(0,re-Ke),Ce=P.substring(re+Re),ge=$.substring(0,ye-Ke),pe=$.substring(ye+Re))}return Se.length*2>=P.length?[Ae,Ce,ge,pe,Se]:null}var O=y(b,g,Math.ceil(b.length/4)),L=y(b,g,Math.ceil(b.length/2)),A;if(!O&&!L)return null;L?O?A=O[4].length>L[4].length?O:L:A=L:A=O;var T,f,R,V;u.length>c.length?(T=A[0],f=A[1],R=A[2],V=A[3]):(R=A[0],V=A[1],T=A[2],f=A[3]);var W=A[4];return[T,f,R,V,W]},m.prototype.diff_cleanupSemantic=function(u){for(var c=!1,b=[],g=0,E=null,y=0,O=0,L=0,A=0,T=0;y<u.length;)u[y][0]==k?(b[g++]=y,O=A,L=T,A=0,T=0,E=u[y][1]):(u[y][0]==D?A+=u[y][1].length:T+=u[y][1].length,E&&E.length<=Math.max(O,L)&&E.length<=Math.max(A,T)&&(u.splice(b[g-1],0,new m.Diff(_,E)),u[b[g-1]+1][0]=D,g--,g--,y=g>0?b[g-1]:-1,O=0,L=0,A=0,T=0,E=null,c=!0)),y++;for(c&&this.diff_cleanupMerge(u),this.diff_cleanupSemanticLossless(u),y=1;y<u.length;){if(u[y-1][0]==_&&u[y][0]==D){var f=u[y-1][1],R=u[y][1],V=this.diff_commonOverlap_(f,R),W=this.diff_commonOverlap_(R,f);V>=W?(V>=f.length/2||V>=R.length/2)&&(u.splice(y,0,new m.Diff(k,R.substring(0,V))),u[y-1][1]=f.substring(0,f.length-V),u[y+1][1]=R.substring(V),y++):(W>=f.length/2||W>=R.length/2)&&(u.splice(y,0,new m.Diff(k,f.substring(0,W))),u[y-1][0]=D,u[y-1][1]=R.substring(0,R.length-W),u[y+1][0]=_,u[y+1][1]=f.substring(W),y++),y++}y++}},m.prototype.diff_cleanupSemanticLossless=function(u){function c(W,P){if(!W||!P)return 6;var $=W.charAt(W.length-1),re=P.charAt(0),ue=$.match(m.nonAlphaNumericRegex_),ye=re.match(m.nonAlphaNumericRegex_),Se=ue&&$.match(m.whitespaceRegex_),Ae=ye&&re.match(m.whitespaceRegex_),Ce=Se&&$.match(m.linebreakRegex_),ge=Ae&&re.match(m.linebreakRegex_),pe=Ce&&W.match(m.blanklineEndRegex_),Re=ge&&P.match(m.blanklineStartRegex_);return pe||Re?5:Ce||ge?4:ue&&!Se&&Ae?3:Se||Ae?2:ue||ye?1:0}for(var b=1;b<u.length-1;){if(u[b-1][0]==k&&u[b+1][0]==k){var g=u[b-1][1],E=u[b][1],y=u[b+1][1],O=this.diff_commonSuffix(g,E);if(O){var L=E.substring(E.length-O);g=g.substring(0,g.length-O),E=L+E.substring(0,E.length-O),y=L+y}for(var A=g,T=E,f=y,R=c(g,E)+c(E,y);E.charAt(0)===y.charAt(0);){g+=E.charAt(0),E=E.substring(1)+y.charAt(0),y=y.substring(1);var V=c(g,E)+c(E,y);V>=R&&(R=V,A=g,T=E,f=y)}u[b-1][1]!=A&&(A?u[b-1][1]=A:(u.splice(b-1,1),b--),u[b][1]=T,f?u[b+1][1]=f:(u.splice(b+1,1),b--))}b++}},m.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,m.whitespaceRegex_=/\s/,m.linebreakRegex_=/[\r\n]/,m.blanklineEndRegex_=/\n\r?\n$/,m.blanklineStartRegex_=/^\r?\n\r?\n/,m.prototype.diff_cleanupEfficiency=function(u){for(var c=!1,b=[],g=0,E=null,y=0,O=!1,L=!1,A=!1,T=!1;y<u.length;)u[y][0]==k?(u[y][1].length<this.Diff_EditCost&&(A||T)?(b[g++]=y,O=A,L=T,E=u[y][1]):(g=0,E=null),A=T=!1):(u[y][0]==_?T=!0:A=!0,E&&(O&&L&&A&&T||E.length<this.Diff_EditCost/2&&O+L+A+T==3)&&(u.splice(b[g-1],0,new m.Diff(_,E)),u[b[g-1]+1][0]=D,g--,E=null,O&&L?(A=T=!0,g=0):(g--,y=g>0?b[g-1]:-1,A=T=!1),c=!0)),y++;c&&this.diff_cleanupMerge(u)},m.prototype.diff_cleanupMerge=function(u){u.push(new m.Diff(k,""));for(var c=0,b=0,g=0,E="",y="",O;c<u.length;)switch(u[c][0]){case D:g++,y+=u[c][1],c++;break;case _:b++,E+=u[c][1],c++;break;case k:b+g>1?(b!==0&&g!==0&&(O=this.diff_commonPrefix(y,E),O!==0&&(c-b-g>0&&u[c-b-g-1][0]==k?u[c-b-g-1][1]+=y.substring(0,O):(u.splice(0,0,new m.Diff(k,y.substring(0,O))),c++),y=y.substring(O),E=E.substring(O)),O=this.diff_commonSuffix(y,E),O!==0&&(u[c][1]=y.substring(y.length-O)+u[c][1],y=y.substring(0,y.length-O),E=E.substring(0,E.length-O))),c-=b+g,u.splice(c,b+g),E.length&&(u.splice(c,0,new m.Diff(_,E)),c++),y.length&&(u.splice(c,0,new m.Diff(D,y)),c++),c++):c!==0&&u[c-1][0]==k?(u[c-1][1]+=u[c][1],u.splice(c,1)):c++,g=0,b=0,E="",y="";break}u[u.length-1][1]===""&&u.pop();var L=!1;for(c=1;c<u.length-1;)u[c-1][0]==k&&u[c+1][0]==k&&(u[c][1].substring(u[c][1].length-u[c-1][1].length)==u[c-1][1]?(u[c][1]=u[c-1][1]+u[c][1].substring(0,u[c][1].length-u[c-1][1].length),u[c+1][1]=u[c-1][1]+u[c+1][1],u.splice(c-1,1),L=!0):u[c][1].substring(0,u[c+1][1].length)==u[c+1][1]&&(u[c-1][1]+=u[c+1][1],u[c][1]=u[c][1].substring(u[c+1][1].length)+u[c+1][1],u.splice(c+1,1),L=!0)),c++;L&&this.diff_cleanupMerge(u)},m.prototype.diff_xIndex=function(u,c){var b=0,g=0,E=0,y=0,O;for(O=0;O<u.length&&(u[O][0]!==D&&(b+=u[O][1].length),u[O][0]!==_&&(g+=u[O][1].length),!(b>c));O++)E=b,y=g;return u.length!=O&&u[O][0]===_?y:y+(c-E)},m.prototype.diff_prettyHtml=function(u){for(var c=[],b=/&/g,g=/</g,E=/>/g,y=/\n/g,O=0;O<u.length;O++){var L=u[O][0],A=u[O][1],T=A.replace(b,"&amp;").replace(g,"&lt;").replace(E,"&gt;").replace(y,"&para;<br>");switch(L){case D:c[O]='<ins style="background:#e6ffe6;">'+T+"</ins>";break;case _:c[O]='<del style="background:#ffe6e6;">'+T+"</del>";break;case k:c[O]="<span>"+T+"</span>";break}}return c.join("")},m.prototype.diff_text1=function(u){for(var c=[],b=0;b<u.length;b++)u[b][0]!==D&&(c[b]=u[b][1]);return c.join("")},m.prototype.diff_text2=function(u){for(var c=[],b=0;b<u.length;b++)u[b][0]!==_&&(c[b]=u[b][1]);return c.join("")},m.prototype.diff_levenshtein=function(u){for(var c=0,b=0,g=0,E=0;E<u.length;E++){var y=u[E][0],O=u[E][1];switch(y){case D:b+=O.length;break;case _:g+=O.length;break;case k:c+=Math.max(b,g),b=0,g=0;break}}return c+=Math.max(b,g),c},m.prototype.diff_toDelta=function(u){for(var c=[],b=0;b<u.length;b++)switch(u[b][0]){case D:c[b]="+"+encodeURI(u[b][1]);break;case _:c[b]="-"+u[b][1].length;break;case k:c[b]="="+u[b][1].length;break}return c.join("	").replace(/%20/g," ")},m.prototype.diff_fromDelta=function(u,c){for(var b=[],g=0,E=0,y=c.split(/\t/g),O=0;O<y.length;O++){var L=y[O].substring(1);switch(y[O].charAt(0)){case"+":try{b[g++]=new m.Diff(D,decodeURI(L))}catch{throw new Error("Illegal escape in diff_fromDelta: "+L)}break;case"-":case"=":var A=parseInt(L,10);if(isNaN(A)||A<0)throw new Error("Invalid number in diff_fromDelta: "+L);var T=u.substring(E,E+=A);y[O].charAt(0)=="="?b[g++]=new m.Diff(k,T):b[g++]=new m.Diff(_,T);break;default:if(y[O])throw new Error("Invalid diff operation in diff_fromDelta: "+y[O])}}if(E!=u.length)throw new Error("Delta length ("+E+") does not equal source text length ("+u.length+").");return b},m.prototype.match_main=function(u,c,b){if(u==null||c==null||b==null)throw new Error("Null input. (match_main)");return b=Math.max(0,Math.min(b,u.length)),u==c?0:u.length?u.substring(b,b+c.length)==c?b:this.match_bitap_(u,c,b):-1},m.prototype.match_bitap_=function(u,c,b){if(c.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var g=this.match_alphabet_(c),E=this;function y(Ae,Ce){var ge=Ae/c.length,pe=Math.abs(b-Ce);return E.Match_Distance?ge+pe/E.Match_Distance:pe?1:ge}var O=this.Match_Threshold,L=u.indexOf(c,b);L!=-1&&(O=Math.min(y(0,L),O),L=u.lastIndexOf(c,b+c.length),L!=-1&&(O=Math.min(y(0,L),O)));var A=1<<c.length-1;L=-1;for(var T,f,R=c.length+u.length,V,W=0;W<c.length;W++){for(T=0,f=R;T<f;)y(W,b+f)<=O?T=f:R=f,f=Math.floor((R-T)/2+T);R=f;var P=Math.max(1,b-f+1),$=Math.min(b+f,u.length)+c.length,re=Array($+2);re[$+1]=(1<<W)-1;for(var ue=$;ue>=P;ue--){var ye=g[u.charAt(ue-1)];if(W===0?re[ue]=(re[ue+1]<<1|1)&ye:re[ue]=(re[ue+1]<<1|1)&ye|((V[ue+1]|V[ue])<<1|1)|V[ue+1],re[ue]&A){var Se=y(W,ue-1);if(Se<=O)if(O=Se,L=ue-1,L>b)P=Math.max(1,2*b-L);else break}}if(y(W+1,b)>O)break;V=re}return L},m.prototype.match_alphabet_=function(u){for(var c={},b=0;b<u.length;b++)c[u.charAt(b)]=0;for(var b=0;b<u.length;b++)c[u.charAt(b)]|=1<<u.length-b-1;return c},m.prototype.patch_addContext_=function(u,c){if(c.length!=0){if(u.start2===null)throw Error("patch not initialized");for(var b=c.substring(u.start2,u.start2+u.length1),g=0;c.indexOf(b)!=c.lastIndexOf(b)&&b.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)g+=this.Patch_Margin,b=c.substring(u.start2-g,u.start2+u.length1+g);g+=this.Patch_Margin;var E=c.substring(u.start2-g,u.start2);E&&u.diffs.unshift(new m.Diff(k,E));var y=c.substring(u.start2+u.length1,u.start2+u.length1+g);y&&u.diffs.push(new m.Diff(k,y)),u.start1-=E.length,u.start2-=E.length,u.length1+=E.length+y.length,u.length2+=E.length+y.length}},m.prototype.patch_make=function(u,c,b){var g,E;if(typeof u=="string"&&typeof c=="string"&&typeof b>"u")g=u,E=this.diff_main(g,c,!0),E.length>2&&(this.diff_cleanupSemantic(E),this.diff_cleanupEfficiency(E));else if(u&&typeof u=="object"&&typeof c>"u"&&typeof b>"u")E=u,g=this.diff_text1(E);else if(typeof u=="string"&&c&&typeof c=="object"&&typeof b>"u")g=u,E=c;else if(typeof u=="string"&&typeof c=="string"&&b&&typeof b=="object")g=u,E=b;else throw new Error("Unknown call format to patch_make.");if(E.length===0)return[];for(var y=[],O=new m.patch_obj,L=0,A=0,T=0,f=g,R=g,V=0;V<E.length;V++){var W=E[V][0],P=E[V][1];switch(!L&&W!==k&&(O.start1=A,O.start2=T),W){case D:O.diffs[L++]=E[V],O.length2+=P.length,R=R.substring(0,T)+P+R.substring(T);break;case _:O.length1+=P.length,O.diffs[L++]=E[V],R=R.substring(0,T)+R.substring(T+P.length);break;case k:P.length<=2*this.Patch_Margin&&L&&E.length!=V+1?(O.diffs[L++]=E[V],O.length1+=P.length,O.length2+=P.length):P.length>=2*this.Patch_Margin&&L&&(this.patch_addContext_(O,f),y.push(O),O=new m.patch_obj,L=0,f=R,A=T);break}W!==D&&(A+=P.length),W!==_&&(T+=P.length)}return L&&(this.patch_addContext_(O,f),y.push(O)),y},m.prototype.patch_deepCopy=function(u){for(var c=[],b=0;b<u.length;b++){var g=u[b],E=new m.patch_obj;E.diffs=[];for(var y=0;y<g.diffs.length;y++)E.diffs[y]=new m.Diff(g.diffs[y][0],g.diffs[y][1]);E.start1=g.start1,E.start2=g.start2,E.length1=g.length1,E.length2=g.length2,c[b]=E}return c},m.prototype.patch_apply=function(u,c){if(u.length==0)return[c,[]];u=this.patch_deepCopy(u);var b=this.patch_addPadding(u);c=b+c+b,this.patch_splitMax(u);for(var g=0,E=[],y=0;y<u.length;y++){var O=u[y].start2+g,L=this.diff_text1(u[y].diffs),A,T=-1;if(L.length>this.Match_MaxBits?(A=this.match_main(c,L.substring(0,this.Match_MaxBits),O),A!=-1&&(T=this.match_main(c,L.substring(L.length-this.Match_MaxBits),O+L.length-this.Match_MaxBits),(T==-1||A>=T)&&(A=-1))):A=this.match_main(c,L,O),A==-1)E[y]=!1,g-=u[y].length2-u[y].length1;else{E[y]=!0,g=A-O;var f;if(T==-1?f=c.substring(A,A+L.length):f=c.substring(A,T+this.Match_MaxBits),L==f)c=c.substring(0,A)+this.diff_text2(u[y].diffs)+c.substring(A+L.length);else{var R=this.diff_main(L,f,!1);if(L.length>this.Match_MaxBits&&this.diff_levenshtein(R)/L.length>this.Patch_DeleteThreshold)E[y]=!1;else{this.diff_cleanupSemanticLossless(R);for(var V=0,W,P=0;P<u[y].diffs.length;P++){var $=u[y].diffs[P];$[0]!==k&&(W=this.diff_xIndex(R,V)),$[0]===D?c=c.substring(0,A+W)+$[1]+c.substring(A+W):$[0]===_&&(c=c.substring(0,A+W)+c.substring(A+this.diff_xIndex(R,V+$[1].length))),$[0]!==_&&(V+=$[1].length)}}}}}return c=c.substring(b.length,c.length-b.length),[c,E]},m.prototype.patch_addPadding=function(u){for(var c=this.Patch_Margin,b="",g=1;g<=c;g++)b+=String.fromCharCode(g);for(var g=0;g<u.length;g++)u[g].start1+=c,u[g].start2+=c;var E=u[0],y=E.diffs;if(y.length==0||y[0][0]!=k)y.unshift(new m.Diff(k,b)),E.start1-=c,E.start2-=c,E.length1+=c,E.length2+=c;else if(c>y[0][1].length){var O=c-y[0][1].length;y[0][1]=b.substring(y[0][1].length)+y[0][1],E.start1-=O,E.start2-=O,E.length1+=O,E.length2+=O}if(E=u[u.length-1],y=E.diffs,y.length==0||y[y.length-1][0]!=k)y.push(new m.Diff(k,b)),E.length1+=c,E.length2+=c;else if(c>y[y.length-1][1].length){var O=c-y[y.length-1][1].length;y[y.length-1][1]+=b.substring(0,O),E.length1+=O,E.length2+=O}return b},m.prototype.patch_splitMax=function(u){for(var c=this.Match_MaxBits,b=0;b<u.length;b++)if(!(u[b].length1<=c)){var g=u[b];u.splice(b--,1);for(var E=g.start1,y=g.start2,O="";g.diffs.length!==0;){var L=new m.patch_obj,A=!0;for(L.start1=E-O.length,L.start2=y-O.length,O!==""&&(L.length1=L.length2=O.length,L.diffs.push(new m.Diff(k,O)));g.diffs.length!==0&&L.length1<c-this.Patch_Margin;){var T=g.diffs[0][0],f=g.diffs[0][1];T===D?(L.length2+=f.length,y+=f.length,L.diffs.push(g.diffs.shift()),A=!1):T===_&&L.diffs.length==1&&L.diffs[0][0]==k&&f.length>2*c?(L.length1+=f.length,E+=f.length,A=!1,L.diffs.push(new m.Diff(T,f)),g.diffs.shift()):(f=f.substring(0,c-L.length1-this.Patch_Margin),L.length1+=f.length,E+=f.length,T===k?(L.length2+=f.length,y+=f.length):A=!1,L.diffs.push(new m.Diff(T,f)),f==g.diffs[0][1]?g.diffs.shift():g.diffs[0][1]=g.diffs[0][1].substring(f.length))}O=this.diff_text2(L.diffs),O=O.substring(O.length-this.Patch_Margin);var R=this.diff_text1(g.diffs).substring(0,this.Patch_Margin);R!==""&&(L.length1+=R.length,L.length2+=R.length,L.diffs.length!==0&&L.diffs[L.diffs.length-1][0]===k?L.diffs[L.diffs.length-1][1]+=R:L.diffs.push(new m.Diff(k,R))),A||u.splice(++b,0,L)}}},m.prototype.patch_toText=function(u){for(var c=[],b=0;b<u.length;b++)c[b]=u[b];return c.join("")},m.prototype.patch_fromText=function(u){var c=[];if(!u)return c;for(var b=u.split(`
`),g=0,E=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;g<b.length;){var y=b[g].match(E);if(!y)throw new Error("Invalid patch string: "+b[g]);var O=new m.patch_obj;for(c.push(O),O.start1=parseInt(y[1],10),y[2]===""?(O.start1--,O.length1=1):y[2]=="0"?O.length1=0:(O.start1--,O.length1=parseInt(y[2],10)),O.start2=parseInt(y[3],10),y[4]===""?(O.start2--,O.length2=1):y[4]=="0"?O.length2=0:(O.start2--,O.length2=parseInt(y[4],10)),g++;g<b.length;){var L=b[g].charAt(0);try{var A=decodeURI(b[g].substring(1))}catch{throw new Error("Illegal escape in patch_fromText: "+A)}if(L=="-")O.diffs.push(new m.Diff(_,A));else if(L=="+")O.diffs.push(new m.Diff(D,A));else if(L==" ")O.diffs.push(new m.Diff(k,A));else{if(L=="@")break;if(L!=="")throw new Error('Invalid patch mode "'+L+'" in: '+A)}g++}}return c},m.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},m.patch_obj.prototype.toString=function(){var u,c;this.length1===0?u=this.start1+",0":this.length1==1?u=this.start1+1:u=this.start1+1+","+this.length1,this.length2===0?c=this.start2+",0":this.length2==1?c=this.start2+1:c=this.start2+1+","+this.length2;for(var b=["@@ -"+u+" +"+c+` @@
`],g,E=0;E<this.diffs.length;E++){switch(this.diffs[E][0]){case D:g="+";break;case _:g="-";break;case k:g=" ";break}b[E+1]=g+encodeURI(this.diffs[E][1])+`
`}return b.join("").replace(/%20/g," ")},M.exports=m,M.exports.diff_match_patch=m,M.exports.DIFF_DELETE=_,M.exports.DIFF_INSERT=D,M.exports.DIFF_EQUAL=k}),408:((M,m,_)=>{_.d(m,{default:()=>st});var D=_(135),k=_(840),u=_(775),c=_(428),b=_(325),g=_(483),E=_(999),y=function(q){q===void 0&&(q=document);var Me=function(ie){var Q=document.createElement("img");Q.src=ie.getAttribute("data-src"),Q.addEventListener("load",function(){!ie.getAttribute("style")&&!ie.getAttribute("class")&&!ie.getAttribute("width")&&!ie.getAttribute("height")&&Q.naturalHeight>Q.naturalWidth&&Q.naturalWidth/Q.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&Q.naturalHeight>window.innerHeight-40&&(ie.style.height=window.innerHeight-40+"px"),ie.src=Q.src}),ie.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return q.querySelectorAll("img").forEach(function(ie){ie.getAttribute("data-src")&&Me(ie)}),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),q.querySelectorAll("img").forEach(function(ie){window.vditorImageIntersectionObserver.observe(ie)})):(window.vditorImageIntersectionObserver=new IntersectionObserver(function(ie){ie.forEach(function(Q){(typeof Q.isIntersecting>"u"?Q.intersectionRatio!==0:Q.isIntersecting)&&Q.target.getAttribute("data-src")&&Me(Q.target)})}),q.querySelectorAll("img").forEach(function(ie){window.vditorImageIntersectionObserver.observe(ie)}))},O=_(472),L=_(280),A=_(637),T=_(825),f=_(11),R=_(194),V=_(436),W=_(229),P=_(145),$=_(538),re=_(413),ue=_(106),ye=_(673),Se=function(q){document.querySelectorAll(".vditor-anchor").forEach(function(Me){q===1&&Me.classList.add("vditor-anchor--left"),Me.onclick=function(){var ie=Me.getAttribute("href").substr(1),Q=document.getElementById("vditorAnchor-"+ie).offsetTop;document.querySelector("html").scrollTop=Q}}),window.onhashchange=function(){var Me=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));Me&&(document.querySelector("html").scrollTop=Me.offsetTop)}},Ae=_(214),Ce=_(810),ge=function(q,Me){if(Me===void 0&&(Me="zh_CN"),!(typeof speechSynthesis>"u"||typeof SpeechSynthesisUtterance>"u")){var ie=function(){var K=speechSynthesis.getVoices(),U,F;return K.forEach(function(ee){ee.lang===Me.replace("_","-")&&(U=ee),ee.default&&(F=ee)}),U||(U=F),U},Q='<svg><use xlink:href="#vditor-icon-play"></use></svg>',Be='<svg><use xlink:href="#vditor-icon-pause"></use></svg>';document.getElementById("vditorIconScript")||(Q='<svg viewBox="0 0 32 32"><path d="M3.436 0l25.128 16-25.128 16v-32z"></path></svg>',Be='<svg viewBox="0 0 32 32"><path d="M20.617 0h9.128v32h-9.128v-32zM2.255 32v-32h9.128v32h-9.128z"></path></svg>');var x=document.querySelector(".vditor-speech");x||(x=document.createElement("button"),x.className="vditor-speech",q.insertAdjacentElement("beforeend",x),speechSynthesis.onvoiceschanged!==void 0&&(speechSynthesis.onvoiceschanged=ie));var N=ie(),B=new SpeechSynthesisUtterance;B.voice=N,B.onend=B.onerror=function(){x.style.display="none",speechSynthesis.cancel(),x.classList.remove("vditor-speech--current"),x.innerHTML=Q},q.addEventListener(window.ontouchstart!==void 0?"touchend":"click",function(K){var U=K.target;if(U.classList.contains("vditor-speech")||U.parentElement.classList.contains("vditor-speech")){x.classList.contains("vditor-speech--current")?speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),x.innerHTML=Be):(speechSynthesis.pause(),x.innerHTML=Q)):(B.text=x.getAttribute("data-text"),speechSynthesis.speak(B),x.classList.add("vditor-speech--current"),x.innerHTML=Be),(0,Ce.Hc)(window.vditorSpeechRange),q.focus();return}if(x.style.display="none",speechSynthesis.cancel(),x.classList.remove("vditor-speech--current"),x.innerHTML=Q,getSelection().rangeCount!==0){var F=getSelection().getRangeAt(0),ee=F.toString().trim();if(ee){window.vditorSpeechRange=F.cloneRange();var Y=F.getBoundingClientRect();x.innerHTML=Q,x.style.display="block",x.style.top=Y.top+Y.height+document.querySelector("html").scrollTop-20+"px",window.ontouchstart!==void 0?x.style.left=K.changedTouches[K.changedTouches.length-1].pageX+2+"px":x.style.left=K.clientX+2+"px",x.setAttribute("data-text",ee)}}})}},pe=function(q,Me,ie,Q){function Be(x){return x instanceof ie?x:new ie(function(N){N(x)})}return new(ie||(ie=Promise))(function(x,N){function B(F){try{U(Q.next(F))}catch(ee){N(ee)}}function K(F){try{U(Q.throw(F))}catch(ee){N(ee)}}function U(F){F.done?x(F.value):Be(F.value).then(B,K)}U((Q=Q.apply(q,Me||[])).next())})},Re=function(q,Me){var ie={label:0,sent:function(){if(x[0]&1)throw x[1];return x[1]},trys:[],ops:[]},Q,Be,x,N;return N={next:B(0),throw:B(1),return:B(2)},typeof Symbol=="function"&&(N[Symbol.iterator]=function(){return this}),N;function B(U){return function(F){return K([U,F])}}function K(U){if(Q)throw new TypeError("Generator is already executing.");for(;N&&(N=0,U[0]&&(ie=0)),ie;)try{if(Q=1,Be&&(x=U[0]&2?Be.return:U[0]?Be.throw||((x=Be.return)&&x.call(Be),0):Be.next)&&!(x=x.call(Be,U[1])).done)return x;switch(Be=0,x&&(U=[U[0]&2,x.value]),U[0]){case 0:case 1:x=U;break;case 4:return ie.label++,{value:U[1],done:!1};case 5:ie.label++,Be=U[1],U=[0];continue;case 7:U=ie.ops.pop(),ie.trys.pop();continue;default:if(x=ie.trys,!(x=x.length>0&&x[x.length-1])&&(U[0]===6||U[0]===2)){ie=0;continue}if(U[0]===3&&(!x||U[1]>x[0]&&U[1]<x[3])){ie.label=U[1];break}if(U[0]===6&&ie.label<x[1]){ie.label=x[1],x=U;break}if(x&&ie.label<x[2]){ie.label=x[2],ie.ops.push(U);break}x[2]&&ie.ops.pop(),ie.trys.pop();continue}U=Me.call(q,ie)}catch(F){U=[6,F],Be=0}finally{Q=x=0}if(U[0]&5)throw U[1];return{value:U[0]?U[1]:void 0,done:!0}}},Ke=function(q){var Me,ie={anchor:0,cdn:P.g.CDN,customEmoji:{},emojiPath:"".concat(P.g.CDN,"/dist/images/emoji"),hljs:P.g.HLJS_OPTIONS,icon:"ant",lang:"zh_CN",markdown:P.g.MARKDOWN_OPTIONS,math:P.g.MATH_OPTIONS,mode:"light",speech:{enable:!1},render:{media:{enable:!0}},theme:P.g.THEME_OPTIONS};return q.cdn&&(!((Me=q.theme)===null||Me===void 0)&&Me.path||(ie.theme.path="".concat(q.cdn,"/dist/css/content-theme")),q.emojiPath||(ie.emojiPath="".concat(q.cdn,"/dist/images/emoji"))),(0,ye.T)(ie,q)},Ne=function(q,Me){var ie=Ke(Me);return(0,re.G)("".concat(ie.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){var Q=(0,Ae.X)({autoSpace:ie.markdown.autoSpace,gfmAutoLink:ie.markdown.gfmAutoLink,codeBlockPreview:ie.markdown.codeBlockPreview,emojiSite:ie.emojiPath,emojis:ie.customEmoji,fixTermTypo:ie.markdown.fixTermTypo,footnotes:ie.markdown.footnotes,headingAnchor:ie.anchor!==0,inlineMathDigit:ie.math.inlineDigit,lazyLoadImage:ie.lazyLoadImage,linkBase:ie.markdown.linkBase,linkPrefix:ie.markdown.linkPrefix,listStyle:ie.markdown.listStyle,mark:ie.markdown.mark,mathBlockPreview:ie.markdown.mathBlockPreview,paragraphBeginningSpace:ie.markdown.paragraphBeginningSpace,sanitize:ie.markdown.sanitize,toc:ie.markdown.toc});return Me?.renderers&&Q.SetJSRenderers({renderers:{Md2HTML:Me.renderers}}),Q.SetHeadingID(!0),Q.Md2HTML(q)})},I=function(q,Me,ie){return pe(void 0,void 0,void 0,function(){var Q,Be,x,N;return Re(this,function(B){switch(B.label){case 0:return Q=Ke(ie),[4,Ne(Me,Q)];case 1:if(Be=B.sent(),Q.transform&&(Be=Q.transform(Be)),q.innerHTML=Be,q.classList.add("vditor-reset"),Q.i18n)return[3,5];if(["de_DE","en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(Q.lang))return[3,2];throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");case 2:return x="vditorI18nScript",N=x+Q.lang,document.querySelectorAll('head script[id^="'.concat(x,'"]')).forEach(function(K){K.id!==N&&document.head.removeChild(K)}),[4,(0,re.G)("".concat(Q.cdn,"/dist/js/i18n/").concat(Q.lang,".js"),N)];case 3:B.sent(),B.label=4;case 4:return[3,6];case 5:window.VditorI18n=Q.i18n,B.label=6;case 6:return Q.icon?[4,(0,re.G)("".concat(Q.cdn,"/dist/js/icons/").concat(Q.icon,".js"),"vditorIconScript")]:[3,8];case 7:B.sent(),B.label=8;case 8:return(0,$.Z)(Q.theme.current,Q.theme.path),Q.anchor===1&&q.classList.add("vditor-reset--anchor"),(0,c.O)(q,Q.hljs),(0,E.s)(Q.hljs,q,Q.cdn),(0,O.H)(q,{cdn:Q.cdn,math:Q.math}),(0,A.i)(q,Q.cdn,Q.mode),(0,T.J)(q,Q.cdn,Q.mode),(0,f.K)(q,Q.cdn),(0,b.P)(q,Q.cdn),(0,g.v)(q,Q.cdn),(0,u.p)(q,Q.cdn,Q.mode),(0,R.P)(q,Q.cdn,Q.mode),(0,W.B)(q,Q.cdn),(0,D.Q)(q,Q.cdn),Q.render.media.enable&&(0,L.Y)(q),Q.speech.enable&&ge(q),Q.anchor!==0&&Se(Q.anchor),Q.after&&Q.after(),Q.lazyLoadImage&&y(q),q.addEventListener("click",function(K){var U=(0,ue.lG)(K.target,"SPAN");if(U&&(0,ue.fb)(U,"vditor-toc")){var F=q.querySelector("#"+U.getAttribute("data-target-id"));F&&window.scrollTo(window.scrollX,F.offsetTop);return}}),[2]}})})},dt=_(190),rn=_(580),an=(function(){function q(){}return q.adapterRender=k,q.previewImage=dt.E,q.codeRender=c.O,q.graphvizRender=g.v,q.highlightRender=E.s,q.mathRender=O.H,q.mermaidRender=A.i,q.SMILESRender=T.J,q.markmapRender=f.K,q.flowchartRender=b.P,q.chartRender=u.p,q.abcRender=D.Q,q.mindmapRender=R.P,q.plantumlRender=W.B,q.outlineRender=V.k,q.mediaRender=L.Y,q.speechRender=ge,q.lazyLoadImageRender=y,q.md2html=Ne,q.preview=I,q.setCodeTheme=rn.Y,q.setContentTheme=$.Z,q})();const st=an}),145:((M,m,_)=>{_.d(m,{H:()=>D,g:()=>k});var D="3.11.1",k=(function(){function u(){}return u.ZWSP="​",u.DROP_EDITOR="application/editor",u.MOBILE_WIDTH=520,u.CLASS_MENU_DISABLED="vditor-menu--disabled",u.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],u.CODE_THEME=["a11y-dark","agate","an-old-hope","androidstudio","arta","atom-one-dark","atom-one-dark-reasonable","base16/3024","base16/apathy","base16/apprentice","base16/ashes","base16/atelier-cave","base16/atelier-dune","base16/atelier-estuary","base16/atelier-forest","base16/atelier-heath","base16/atelier-lakeside","base16/atelier-plateau","base16/atelier-savanna","base16/atelier-seaside","base16/atelier-sulphurpool","base16/atlas","base16/bespin","base16/black-metal","base16/black-metal-bathory","base16/black-metal-burzum","base16/black-metal-dark-funeral","base16/black-metal-gorgoroth","base16/black-metal-immortal","base16/black-metal-khold","base16/black-metal-marduk","base16/black-metal-mayhem","base16/black-metal-nile","base16/black-metal-venom","base16/brewer","base16/bright","base16/brogrammer","base16/brush-trees-dark","base16/chalk","base16/circus","base16/classic-dark","base16/codeschool","base16/colors","base16/danqing","base16/darcula","base16/dark-violet","base16/darkmoss","base16/darktooth","base16/decaf","base16/default-dark","base16/dracula","base16/edge-dark","base16/eighties","base16/embers","base16/equilibrium-dark","base16/equilibrium-gray-dark","base16/espresso","base16/eva","base16/eva-dim","base16/flat","base16/framer","base16/gigavolt","base16/google-dark","base16/grayscale-dark","base16/green-screen","base16/gruvbox-dark-hard","base16/gruvbox-dark-medium","base16/gruvbox-dark-pale","base16/gruvbox-dark-soft","base16/hardcore","base16/harmonic16-dark","base16/heetch-dark","base16/helios","base16/hopscotch","base16/horizon-dark","base16/humanoid-dark","base16/ia-dark","base16/icy-dark","base16/ir-black","base16/isotope","base16/kimber","base16/london-tube","base16/macintosh","base16/marrakesh","base16/materia","base16/material","base16/material-darker","base16/material-palenight","base16/material-vivid","base16/mellow-purple","base16/mocha","base16/monokai","base16/nebula","base16/nord","base16/nova","base16/ocean","base16/oceanicnext","base16/onedark","base16/outrun-dark","base16/papercolor-dark","base16/paraiso","base16/pasque","base16/phd","base16/pico","base16/pop","base16/porple","base16/qualia","base16/railscasts","base16/rebecca","base16/ros-pine","base16/ros-pine-moon","base16/sandcastle","base16/seti-ui","base16/silk-dark","base16/snazzy","base16/solar-flare","base16/solarized-dark","base16/spacemacs","base16/summercamp","base16/summerfruit-dark","base16/synth-midnight-terminal-dark","base16/tango","base16/tender","base16/tomorrow-night","base16/twilight","base16/unikitty-dark","base16/vulcan","base16/windows-10","base16/windows-95","base16/windows-high-contrast","base16/windows-nt","base16/woodland","base16/xcode-dusk","base16/zenburn","codepen-embed","dark","devibeans","far","felipec","github-dark","github-dark-dimmed","gml","gradient-dark","hybrid","ir-black","isbl-editor-dark","kimbie-dark","lioshi","monokai","monokai-sublime","night-owl","nnfx-dark","nord","obsidian","panda-syntax-dark","paraiso-dark","pojoaque","qtcreator-dark","rainbow","shades-of-purple","srcery","stackoverflow-dark","sunburst","tomorrow-night-blue","tomorrow-night-bright","tokyo-night-dark","vs2015","xt256","ant-design","a11y-light","arduino-light","ascetic","atom-one-light","base16/atelier-cave-light","base16/atelier-dune-light","base16/atelier-estuary-light","base16/atelier-forest-light","base16/atelier-heath-light","base16/atelier-lakeside-light","base16/atelier-plateau-light","base16/atelier-savanna-light","base16/atelier-seaside-light","base16/atelier-sulphurpool-light","base16/brush-trees","base16/classic-light","base16/cupcake","base16/cupertino","base16/default-light","base16/dirtysea","base16/edge-light","base16/equilibrium-gray-light","base16/equilibrium-light","base16/fruit-soda","base16/github","base16/google-light","base16/grayscale-light","base16/gruvbox-light-hard","base16/gruvbox-light-medium","base16/gruvbox-light-soft","base16/harmonic16-light","base16/heetch-light","base16/humanoid-light","base16/horizon-light","base16/ia-light","base16/material-lighter","base16/mexico-light","base16/one-light","base16/papercolor-light","base16/ros-pine-dawn","base16/sagelight","base16/shapeshifter","base16/silk-light","base16/solar-flare-light","base16/solarized-light","base16/summerfruit-light","base16/synth-midnight-terminal-light","base16/tomorrow","base16/unikitty-light","base16/windows-10-light","base16/windows-95-light","base16/windows-high-contrast-light","brown-paper","base16/windows-nt-light","color-brewer","docco","foundation","github","googlecode","gradient-light","grayscale","idea","intellij-light","isbl-editor-light","kimbie-light","lightfair","magula","mono-blue","nnfx-light","panda-syntax-light","paraiso-light","purebasic","qtcreator-light","routeros","school-book","stackoverflow-light","tokyo-night-light","vs","xcode","default"],u.ALIAS_CODE_LANGUAGES=["abc","plantuml","mermaid","flowchart","echarts","mindmap","graphviz","math","markmap","smiles","js","ts","html","toml","c#","bat"],u.CDN="https://unpkg.com/vditor@".concat("3.11.1"),u.MARKDOWN_OPTIONS={autoSpace:!1,gfmAutoLink:!0,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",linkPrefix:"",listStyle:!1,mark:!1,mathBlockPreview:!0,paragraphBeginningSpace:!1,sanitize:!0,toc:!1},u.HLJS_OPTIONS={enable:!0,lineNumber:!1,defaultLang:"",style:"github"},u.MATH_OPTIONS={engine:"KaTeX",inlineDigit:!1,macros:{}},u.THEME_OPTIONS={current:"light",list:{"ant-design":"Ant Design",dark:"Dark",light:"Light",wechat:"WeChat"},path:"".concat(u.CDN,"/dist/css/content-theme")},u})()}),825:((M,m,_)=>{_.d(m,{J:()=>b});var D=_(145),k=_(413),u=_(840),c=_(494),b=function(g,E,y){g===void 0&&(g=document),E===void 0&&(E=D.g.CDN);var O=u.SMILESRenderAdapter.getElements(g);O.length>0&&(0,k.G)("".concat(E,"/dist/js/smiles-drawer/smiles-drawer.min.js?v=2.1.7"),"vditorAbcjsScript").then(function(){var L=new SmiDrawer({},{});O.forEach(function(A){var T=u.SMILESRenderAdapter.getCode(A).trim();if(!(A.getAttribute("data-processed")==="true"||T.trim()==="")){var f="smiles"+(0,c.Wb)();A.innerHTML='<svg id="'.concat(f,'"></svg>'),L.draw(T,"#"+f,y==="dark"?"dark":void 0),A.setAttribute("data-processed","true")}})})}}),135:((M,m,_)=>{_.d(m,{Q:()=>c});var D=_(145),k=_(413),u=_(840),c=function(b,g){b===void 0&&(b=document),g===void 0&&(g=D.g.CDN);var E=u.abcRenderAdapter.getElements(b);E.length>0&&(0,k.G)("".concat(g,"/dist/js/abcjs/abcjs_basic.min.js"),"vditorAbcjsScript").then(function(){E.forEach(function(y){y.parentElement.classList.contains("vditor-wysiwyg__pre")||y.parentElement.classList.contains("vditor-ir__marker--pre")||y.getAttribute("data-processed")!=="true"&&(ABCJS.renderAbc(y,u.abcRenderAdapter.getCode(y).trim()),y.style.overflowX="auto",y.setAttribute("data-processed","true"))})})}}),840:((M,m,_)=>{_.r(m),_.d(m,{mathRenderAdapter:()=>D,SMILESRenderAdapter:()=>k,mermaidRenderAdapter:()=>u,markmapRenderAdapter:()=>c,mindmapRenderAdapter:()=>b,chartRenderAdapter:()=>g,abcRenderAdapter:()=>E,graphvizRenderAdapter:()=>y,flowchartRenderAdapter:()=>O,plantumlRenderAdapter:()=>L});var D={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-math")}},k={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-smiles")}},u={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-mermaid")}},c={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-markmap")}},b={getCode:function(A){return A.getAttribute("data-code")},getElements:function(A){return A.querySelectorAll(".language-mindmap")}},g={getCode:function(A){return A.innerText},getElements:function(A){return A.querySelectorAll(".language-echarts")}},E={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-abc")}},y={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-graphviz")}},O={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-flowchart")}},L={getCode:function(A){return A.textContent},getElements:function(A){return A.querySelectorAll(".language-plantuml")}}}),775:((M,m,_)=>{_.d(m,{p:()=>E});var D=_(145),k=_(413),u=_(840),c=_(494),b=function(y,O,L,A){function T(f){return f instanceof L?f:new L(function(R){R(f)})}return new(L||(L=Promise))(function(f,R){function V($){try{P(A.next($))}catch(re){R(re)}}function W($){try{P(A.throw($))}catch(re){R(re)}}function P($){$.done?f($.value):T($.value).then(V,W)}P((A=A.apply(y,O||[])).next())})},g=function(y,O){var L={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},A,T,f,R;return R={next:V(0),throw:V(1),return:V(2)},typeof Symbol=="function"&&(R[Symbol.iterator]=function(){return this}),R;function V(P){return function($){return W([P,$])}}function W(P){if(A)throw new TypeError("Generator is already executing.");for(;R&&(R=0,P[0]&&(L=0)),L;)try{if(A=1,T&&(f=P[0]&2?T.return:P[0]?T.throw||((f=T.return)&&f.call(T),0):T.next)&&!(f=f.call(T,P[1])).done)return f;switch(T=0,f&&(P=[P[0]&2,f.value]),P[0]){case 0:case 1:f=P;break;case 4:return L.label++,{value:P[1],done:!1};case 5:L.label++,T=P[1],P=[0];continue;case 7:P=L.ops.pop(),L.trys.pop();continue;default:if(f=L.trys,!(f=f.length>0&&f[f.length-1])&&(P[0]===6||P[0]===2)){L=0;continue}if(P[0]===3&&(!f||P[1]>f[0]&&P[1]<f[3])){L.label=P[1];break}if(P[0]===6&&L.label<f[1]){L.label=f[1],f=P;break}if(f&&L.label<f[2]){L.label=f[2],L.ops.push(P);break}f[2]&&L.ops.pop(),L.trys.pop();continue}P=O.call(y,L)}catch($){P=[6,$],T=0}finally{A=f=0}if(P[0]&5)throw P[1];return{value:P[0]?P[1]:void 0,done:!0}}},E=function(y,O,L){y===void 0&&(y=document),O===void 0&&(O=D.g.CDN);var A=u.chartRenderAdapter.getElements(y);A.length>0&&(0,k.G)("".concat(O,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){A.forEach(function(T){return b(void 0,void 0,void 0,function(){var f,R,V;return g(this,function(W){switch(W.label){case 0:if(T.parentElement.classList.contains("vditor-wysiwyg__pre")||T.parentElement.classList.contains("vditor-ir__marker--pre"))return[2];if(f=u.chartRenderAdapter.getCode(T).trim(),!f)return[2];W.label=1;case 1:return W.trys.push([1,3,,4]),T.getAttribute("data-processed")==="true"?[2]:[4,(0,c.Qf)(f)];case 2:return R=W.sent(),echarts.init(T,L==="dark"?"dark":void 0).setOption(R),T.setAttribute("data-processed","true"),[3,4];case 3:return V=W.sent(),T.className="vditor-reset--error",T.innerHTML="echarts render error: <br>".concat(V),[3,4];case 4:return[2]}})})})})}}),428:((M,m,_)=>{_.d(m,{O:()=>u});var D=_(105),k=_(145),u=function(c,b){Array.from(c.querySelectorAll("pre > code")).filter(function(g,E){return!(g.parentElement.classList.contains("vditor-wysiwyg__pre")||g.parentElement.classList.contains("vditor-ir__marker--pre")||g.classList.contains("language-mermaid")||g.classList.contains("language-flowchart")||g.classList.contains("language-echarts")||g.classList.contains("language-mindmap")||g.classList.contains("language-plantuml")||g.classList.contains("language-markmap")||g.classList.contains("language-abc")||g.classList.contains("language-graphviz")||g.classList.contains("language-math")||g.classList.contains("language-smiles")||g.style.maxHeight.indexOf("px")>-1||c.classList.contains("vditor-preview")&&E>5)}).forEach(function(g){var E,y,O,L=g.innerText;if(g.classList.contains("highlight-chroma")){var A=g.cloneNode(!0);A.querySelectorAll(".highlight-ln").forEach(function(V){V.remove()}),L=A.innerText}else L.endsWith(`
`)&&(L=L.substr(0,L.length-1));var T='<svg><use xlink:href="#vditor-icon-copy"></use></svg>';document.getElementById("vditorIconScript")||(T='<svg viewBox="0 0 32 32"><path d="M22.545-0h-17.455c-1.6 0-2.909 1.309-2.909 2.909v20.364h2.909v-20.364h17.455v-2.909zM26.909 5.818h-16c-1.6 0-2.909 1.309-2.909 2.909v20.364c0 1.6 1.309 2.909 2.909 2.909h16c1.6 0 2.909-1.309 2.909-2.909v-20.364c0-1.6-1.309-2.909-2.909-2.909zM26.909 29.091h-16v-20.364h16v20.364z"></path></svg>');var f=document.createElement("div");f.className="vditor-copy",f.innerHTML='<span aria-label="'.concat(((E=window.VditorI18n)===null||E===void 0?void 0:E.copy)||"复制",`"
onmouseover="this.setAttribute('aria-label', '`).concat(((y=window.VditorI18n)===null||y===void 0?void 0:y.copy)||"复制",`')"
class="vditor-tooltipped vditor-tooltipped__w"
onclick="event.stopPropagation();this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '`).concat(((O=window.VditorI18n)===null||O===void 0?void 0:O.copied)||"已复制",`');this.previousElementSibling.blur()">`).concat(T,"</span>");var R=document.createElement("textarea");R.value=(0,D.X)(L),f.insertAdjacentElement("afterbegin",R),b&&b.renderMenu&&b.renderMenu(g,f),g.before(f),g.style.maxHeight=window.outerHeight-40+"px",g.insertAdjacentHTML("afterend",'<span style="position: absolute">'.concat(k.g.ZWSP,"</span>"))})}}),325:((M,m,_)=>{_.d(m,{P:()=>c});var D=_(145),k=_(413),u=_(840),c=function(b,g){g===void 0&&(g=D.g.CDN);var E=u.flowchartRenderAdapter.getElements(b);E.length!==0&&(0,k.G)("".concat(g,"/dist/js/flowchart.js/flowchart.min.js"),"vditorFlowchartScript").then(function(){E.forEach(function(y){if(y.getAttribute("data-processed")!=="true"){var O=flowchart.parse(u.flowchartRenderAdapter.getCode(y));y.innerHTML="",O.drawSVG(y),y.setAttribute("data-processed","true")}})})}}),483:((M,m,_)=>{_.d(m,{v:()=>c});var D=_(145),k=_(413),u=_(840),c=function(b,g){g===void 0&&(g=D.g.CDN);var E=u.graphvizRenderAdapter.getElements(b);E.length!==0&&(0,k.G)("".concat(g,"/dist/js/graphviz/viz.js"),"vditorGraphVizScript").then(function(){E.forEach(function(y){var O=u.graphvizRenderAdapter.getCode(y);if(!(y.parentElement.classList.contains("vditor-wysiwyg__pre")||y.parentElement.classList.contains("vditor-ir__marker--pre"))&&!(y.getAttribute("data-processed")==="true"||O.trim()==="")){try{var L=new Blob(["importScripts('".concat(document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js"),"');")],{type:"application/javascript"}),A=window.URL||window.webkitURL,T=A.createObjectURL(L),f=new Worker(T);new Viz({worker:f}).renderSVGElement(O).then(function(R){y.innerHTML=R.outerHTML}).catch(function(R){y.innerHTML="graphviz render error: <br>".concat(R),y.className="vditor-reset--error"})}catch(R){console.error("graphviz error",R)}y.setAttribute("data-processed","true")}})})}}),999:((M,m,_)=>{_.d(m,{s:()=>c});var D=_(145),k=_(413),u=_(290),c=function(b,g,E){g===void 0&&(g=document),E===void 0&&(E=D.g.CDN);var y=b.style;D.g.CODE_THEME.includes(y)||(y="github");var O=document.getElementById("vditorHljsStyle"),L="".concat(E,"/dist/js/highlight.js/styles/").concat(y,".min.css");if(O&&O.getAttribute("href")!==L&&O.remove(),(0,u.c)("".concat(E,"/dist/js/highlight.js/styles/").concat(y,".min.css"),"vditorHljsStyle"),b.enable!==!1){var A=g.querySelectorAll("pre > code");A.length!==0&&(0,k.G)("".concat(E,"/dist/js/highlight.js/highlight.min.js?v=11.7.0"),"vditorHljsScript").then(function(){(0,k.G)("".concat(E,"/dist/js/highlight.js/third-languages.js?v=1.0.1"),"vditorHljsThirdScript").then(function(){g.querySelectorAll("pre > code").forEach(function(T){if(!(T.parentElement.classList.contains("vditor-ir__marker--pre")||T.parentElement.classList.contains("vditor-wysiwyg__pre"))&&!(T.classList.contains("language-mermaid")||T.classList.contains("language-flowchart")||T.classList.contains("language-echarts")||T.classList.contains("language-mindmap")||T.classList.contains("language-plantuml")||T.classList.contains("language-smiles")||T.classList.contains("language-abc")||T.classList.contains("language-graphviz")||T.classList.contains("language-math"))){b.defaultLang!==""&&T.className.indexOf("language-")===-1&&T.classList.add("language-"+b.defaultLang);var f=T.className.replace("language-","");if(window.hljs.getLanguage(f)||(f="plaintext"),T.innerHTML=window.hljs.highlight(T.textContent,{language:f,ignoreIllegals:!0}).value,T.classList.add("hljs"),!!b.lineNumber){T.classList.add("vditor-linenumber");var R=T.querySelector(".vditor-linenumber__temp");R||(R=document.createElement("div"),R.className="vditor-linenumber__temp",T.insertAdjacentElement("beforeend",R));var V=getComputedStyle(T).whiteSpace,W=!1;(V==="pre-wrap"||V==="pre-line")&&(W=!0);var P="",$=T.textContent.split(/\r\n|\r|\n/g);$.pop(),$.map(function(re){var ue="";W&&(R.textContent=re||`
`,ue=' style="height:'.concat(R.getBoundingClientRect().height,'px"')),P+="<span".concat(ue,"></span>")}),R.style.display="none",P='<span class="vditor-linenumber__rows">'.concat(P,"</span>"),T.insertAdjacentHTML("beforeend",P)}}})})})}}}),11:((M,m,_)=>{_.d(m,{K:()=>E});var D=_(145),k=_(413),u=_(840),c={},b=function(y,O){var L=y.transform(O),A=Object.keys(L.features).filter(function(W){return!c[W]});A.forEach(function(W){c[W]=!0});var T=y.getAssets(A),f=T.styles,R=T.scripts,V=window.markmap;return f&&V.loadCSS(f),R&&V.loadJS(R),L},g=function(y,O){var L=window.markmap,A=L.Transformer,T=L.Markmap,f=L.deriveOptions;L.globalCSS;var R=new A;y.innerHTML='<svg style="width:100%"></svg>';var V=y.firstChild,W=T.create(V,null),P=b(R,O),$=P.root,re=P.frontmatter,ue=re?.markmap,ye=f(ue);W.setData($,ye),W.fit()},E=function(y,O){y===void 0&&(y=document),O===void 0&&(O=D.g.CDN);var L=u.markmapRenderAdapter.getElements(y);L.length!==0&&(0,k.G)("".concat(O,"/dist/js/markmap/markmap.min.js"),"vditorMarkerScript").then(function(){L.forEach(function(A){var T=u.markmapRenderAdapter.getCode(A);if(!(A.getAttribute("data-processed")==="true"||T.trim()==="")){var f=document.createElement("div");f.className="language-markmap",A.parentNode.appendChild(f),g(f,T),A.parentNode.childNodes[0].nodeName=="CODE"&&A.parentNode.removeChild(A.parentNode.childNodes[0])}})})}}),472:((M,m,_)=>{_.d(m,{H:()=>g});var D=_(145),k=_(413),u=_(290),c=_(105),b=_(840),g=function(E,y){E===void 0&&(E=document);var O=b.mathRenderAdapter.getElements(E);if(O.length!==0){var L={cdn:D.g.CDN,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};if(y&&y.math&&(y.math=Object.assign({},L.math,y.math)),y=Object.assign({},L,y),y.math.engine==="KaTeX")(0,u.c)("".concat(y.cdn,"/dist/js/katex/katex.min.css?v=0.16.9"),"vditorKatexStyle"),(0,k.G)("".concat(y.cdn,"/dist/js/katex/katex.min.js?v=0.16.9"),"vditorKatexScript").then(function(){(0,k.G)("".concat(y.cdn,"/dist/js/katex/mhchem.min.js?v=0.16.9"),"vditorKatexChemScript").then(function(){O.forEach(function(f){if(!(f.parentElement.classList.contains("vditor-wysiwyg__pre")||f.parentElement.classList.contains("vditor-ir__marker--pre"))&&!f.getAttribute("data-math")){var R=(0,c.X)(b.mathRenderAdapter.getCode(f));f.setAttribute("data-math",R);try{f.innerHTML=katex.renderToString(R,{displayMode:f.tagName==="DIV",output:"html",macros:y.math.macros})}catch(V){f.innerHTML=V.message,f.className="language-math vditor-reset--error"}f.addEventListener("copy",function(V){V.stopPropagation(),V.preventDefault();var W=V.currentTarget.closest(".language-math");V.clipboardData.setData("text/html",W.innerHTML),V.clipboardData.setData("text/plain",W.getAttribute("data-math"))})}})})});else if(y.math.engine==="MathJax"){var A=function(f){if(f.length!==0){var R=0,V=f[f.length-1],W=function(){var P=f[R++];P===V?P():P(W)};W()}};window.MathJax||(window.MathJax={loader:{paths:{mathjax:"".concat(y.cdn,"/dist/js/mathjax")}},startup:{typeset:!1},tex:{macros:y.math.macros}},Object.assign(window.MathJax,y.math.mathJaxOptions)),(0,k.J)("".concat(y.cdn,"/dist/js/mathjax/tex-svg-full.js"),"protyleMathJaxScript");var T=function(f,R){var V=(0,c.X)(f.textContent).trim(),W=window.MathJax.getMetricsFor(f);W.display=f.tagName==="DIV",window.MathJax.tex2svgPromise(V,W).then(function(P){f.innerHTML="",f.setAttribute("data-math",V),f.append(P),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var $=P.querySelector('[data-mml-node="merror"]');$&&$.textContent.trim()!==""&&(f.innerHTML=$.textContent.trim(),f.className="vditor-reset--error"),R&&R()})};window.MathJax.startup.promise.then(function(){for(var f=[],R=function(W){var P=O[W];!P.parentElement.classList.contains("vditor-wysiwyg__pre")&&!P.parentElement.classList.contains("vditor-ir__marker--pre")&&!P.getAttribute("data-math")&&(0,c.X)(P.textContent).trim()&&f.push(function($){W===O.length-1?T(P):T(P,$)})},V=0;V<O.length;V++)R(V);A(f)})}}}}),280:((M,m,_)=>{_.d(m,{Y:()=>b});var D=_(494),k=function(g,E){g.insertAdjacentHTML("afterend",'<video controls="controls" src="'.concat(E,'"></video>')),g.remove()},u=function(g,E){g.insertAdjacentHTML("afterend",'<audio controls="controls" src="'.concat(E,'"></audio>')),g.remove()},c=function(g,E){var y=E.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),O=E.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),L=E.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),A=E.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),T=E.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),f=E.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),R=E.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),V=E.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);if(y&&y[1].length===11)g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'.concat(y[1]+(y[2]?"?start="+y[2]:""),'"></iframe>')),g.remove();else if(O&&O[1])g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'.concat(O[1],'"></iframe>')),g.remove();else if(L&&L[1])g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='.concat(L[1],'"></iframe>')),g.remove();else if(A&&A[1])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="//coub.com/embed/`.concat(A[1],'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>')),g.remove();else if(T&&T[0])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.facebook.com/plugins/video.php?href=`.concat(encodeURIComponent(T[0]),'"></iframe>')),g.remove();else if(f&&f[2])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.dailymotion.com/embed/video/`.concat(f[2],'"></iframe>')),g.remove();else if(E.indexOf("bilibili.com")>-1&&(E.indexOf("bvid=")>-1||R&&R[1])){var W={bvid:(0,D.on)("bvid",E)||R&&R[1],page:"1",high_quality:"1",as_wide:"1",allowfullscreen:"true",autoplay:"0"};new URL(E.startsWith("http")?E:"https:"+E).search.split("&").forEach(function(re,ue){if(re){ue===0&&(re=re.substr(1));var ye=re.split("=");W[ye[0]]=ye[1]}});var P="https://player.bilibili.com/player.html?",$=Object.keys(W);$.forEach(function(re,ue){P+="".concat(re,"=").concat(W[re]),ue<$.length-1&&(P+="&")}),g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="'.concat(P,'"></iframe>')),g.remove()}else V&&V[1]&&(g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'.concat(V[1],'"></iframe>')),g.remove())},b=function(g){g&&g.querySelectorAll("a").forEach(function(E){var y=E.getAttribute("href");y&&(y.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?k(E,y):y.match(/^.+.(mp3|wav|flac)$/)?u(E,y):c(E,y))})}}),637:((M,m,_)=>{_.d(m,{i:()=>E});var D=_(145),k=_(413),u=_(840),c=_(494),b=function(y,O,L,A){function T(f){return f instanceof L?f:new L(function(R){R(f)})}return new(L||(L=Promise))(function(f,R){function V($){try{P(A.next($))}catch(re){R(re)}}function W($){try{P(A.throw($))}catch(re){R(re)}}function P($){$.done?f($.value):T($.value).then(V,W)}P((A=A.apply(y,O||[])).next())})},g=function(y,O){var L={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},A,T,f,R;return R={next:V(0),throw:V(1),return:V(2)},typeof Symbol=="function"&&(R[Symbol.iterator]=function(){return this}),R;function V(P){return function($){return W([P,$])}}function W(P){if(A)throw new TypeError("Generator is already executing.");for(;R&&(R=0,P[0]&&(L=0)),L;)try{if(A=1,T&&(f=P[0]&2?T.return:P[0]?T.throw||((f=T.return)&&f.call(T),0):T.next)&&!(f=f.call(T,P[1])).done)return f;switch(T=0,f&&(P=[P[0]&2,f.value]),P[0]){case 0:case 1:f=P;break;case 4:return L.label++,{value:P[1],done:!1};case 5:L.label++,T=P[1],P=[0];continue;case 7:P=L.ops.pop(),L.trys.pop();continue;default:if(f=L.trys,!(f=f.length>0&&f[f.length-1])&&(P[0]===6||P[0]===2)){L=0;continue}if(P[0]===3&&(!f||P[1]>f[0]&&P[1]<f[3])){L.label=P[1];break}if(P[0]===6&&L.label<f[1]){L.label=f[1],f=P;break}if(f&&L.label<f[2]){L.label=f[2],L.ops.push(P);break}f[2]&&L.ops.pop(),L.trys.pop();continue}P=O.call(y,L)}catch($){P=[6,$],T=0}finally{A=f=0}if(P[0]&5)throw P[1];return{value:P[0]?P[1]:void 0,done:!0}}},E=function(y,O,L){y===void 0&&(y=document),O===void 0&&(O=D.g.CDN);var A=u.mermaidRenderAdapter.getElements(y);A.length!==0&&(0,k.G)("".concat(O,"/dist/js/mermaid/mermaid.min.js?v=11.6.0"),"vditorMermaidScript").then(function(){var T={securityLevel:"loose",altFontFamily:"sans-serif",fontFamily:"sans-serif",startOnLoad:!1,flowchart:{htmlLabels:!0,useMaxWidth:!0},sequence:{useMaxWidth:!0,diagramMarginX:8,diagramMarginY:8,boxMargin:8,showSequenceNumbers:!0},gantt:{leftPadding:75,rightPadding:20}};L==="dark"&&(T.theme="dark"),mermaid.initialize(T),A.forEach(function(f){return b(void 0,void 0,void 0,function(){var R,V,W,P,$;return g(this,function(re){switch(re.label){case 0:if(R=u.mermaidRenderAdapter.getCode(f),f.getAttribute("data-processed")==="true"||R.trim()==="")return[2];V="mermaid"+(0,c.Wb)(),re.label=1;case 1:return re.trys.push([1,3,,4]),[4,mermaid.render(V,f.textContent)];case 2:return W=re.sent(),f.innerHTML=W.svg,[3,4];case 3:return P=re.sent(),$=document.querySelector("#"+V),f.innerHTML="".concat($.outerHTML,`<br>
<div style="text-align: left"><small>`).concat(P.message.replace(/\n/,"<br>"),"</small></div>"),$.parentElement.remove(),[3,4];case 4:return f.setAttribute("data-processed","true"),[2]}})})})})}}),194:((M,m,_)=>{_.d(m,{P:()=>c});var D=_(145),k=_(413),u=_(840),c=function(b,g,E){b===void 0&&(b=document),g===void 0&&(g=D.g.CDN);var y=u.mindmapRenderAdapter.getElements(b);y.length>0&&(0,k.G)("".concat(g,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){y.forEach(function(O){if(!(O.parentElement.classList.contains("vditor-wysiwyg__pre")||O.parentElement.classList.contains("vditor-ir__marker--pre"))){var L=u.mindmapRenderAdapter.getCode(O);if(L)try{if(O.getAttribute("data-processed")==="true")return;echarts.init(O,E==="dark"?"dark":void 0).setOption({series:[{data:[JSON.parse(decodeURIComponent(L))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(A,T){var f;return!((f=T?.data)===null||f===void 0)&&f.children?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}}),O.setAttribute("data-processed","true")}catch(A){O.className="vditor-reset--error",O.innerHTML="mindmap render error: <br>".concat(A)}}})})}}),436:((M,m,_)=>{_.d(m,{k:()=>u});var D=_(771),k=_(472),u=function(c,b,g){var E="",y=[];if(Array.from(c.children).forEach(function(T,f){if((0,D.W)(T)){if(g){var R=T.id.lastIndexOf("_");T.id=T.id.substring(0,R===-1?void 0:R)+"_"+f}y.push(T.id),E+=T.outerHTML.replace("<wbr>","")}}),E==="")return b.innerHTML="","";var O=document.createElement("div");if(g)g.lute.SetToC(!0),g.currentMode==="wysiwyg"&&!g.preview.element.contains(c)?O.innerHTML=g.lute.SpinVditorDOM("<p>[ToC]</p>"+E):g.currentMode==="ir"&&!g.preview.element.contains(c)?O.innerHTML=g.lute.SpinVditorIRDOM("<p>[ToC]</p>"+E):O.innerHTML=g.lute.HTML2VditorDOM("<p>[ToC]</p>"+E),g.lute.SetToC(g.options.preview.markdown.toc);else{b.classList.add("vditor-outline");var L=Lute.New();L.SetToC(!0),O.innerHTML=L.HTML2VditorDOM("<p>[ToC]</p>"+E)}var A=O.firstElementChild.querySelectorAll("li > span[data-target-id]");return A.forEach(function(T,f){if(T.nextElementSibling&&T.nextElementSibling.tagName==="UL"){var R="<svg class='vditor-outline__action'><use xlink:href='#vditor-icon-down'></use></svg>";document.getElementById("vditorIconScript")||(R='<svg class="vditor-outline__action" viewBox="0 0 32 32"><path d="M3.76 6.12l12.24 12.213 12.24-12.213 3.76 3.76-16 16-16-16 3.76-3.76z"></path></svg>'),T.innerHTML="".concat(R,"<span>").concat(T.innerHTML,"</span>")}else T.innerHTML="<svg></svg><span>".concat(T.innerHTML,"</span>");T.setAttribute("data-target-id",y[f])}),E=O.firstElementChild.innerHTML,A.length===0?(b.innerHTML="",E):(b.innerHTML=E,g&&(0,k.H)(b,{cdn:g.options.cdn,math:g.options.preview.math}),b.firstElementChild.addEventListener("click",function(T){for(var f=T.target;f&&!f.isEqualNode(b);){if(f.classList.contains("vditor-outline__action")){f.classList.contains("vditor-outline__action--close")?(f.classList.remove("vditor-outline__action--close"),f.parentElement.nextElementSibling.setAttribute("style","display:block")):(f.classList.add("vditor-outline__action--close"),f.parentElement.nextElementSibling.setAttribute("style","display:none")),T.preventDefault(),T.stopPropagation();break}else if(f.getAttribute("data-target-id")){T.preventDefault(),T.stopPropagation();var R=document.getElementById(f.getAttribute("data-target-id"));if(!R)return;if(g)if(g.options.height==="auto"){var V=R.offsetTop+g.element.offsetTop;g.options.toolbarConfig.pin||(V+=g.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,V)}else g.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,g.element.offsetTop),g.preview.element.contains(c)?c.parentElement.scrollTop=R.offsetTop:c.scrollTop=R.offsetTop;else window.scrollTo(window.scrollX,R.offsetTop);break}f=f.parentElement}}),E)}}),229:((M,m,_)=>{_.d(m,{B:()=>c});var D=_(145),k=_(413),u=_(840),c=function(b,g){b===void 0&&(b=document),g===void 0&&(g=D.g.CDN);var E=u.plantumlRenderAdapter.getElements(b);E.length!==0&&(0,k.G)("".concat(g,"/dist/js/plantuml/plantuml-encoder.min.js"),"vditorPlantumlScript").then(function(){E.forEach(function(y){if(!(y.parentElement.classList.contains("vditor-wysiwyg__pre")||y.parentElement.classList.contains("vditor-ir__marker--pre"))){var O=u.plantumlRenderAdapter.getCode(y).trim();if(O)try{y.innerHTML='<object type="image/svg+xml" data="https://www.plantuml.com/plantuml/svg/~1'.concat(plantumlEncoder.encode(O),'"/>')}catch(L){y.className="vditor-reset--error",y.innerHTML="plantuml render error: <br>".concat(L)}}})})}}),214:((M,m,_)=>{_.d(m,{X:()=>D});var D=function(k){var u=Lute.New();return u.PutEmojis(k.emojis),u.SetEmojiSite(k.emojiSite),u.SetHeadingAnchor(k.headingAnchor),u.SetInlineMathAllowDigitAfterOpenMarker(k.inlineMathDigit),u.SetAutoSpace(k.autoSpace),u.SetToC(k.toc),u.SetFootnotes(k.footnotes),u.SetFixTermTypo(k.fixTermTypo),u.SetVditorCodeBlockPreview(k.codeBlockPreview),u.SetVditorMathBlockPreview(k.mathBlockPreview),u.SetSanitize(k.sanitize),u.SetChineseParagraphBeginningSpace(k.paragraphBeginningSpace),u.SetRenderListStyle(k.listStyle),u.SetLinkBase(k.linkBase),u.SetLinkPrefix(k.linkPrefix),u.SetMark(k.mark),u.SetGFMAutoLink(k.gfmAutoLink),k.lazyLoadImage&&u.SetImageLazyLoading(k.lazyLoadImage),u}}),190:((M,m,_)=>{_.d(m,{E:()=>D});var D=function(k,u,c){c===void 0&&(c="classic");var b=k.getBoundingClientRect(),g=36;document.body.insertAdjacentHTML("beforeend",'<div class="vditor vditor-img'.concat(c==="dark"?" vditor--dark":"",`">
    <div class="vditor-img__bar">
      <span class="vditor-img__btn" data-deg="0">
        <svg><use xlink:href="#vditor-icon-redo"></use></svg>
        `).concat(window.VditorI18n.spin,`
      </span>
      <span class="vditor-img__btn"  onclick="this.parentElement.parentElement.outerHTML = '';document.body.style.overflow = ''">
        X &nbsp;`).concat(window.VditorI18n.close,`
      </span>
    </div>
    <div class="vditor-img__img" onclick="this.parentElement.outerHTML = '';document.body.style.overflow = ''">
      <img style="width: `).concat(k.width,"px;height:").concat(k.height,"px;transform: translate3d(").concat(b.left,"px, ").concat(b.top-g,'px, 0)" src="').concat(k.getAttribute("src"),`">
    </div>
</div>`)),document.body.style.overflow="hidden";var E=document.querySelector(".vditor-img img"),y="translate3d(".concat(Math.max(0,window.innerWidth-k.naturalWidth)/2,"px, ").concat(Math.max(0,window.innerHeight-g-k.naturalHeight)/2,"px, 0)");setTimeout(function(){E.setAttribute("style","transition: transform .3s ease-in-out;transform: ".concat(y)),setTimeout(function(){E.parentElement.scrollTo((E.parentElement.scrollWidth-E.parentElement.clientWidth)/2,(E.parentElement.scrollHeight-E.parentElement.clientHeight)/2)},400)});var O=document.querySelector(".vditor-img__btn");O.addEventListener("click",function(){var L=parseInt(O.getAttribute("data-deg"),10)+90;L/90%2===1&&k.naturalWidth>E.parentElement.clientHeight?E.style.transform="translate3d(".concat(Math.max(0,window.innerWidth-k.naturalWidth)/2,"px, ").concat(k.naturalWidth/2-k.naturalHeight/2,"px, 0) rotateZ(").concat(L,"deg)"):E.style.transform="".concat(y," rotateZ(").concat(L,"deg)"),O.setAttribute("data-deg",L.toString()),setTimeout(function(){E.parentElement.scrollTo((E.parentElement.scrollWidth-E.parentElement.clientWidth)/2,(E.parentElement.scrollHeight-E.parentElement.clientHeight)/2)},400)})}}),580:((M,m,_)=>{_.d(m,{Y:()=>u});var D=_(145),k=_(290),u=function(c,b){b===void 0&&(b=D.g.CDN),D.g.CODE_THEME.includes(c)||(c="github");var g=document.getElementById("vditorHljsStyle"),E="".concat(b,"/dist/js/highlight.js/styles/").concat(c,".min.css");g?g.getAttribute("href")!==E&&(g.remove(),(0,k.c)(E,"vditorHljsStyle")):(0,k.c)(E,"vditorHljsStyle")}}),538:((M,m,_)=>{_.d(m,{Z:()=>k});var D=_(290),k=function(u,c){if(!(!u||!c)){var b=document.getElementById("vditorContentTheme"),g="".concat(c,"/").concat(u,".css");b?b.getAttribute("href")!==g&&(b.remove(),(0,D.c)(g,"vditorContentTheme")):(0,D.c)(g,"vditorContentTheme")}}}),413:((M,m,_)=>{_.d(m,{J:()=>D,G:()=>k});var D=function(u,c){if(document.getElementById(c))return!1;var b=new XMLHttpRequest;b.open("GET",u,!1),b.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),b.send("");var g=document.createElement("script");g.type="text/javascript",g.text=b.responseText,g.id=c,document.head.appendChild(g)},k=function(u,c){return new Promise(function(b,g){if(document.getElementById(c))return b(!0),!1;var E=document.createElement("script");E.src=u,E.async=!0,document.head.appendChild(E),E.onerror=function(y){g(y)},E.onload=function(){if(document.getElementById(c))return E.remove(),b(!0),!1;E.id=c,b(!0)}})}}),290:((M,m,_)=>{_.d(m,{c:()=>D});var D=function(k,u){if(!document.getElementById(u)){var c=document.createElement("link");c.id=u,c.rel="stylesheet",c.type="text/css",c.href=k,document.getElementsByTagName("head")[0].appendChild(c)}}}),105:((M,m,_)=>{_.d(m,{X:()=>D});var D=function(k){return k.replace(/\u00a0/g," ")}}),410:((M,m,_)=>{_.d(m,{G6:()=>D,vU:()=>k,pK:()=>u,Le:()=>c,yl:()=>b,ns:()=>g,i7:()=>E});var D=function(){return navigator.userAgent.indexOf("Safari")>-1&&navigator.userAgent.indexOf("Chrome")===-1},k=function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1},u=function(){try{return typeof localStorage<"u"}catch{return!1}},c=function(){return navigator.userAgent.indexOf("iPhone")>-1?"touchstart":"click"},b=function(y){return navigator.platform.toUpperCase().indexOf("MAC")>=0?!!(y.metaKey&&!y.ctrlKey):!!(!y.metaKey&&y.ctrlKey)},g=function(y){return/Mac/.test(navigator.platform)||navigator.platform==="iPhone"?y.indexOf("⇧")>-1&&k()&&(y=y.replace(";",":").replace("=","+").replace("-","_")):(y.startsWith("⌘")?y=y.replace("⌘","⌘+"):y.startsWith("⌥")&&y.substr(1,1)!=="⌘"?y=y.replace("⌥","⌥+"):y=y.replace("⇧⌘","⌘+⇧+").replace("⌥⌘","⌥+⌘+"),y=y.replace("⌘","Ctrl").replace("⇧","Shift").replace("⌥","Alt"),y.indexOf("Shift")>-1&&(y=y.replace(";",":").replace("=","+").replace("-","_"))),y},E=function(){return/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor)}}),494:((M,m,_)=>{_.d(m,{Wb:()=>D,on:()=>k,Qf:()=>u});var D=function(){return([1e7].toString()+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,function(c){return(parseInt(c,10)^window.crypto.getRandomValues(new Uint32Array(1))[0]&15>>parseInt(c,10)/4).toString(16)})},k=function(c,b){b===void 0&&(b=window.location.search);var g=b.substring(b.indexOf("?")),E=g.indexOf("#"),y=new URLSearchParams(g.substring(0,E>=0?E:void 0));return y.get(c)},u=function(c){return Function('"use strict";return ('.concat(c,")"))()}}),106:((M,m,_)=>{_.d(m,{JQ:()=>k,E2:()=>u,O9:()=>c,a1:()=>b,F9:()=>g,lG:()=>E,fb:()=>y,DX:()=>O});var D=_(771),k=function(L,A){for(var T=y(L,A),f=!1,R=!1;T&&!T.classList.contains("vditor-reset")&&!R;)f=y(T.parentElement,A),f?T=f:R=!0;return T||!1},u=function(L,A){for(var T=(0,D.S)(L,A),f=!1,R=!1;T&&!T.classList.contains("vditor-reset")&&!R;)f=(0,D.S)(T.parentElement,A),f?T=f:R=!0;return T||!1},c=function(L){var A=u(L,"UL"),T=u(L,"OL"),f=A;return T&&(!A||A&&T.contains(A))&&(f=T),f},b=function(L,A,T){if(!L)return!1;L.nodeType===3&&(L=L.parentElement);for(var f=L,R=!1;f&&!R&&!f.classList.contains("vditor-reset");)f.getAttribute(A)===T?R=!0:f=f.parentElement;return R&&f},g=function(L){if(!L)return!1;L.nodeType===3&&(L=L.parentElement);var A=L,T=!1,f=b(L,"data-block","0");if(f)return f;for(;A&&!T&&!A.classList.contains("vditor-reset");)A.tagName==="H1"||A.tagName==="H2"||A.tagName==="H3"||A.tagName==="H4"||A.tagName==="H5"||A.tagName==="H6"||A.tagName==="P"||A.tagName==="BLOCKQUOTE"||A.tagName==="OL"||A.tagName==="UL"?T=!0:A=A.parentElement;return T&&A},E=function(L,A){if(!L)return!1;L.nodeType===3&&(L=L.parentElement);for(var T=L,f=!1;T&&!f&&!T.classList.contains("vditor-reset");)T.nodeName===A?f=!0:T=T.parentElement;return f&&T},y=function(L,A){if(!L)return!1;L.nodeType===3&&(L=L.parentElement);for(var T=L,f=!1;T&&!f&&!T.classList.contains("vditor-reset");)T.classList.contains(A)?f=!0:T=T.parentElement;return f&&T},O=function(L){for(;L&&L.lastChild;)L=L.lastChild;return L}}),771:((M,m,_)=>{_.d(m,{S:()=>D,W:()=>k});var D=function(u,c){if(!u)return!1;u.nodeType===3&&(u=u.parentElement);for(var b=u,g=!1;b&&!g&&!b.classList.contains("vditor-reset");)b.nodeName.indexOf(c)===0?g=!0:b=b.parentElement;return g&&b},k=function(u){var c=D(u,"H");return c&&c.tagName.length===2&&c.tagName!=="HR"?c:!1}}),673:((M,m,_)=>{_.d(m,{T:()=>D});var D=function(){for(var k=[],u=0;u<arguments.length;u++)k[u]=arguments[u];for(var c={},b=function(E){for(var y in E)E.hasOwnProperty(y)&&(Object.prototype.toString.call(E[y])==="[object Object]"?c[y]=D(c[y],E[y]):c[y]=E[y])},g=0;g<k.length;g++)b(k[g]);return c}}),810:((M,m,_)=>{_.d(m,{zh:()=>c,Ny:()=>b,Gb:()=>g,Hc:()=>E,im:()=>y,$j:()=>O,ib:()=>L,oC:()=>A});var D=_(145),k=_(410),u=_(106),c=function(T){var f,R=T[T.currentMode].element;return getSelection().rangeCount>0&&(f=getSelection().getRangeAt(0),R.isEqualNode(f.startContainer)||R.contains(f.startContainer))?f:T[T.currentMode].range?T[T.currentMode].range:(R.focus(),f=R.ownerDocument.createRange(),f.setStart(R,0),f.collapse(!0),f)},b=function(T){var f=window.getSelection().getRangeAt(0);if(!T.contains(f.startContainer)&&!(0,u.fb)(f.startContainer,"vditor-panel--none"))return{left:0,top:0};var R=T.parentElement.getBoundingClientRect(),V;if(f.getClientRects().length===0)if(f.startContainer.nodeType===3){var W=f.startContainer.parentElement;if(W&&W.getClientRects().length>0)V=W.getClientRects()[0];else return{left:0,top:0}}else{var P=f.startContainer.children;if(P[f.startOffset]&&P[f.startOffset].getClientRects().length>0)V=P[f.startOffset].getClientRects()[0];else if(f.startContainer.childNodes.length>0){var $=f.cloneRange();f.selectNode(f.startContainer.childNodes[Math.max(0,f.startOffset-1)]),V=f.getClientRects()[0],f.setEnd($.endContainer,$.endOffset),f.setStart($.startContainer,$.startOffset)}else V=f.startContainer.getClientRects()[0];if(!V){for(var re=f.startContainer.childNodes[f.startOffset];!re.getClientRects||re.getClientRects&&re.getClientRects().length===0;)re=re.parentElement;V=re.getClientRects()[0]}}else V=f.getClientRects()[0];return{left:V.left-R.left,top:V.top-R.top}},g=function(T,f){if(!f){if(getSelection().rangeCount===0)return!1;f=getSelection().getRangeAt(0)}var R=f.commonAncestorContainer;return T.isEqualNode(R)||T.contains(R)},E=function(T){var f=window.getSelection();f.removeAllRanges(),f.addRange(T)},y=function(T,f,R){var V={end:0,start:0};if(!R){if(getSelection().rangeCount===0)return V;R=window.getSelection().getRangeAt(0)}if(g(f,R)){var W=R.cloneRange();T.childNodes[0]&&T.childNodes[0].childNodes[0]?W.setStart(T.childNodes[0].childNodes[0],0):W.selectNodeContents(T),W.setEnd(R.startContainer,R.startOffset),V.start=W.toString().length,V.end=V.start+R.toString().length}return V},O=function(T,f,R){var V=0,W=0,P=R.childNodes[W],$=!1,re=!1;T=Math.max(0,T),f=Math.max(0,f);var ue=R.ownerDocument.createRange();for(ue.setStart(P||R,0),ue.collapse(!0);!re&&P;){var ye=V+P.textContent.length;if(!$&&T>=V&&T<=ye&&(T===0?ue.setStart(P,0):P.childNodes[0].nodeType===3?ue.setStart(P.childNodes[0],T-V):P.nextSibling?ue.setStartBefore(P.nextSibling):ue.setStartAfter(P),$=!0,T===f)){re=!0;break}$&&f>=V&&f<=ye&&(f===0?ue.setEnd(P,0):P.childNodes[0].nodeType===3?ue.setEnd(P.childNodes[0],f-V):P.nextSibling?ue.setEndBefore(P.nextSibling):ue.setEndAfter(P),re=!0),V=ye,P=R.childNodes[++W]}return!re&&R.childNodes[W-1]&&ue.setStartBefore(R.childNodes[W-1]),E(ue),ue},L=function(T,f){var R=T.querySelector("wbr");if(R){if(!R.previousElementSibling)R.previousSibling?f.setStart(R.previousSibling,R.previousSibling.textContent.length):R.nextSibling?R.nextSibling.nodeType===3?f.setStart(R.nextSibling,0):f.setStartBefore(R.nextSibling):f.setStart(R.parentElement,0);else if(R.previousElementSibling.isSameNode(R.previousSibling))if(R.previousElementSibling.lastChild){f.setStartBefore(R),f.collapse(!0),E(f),(0,k.i7)()&&(R.previousElementSibling.tagName==="EM"||R.previousElementSibling.tagName==="STRONG"||R.previousElementSibling.tagName==="S")&&(f.insertNode(document.createTextNode(D.g.ZWSP)),f.collapse(!1)),R.remove();return}else f.setStartAfter(R.previousElementSibling);else f.setStart(R.previousSibling,R.previousSibling.textContent.length);f.collapse(!0),R.remove(),E(f)}},A=function(T,f){var R=document.createElement("div");R.innerHTML=T;var V=R.querySelectorAll("p");V.length===1&&!V[0].previousSibling&&!V[0].nextSibling&&f[f.currentMode].element.children.length>0&&R.firstElementChild.tagName==="P"&&(T=V[0].innerHTML.trim());var W=document.createElement("div");W.innerHTML=T;var P=c(f);if(P.toString()!==""&&(f[f.currentMode].preventInput=!0,document.execCommand("delete",!1,"")),W.firstElementChild&&W.firstElementChild.getAttribute("data-block")==="0"){W.lastElementChild.insertAdjacentHTML("beforeend","<wbr>");var $=(0,u.F9)(P.startContainer);$?$.insertAdjacentHTML("afterend",W.innerHTML):f[f.currentMode].element.insertAdjacentHTML("beforeend",W.innerHTML),L(f[f.currentMode].element,P)}else{var re=document.createElement("template");re.innerHTML=T,P.insertNode(re.content.cloneNode(!0)),P.collapse(!1),E(P)}}})},p={};function h(M){var m=p[M];if(m!==void 0)return m.exports;var _=p[M]={exports:{}};return l[M](_,_.exports,h),_.exports}h.d=(M,m)=>{for(var _ in m)h.o(m,_)&&!h.o(M,_)&&Object.defineProperty(M,_,{enumerable:!0,get:m[_]})},h.o=(M,m)=>Object.prototype.hasOwnProperty.call(M,m),h.r=M=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(M,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(M,"__esModule",{value:!0})};var S={};return(()=>{h.d(S,{default:()=>xl});var M=h(408),m=h(145),_=h(105),D=function(e){return e.currentMode==="sv"?(0,_.X)("".concat(e.sv.element.textContent,`
`).replace(/\n\n$/,`
`)):e.currentMode==="wysiwyg"?e.lute.VditorDOM2Md(e.wysiwyg.element.innerHTML):e.currentMode==="ir"?e.lute.VditorIRDOM2Md(e.ir.element.innerHTML):""},k=h(413),u=(function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-devtools",this.element.innerHTML='<div class="vditor-reset--error"></div><div style="height: 100%;"></div>'}return e.prototype.renderEchart=function(t){var n=this;t.devtools.element.style.display==="block"&&(0,k.G)("".concat(t.options.cdn,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){n.ASTChart||(n.ASTChart=echarts.init(t.devtools.element.lastElementChild));try{n.element.lastElementChild.style.display="block",n.element.firstElementChild.innerHTML="",n.ASTChart.setOption({series:[{data:JSON.parse(t.lute.RenderEChartsJSON(D(t))),initialTreeDepth:-1,label:{align:"left",backgroundColor:"rgba(68, 77, 86, .68)",borderRadius:3,color:"#d1d5da",fontSize:12,lineHeight:12,offset:[9,12],padding:[2,4,2,4],position:"top",verticalAlign:"middle"},lineStyle:{color:"#4285f4",type:"curve",width:1},orient:"vertical",roam:!0,type:"tree"}],toolbox:{bottom:25,emphasis:{iconStyle:{color:"#4285f4"}},feature:{restore:{show:!0},saveAsImage:{show:!0}},right:15,show:!0}}),n.ASTChart.resize()}catch(i){n.element.lastElementChild.style.display="none",n.element.firstElementChild.innerHTML=i}})},e})(),c=h(410),b=function(e,t){t.forEach(function(n){if(e[n]){var i=e[n].children[0];i&&i.classList.contains("vditor-menu--current")&&i.classList.remove("vditor-menu--current")}})},g=function(e,t){t.forEach(function(n){if(e[n]){var i=e[n].children[0];i&&!i.classList.contains("vditor-menu--current")&&i.classList.add("vditor-menu--current")}})},E=function(e,t){t.forEach(function(n){if(e[n]){var i=e[n].children[0];i&&i.classList.contains(m.g.CLASS_MENU_DISABLED)&&i.classList.remove(m.g.CLASS_MENU_DISABLED)}})},y=function(e,t){t.forEach(function(n){if(e[n]){var i=e[n].children[0];i&&!i.classList.contains(m.g.CLASS_MENU_DISABLED)&&i.classList.add(m.g.CLASS_MENU_DISABLED)}})},O=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="none")})},L=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="block")})},A=function(e,t,n){t.includes("subToolbar")&&(e.toolbar.element.querySelectorAll(".vditor-hint").forEach(function(i){n&&i.isEqualNode(n)||(i.style.display="none")}),e.toolbar.elements.emoji&&(e.toolbar.elements.emoji.lastElementChild.style.display="none")),t.includes("hint")&&(e.hint.element.style.display="none"),e.wysiwyg.popover&&t.includes("popover")&&(e.wysiwyg.popover.style.display="none")},T=function(e,t,n,i){n.addEventListener((0,c.Le)(),function(a){a.preventDefault(),a.stopPropagation(),!n.classList.contains(m.g.CLASS_MENU_DISABLED)&&(e.toolbar.element.querySelectorAll(".vditor-hint--current").forEach(function(o){o.classList.remove("vditor-hint--current")}),t.style.display==="block"?t.style.display="none":(A(e,["subToolbar","hint","popover"],n.parentElement.parentElement),n.classList.contains("vditor-tooltipped")||n.classList.add("vditor-hint--current"),t.style.display="block",e.toolbar.element.getBoundingClientRect().right-n.getBoundingClientRect().right<250?t.classList.add("vditor-panel--left"):t.classList.remove("vditor-panel--left")))})},f=h(106),R=h(771),V=function(e,t,n,i){i&&console.log("".concat(e," - ").concat(n,": ").concat(t))},W=h(135),P=h(775),$=h(428),re=h(325),ue=h(483),ye=h(999),Se=h(472),Ae=h(637),Ce=h(11),ge=h(194),pe=h(229),Re=h(825),Ke=function(e,t,n){n===void 0&&(n="sv");var i=document.createElement("div");i.innerHTML=e;var a=!1;i.childElementCount===1&&i.lastElementChild.style.fontFamily.indexOf("monospace")>-1&&(a=!0);var o=i.querySelectorAll("pre");if(i.childElementCount===1&&o.length===1&&o[0].className!=="vditor-wysiwyg"&&o[0].className!=="vditor-sv"&&(a=!0),e.indexOf(`
<p class="p1">`)===0&&(a=!0),i.childElementCount===1&&i.firstElementChild.tagName==="TABLE"&&i.querySelector(".line-number")&&i.querySelector(".line-content")&&(a=!0),a){var d=t||e;return/\n/.test(d)||o.length===1?n==="wysiwyg"?'<div class="vditor-wysiwyg__block" data-block="0" data-type="code-block"><pre><code>'.concat(d.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"<wbr></code></pre></div>"):"\n```\n"+d.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"\n```":n==="wysiwyg"?"<code>".concat(d.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"</code><wbr>"):"`".concat(d,"`")}return!1},Ne=function(e,t){if(e){if(e.parentElement.getAttribute("data-type")==="html-block"){e.setAttribute("data-render","1");return}var n=e.firstElementChild.className.replace("language-","");if(n==="abc")(0,W.Q)(e,t.options.cdn);else if(n==="mermaid")(0,Ae.i)(e,t.options.cdn,t.options.theme);else if(n==="smiles")(0,Re.J)(e,t.options.cdn,t.options.theme);else if(n==="markmap")(0,Ce.K)(e,t.options.cdn);else if(n==="flowchart")(0,re.P)(e,t.options.cdn);else if(n==="echarts")(0,P.p)(e,t.options.cdn,t.options.theme);else if(n==="mindmap")(0,ge.P)(e,t.options.cdn,t.options.theme);else if(n==="plantuml")(0,pe.B)(e,t.options.cdn);else if(n==="graphviz")(0,ue.v)(e,t.options.cdn);else if(n==="math")(0,Se.H)(e,{cdn:t.options.cdn,math:t.options.preview.math});else{var i=t.options.customRenders.find(function(a){if(a.language===n)return a.render(e,t),!0});i||((0,ye.s)(Object.assign({},t.options.preview.hljs),e,t.options.cdn),(0,$.O)(e,t.options.preview.hljs))}e.setAttribute("data-render","1")}},I=h(810),dt=function(e){if(e.currentMode!=="sv"){var t=e[e.currentMode].element,n=e.outline.render(e);n===""&&(n="[ToC]"),t.querySelectorAll('[data-type="toc-block"]').forEach(function(i){i.innerHTML=n,(0,Se.H)(i,{cdn:e.options.cdn,math:e.options.preview.math})})}},rn=function(e,t){var n=(0,f.lG)(e.target,"SPAN");if(n&&(0,f.fb)(n,"vditor-toc")){var i=t[t.currentMode].element.querySelector("#"+n.getAttribute("data-target-id"));if(i)if(t.options.height==="auto"){var a=i.offsetTop+t.element.offsetTop;t.options.toolbarConfig.pin||(a+=t.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,a)}else t.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,t.element.offsetTop),t[t.currentMode].element.scrollTop=i.offsetTop;return}},an=function(e,t,n,i){if(e.previousElementSibling&&e.previousElementSibling.classList.contains("vditor-toc")){if(n.key==="Backspace"&&(0,I.im)(e,t[t.currentMode].element,i).start===0)return e.previousElementSibling.remove(),Ee(t),!0;if(un(t,n,i,e,e.previousElementSibling))return!0}if(e.nextElementSibling&&e.nextElementSibling.classList.contains("vditor-toc")){if(n.key==="Delete"&&(0,I.im)(e,t[t.currentMode].element,i).start>=e.textContent.trimRight().length)return e.nextElementSibling.remove(),Ee(t),!0;if(Tn(t,n,i,e,e.nextElementSibling))return!0}if(n.key==="Backspace"||n.key==="Delete"){var a=(0,f.fb)(i.startContainer,"vditor-toc");if(a)return a.remove(),Ee(t),!0}},st=function(e,t,n,i){n===void 0&&(n=!1);var a=(0,f.F9)(t.startContainer);if(a&&!n&&a.getAttribute("data-type")!=="code-block"){if(Br(a.innerHTML)&&a.previousElementSibling||Ur(a.innerHTML))return;for(var o=(0,I.im)(a,e.ir.element,t).start,d=!0,w=o-1;w>a.textContent.substr(0,o).lastIndexOf(`
`);w--)if(a.textContent.charAt(w)!==" "&&a.textContent.charAt(w)!=="	"){d=!1;break}o===0&&(d=!1);for(var C=!0,w=o-1;w<a.textContent.length;w++)if(a.textContent.charAt(w)!==" "&&a.textContent.charAt(w)!==`
`){C=!1;break}if(d){typeof e.options.input=="function"&&e.options.input(D(e));return}if(C&&/^#{1,6} $/.test(a.textContent)&&(C=!1),C){var v=(0,f.fb)(t.startContainer,"vditor-ir__marker");if(!v){var H=t.startContainer.previousSibling;H&&H.nodeType!==3&&H.classList.contains("vditor-ir__node--expand")&&H.classList.remove("vditor-ir__node--expand"),typeof e.options.input=="function"&&e.options.input(D(e));return}}}if(e.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function(we){we.classList.remove("vditor-ir__node--expand")}),a||(a=e.ir.element),!a.querySelector("wbr")){var j=(0,f.fb)(t.startContainer,"vditor-ir__preview");j?j.previousElementSibling.insertAdjacentHTML("beforeend","<wbr>"):t.insertNode(document.createElement("wbr"))}a.querySelectorAll("[style]").forEach(function(we){we.removeAttribute("style")}),a.getAttribute("data-type")==="link-ref-defs-block"&&(a=e.ir.element);var Z=a.isEqualNode(e.ir.element),X=(0,f.a1)(a,"data-type","footnotes-block"),G="";if(Z)G=a.innerHTML;else{var ce=(0,R.S)(t.startContainer,"BLOCKQUOTE"),ne=(0,f.O9)(t.startContainer);if(ne&&(a=ne),ce&&(!ne||ne&&!ce.contains(ne))&&(a=ce),X&&(a=X),G=a.outerHTML,a.tagName==="UL"||a.tagName==="OL"){var z=a.previousElementSibling,de=a.nextElementSibling;z&&(z.tagName==="UL"||z.tagName==="OL")&&(G=z.outerHTML+G,z.remove()),de&&(de.tagName==="UL"||de.tagName==="OL")&&(G=G+de.outerHTML,de.remove()),G=G.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}else a.previousElementSibling&&a.previousElementSibling.textContent.replace(m.g.ZWSP,"")!==""&&i&&i.inputType==="insertParagraph"&&(G=a.previousElementSibling.outerHTML+G,a.previousElementSibling.remove());a.innerText.startsWith("```")||(e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(we){we&&!a.isEqualNode(we)&&(G+=we.outerHTML,we.remove())}),e.ir.element.querySelectorAll("[data-type='footnotes-block']").forEach(function(we){we&&!a.isEqualNode(we)&&(G+=we.outerHTML,we.remove())}))}if(V("SpinVditorIRDOM",G,"argument",e.options.debugger),G=e.lute.SpinVditorIRDOM(G),V("SpinVditorIRDOM",G,"result",e.options.debugger),Z)a.innerHTML=G;else if(a.outerHTML=G,X){var oe=(0,f.a1)(e.ir.element.querySelector("wbr"),"data-type","footnotes-def");if(oe){var me=oe.textContent,Ie=me.substring(1,me.indexOf("]:")),De=e.ir.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(Ie,'"]'));De&&De.setAttribute("aria-label",me.substr(Ie.length+3).trim().substr(0,24))}}var Pe,lt=e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']");lt.forEach(function(we,Fe){Fe===0?Pe=we:(Pe.insertAdjacentHTML("beforeend",we.innerHTML),we.remove())}),lt.length>0&&e.ir.element.insertAdjacentElement("beforeend",lt[0]);var Xe,ct=e.ir.element.querySelectorAll("[data-type='footnotes-block']");ct.forEach(function(we,Fe){Fe===0?Xe=we:(Xe.insertAdjacentHTML("beforeend",we.innerHTML),we.remove())}),ct.length>0&&e.ir.element.insertAdjacentElement("beforeend",ct[0]),(0,I.ib)(e.ir.element,t),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(we){Ne(we,e)}),dt(e),Rt(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},q=function(e,t){if(e==="")return!1;if(e.indexOf("⇧")===-1&&e.indexOf("⌘")===-1&&e.indexOf("⌥")===-1)return!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&t.code===e;if(e==="⇧Tab")return!!(!(0,c.yl)(t)&&!t.altKey&&t.shiftKey&&t.code==="Tab");var n=e.split("");if(e.startsWith("⌥")){var i=n.length===3?n[2]:n[1];return!!((n.length===3?(0,c.yl)(t):!(0,c.yl)(t))&&t.altKey&&!t.shiftKey&&t.code===(/^[0-9]$/.test(i)?"Digit":"Key")+i)}e==="⌘Enter"&&(n=["⌘","Enter"]);var a=n.length>2&&n[0]==="⇧",o=a?n[2]:n[1];return a&&((0,c.vU)()||!/Mac/.test(navigator.platform))&&(o==="-"?o="_":o==="="&&(o="+")),!!((0,c.yl)(t)&&t.key.toLowerCase()===o.toLowerCase()&&!t.altKey&&(!a&&!t.shiftKey||a&&t.shiftKey))},Me=function(e){var t=e.startContainer;if(t.nodeType===3&&t.nodeValue.length!==e.startOffset)return!1;for(var n=t.nextSibling;n&&n.textContent==="";)n=n.nextSibling;if(n){if(n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block"))return n}else{var i=(0,f.fb)(t,"vditor-ir__marker");if(i&&!i.nextSibling){var a=t.parentElement.parentElement.nextSibling;if(a&&a.nodeType!==3&&a.classList.contains("vditor-ir__node"))return a}return!1}return!1},ie=function(e){var t=e.startContainer,n=t.previousSibling;return t.nodeType===3&&e.startOffset===0&&n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block")?n:!1},Q=function(e,t){t.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function(d){d.classList.remove("vditor-ir__node--expand")});var n=(0,f.JQ)(e.startContainer,"vditor-ir__node"),i=!e.collapsed&&(0,f.JQ)(e.endContainer,"vditor-ir__node");if(!(!e.collapsed&&(!n||n!==i))){n&&(n.classList.add("vditor-ir__node--expand"),n.classList.remove("vditor-ir__node--hidden"),(0,I.Hc)(e));var a=Me(e);if(a){a.classList.add("vditor-ir__node--expand"),a.classList.remove("vditor-ir__node--hidden");return}var o=ie(e);if(o){o.classList.add("vditor-ir__node--expand"),o.classList.remove("vditor-ir__node--hidden");return}}},Be=function(e,t){if(e.ir.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,I.zh)(e),i=n.startContainer;if(!zi(t,e,i)||(Gi(n,e,t),la(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var a=(0,f.a1)(i,"data-newline","1");if(!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&t.key==="Enter"&&a&&n.startOffset<a.textContent.length){var o=a.previousElementSibling;o&&(n.insertNode(document.createTextNode(o.textContent)),n.collapse(!1));var d=a.nextSibling;d&&(n.insertNode(document.createTextNode(d.textContent)),n.collapse(!0))}var w=(0,f.lG)(i,"P");if(Yi(t,e,w,n)||Xi(n,e,w,t)||aa(e,n,t,w))return!0;var C=(0,f.fb)(i,"vditor-ir__marker--pre");if(C&&C.tagName==="PRE"){var v=C.firstChild;if(ia(e,t,C,n)||(v.getAttribute("data-type")==="math-block"||v.getAttribute("data-type")==="html-block")&&un(e,t,n,v,C.parentElement)||Tn(e,t,n,v,C.parentElement))return!0}var H=(0,f.a1)(i,"data-type","code-block-info");if(H){if(t.key==="Enter"||t.key==="Tab")return n.selectNodeContents(H.nextElementSibling.firstChild),n.collapse(!0),t.preventDefault(),A(e,["hint"]),!0;if(t.key==="Backspace"){var j=(0,I.im)(H,e.ir.element).start;j===1&&n.setStart(i,0),j===2&&(e.hint.recentLanguage="")}if(un(e,t,n,H,H.parentElement))return A(e,["hint"]),!0}var Z=(0,f.lG)(i,"TD")||(0,f.lG)(i,"TH");if(t.key.indexOf("Arrow")>-1&&Z){var X=ho(Z);if(X&&un(e,t,n,Z,X))return!0;var G=mo(Z);if(G&&Tn(e,t,n,Z,G))return!0}if(ra(e,t,n)||sa(e,n,t)||Vr(e,n,t))return!0;var ce=(0,R.W)(i);if(ce){if(q("⌘=",t)){var ne=ce.querySelector(".vditor-ir__marker--heading");return ne&&ne.textContent.trim().length>1&&Mn(e,ne.textContent.substr(1)),t.preventDefault(),!0}if(q("⌘-",t)){var ne=ce.querySelector(".vditor-ir__marker--heading");return ne&&ne.textContent.trim().length<6&&Mn(e,ne.textContent.trim()+"# "),t.preventDefault(),!0}}var z=(0,f.F9)(i);if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(oa(e,n,t,w))return!0;if(z&&z.previousElementSibling&&z.tagName!=="UL"&&z.tagName!=="OL"&&(z.previousElementSibling.getAttribute("data-type")==="code-block"||z.previousElementSibling.getAttribute("data-type")==="math-block")){var de=(0,I.im)(z,e.ir.element,n).start;if(de===0||de===1&&z.innerText.startsWith(m.g.ZWSP))return n.selectNodeContents(z.previousElementSibling.querySelector(".vditor-ir__marker--pre code")),n.collapse(!1),Q(n,e),z.textContent.trim().replace(m.g.ZWSP,"")===""&&(z.remove(),Rt(e)),t.preventDefault(),!0}if(ce){var oe=ce.firstElementChild.textContent.length;(0,I.im)(ce,e.ir.element).start===oe&&oe!==0&&(n.setStart(ce.firstElementChild.firstChild,oe-1),n.collapse(!0),(0,I.Hc)(n))}}return(t.key==="ArrowUp"||t.key==="ArrowDown")&&z&&(z.querySelectorAll(".vditor-ir__node").forEach(function(me){me.contains(i)||me.classList.add("vditor-ir__node--hidden")}),ca(t,z,n))?!0:($i(n,t.key),z&&an(z,e,t,n)?(t.preventDefault(),!0):!1)},x=h(190),N=function(e,t){e.querySelectorAll("[data-type=footnotes-link]").forEach(function(n){for(var i=n.parentElement,a=i.nextSibling;a&&a.textContent.startsWith("    ");){var o=a;o.childNodes.forEach(function(d){i.append(d.cloneNode(!0))}),a=a.nextSibling,o.remove()}t&&t(i)})},B=function(e,t){var n,i=getSelection().getRangeAt(0).cloneRange(),a=i.startContainer;i.startContainer.nodeType!==3&&i.startContainer.tagName==="DIV"&&(a=i.startContainer.childNodes[i.startOffset-1]);var o=(0,f.a1)(a,"data-block","0");if(o&&t&&(t.inputType==="deleteContentBackward"||t.data===" ")){for(var d=(0,I.im)(o,e.sv.element,i).start,w=!0,C=d-1;C>o.textContent.substr(0,d).lastIndexOf(`
`);C--)if(o.textContent.charAt(C)!==" "&&o.textContent.charAt(C)!=="	"){w=!1;break}if(d===0&&(w=!1),w){ze(e);return}if(t.inputType==="deleteContentBackward"){var v=(0,f.a1)(a,"data-type","code-block-open-marker")||(0,f.a1)(a,"data-type","code-block-close-marker");if(v){if(v.getAttribute("data-type")==="code-block-close-marker"){var H=Jn(a,"code-block-open-marker");if(H){H.textContent=v.textContent,ze(e);return}}if(v.getAttribute("data-type")==="code-block-open-marker"){var H=Jn(a,"code-block-close-marker",!1);if(H){H.textContent=v.textContent,ze(e);return}}}var j=(0,f.a1)(a,"data-type","math-block-open-marker");if(j){var Z=j.nextElementSibling.nextElementSibling;Z&&Z.getAttribute("data-type")==="math-block-close-marker"&&(Z.remove(),ze(e));return}o.querySelectorAll('[data-type="code-block-open-marker"]').forEach(function(z){z.textContent.length===1&&z.remove()}),o.querySelectorAll('[data-type="code-block-close-marker"]').forEach(function(z){z.textContent.length===1&&z.remove()});var X=(0,f.a1)(a,"data-type","heading-marker");if(X&&X.textContent.indexOf("#")===-1){ze(e);return}}if((t.data===" "||t.inputType==="deleteContentBackward")&&((0,f.a1)(a,"data-type","padding")||(0,f.a1)(a,"data-type","li-marker")||(0,f.a1)(a,"data-type","task-marker")||(0,f.a1)(a,"data-type","blockquote-marker"))){ze(e);return}}if(o&&o.textContent.trimRight()==="$$"){ze(e);return}o||(o=e.sv.element),((n=o.firstElementChild)===null||n===void 0?void 0:n.getAttribute("data-type"))==="link-ref-defs-block"&&(o=e.sv.element),(0,f.a1)(a,"data-type","footnotes-link")&&(o=e.sv.element),o.textContent.indexOf(Lute.Caret)===-1&&i.insertNode(document.createTextNode(Lute.Caret)),o.querySelectorAll("[style]").forEach(function(z){z.removeAttribute("style")}),o.querySelectorAll("font").forEach(function(z){z.outerHTML=z.innerHTML});var G=o.textContent,ce=o.isEqualNode(e.sv.element);if(ce)G=o.textContent;else{o.previousElementSibling&&(G=o.previousElementSibling.textContent+G,o.previousElementSibling.remove()),o.previousElementSibling&&G.indexOf(`---
`)===0&&(G=o.previousElementSibling.textContent+G,o.previousElementSibling.remove());var ne="";e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(z,de){z&&!o.isEqualNode(z.parentElement)&&(ne+=z.parentElement.textContent+`
`,z.parentElement.remove())}),e.sv.element.querySelectorAll("[data-type='footnotes-link']").forEach(function(z,de){z&&!o.isEqualNode(z.parentElement)&&(ne+=z.parentElement.textContent+`
`,z.parentElement.remove())}),G=ne+G}G=Fi(G,e),ce?o.innerHTML=G:o.outerHTML=G,e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(z){e.sv.element.insertAdjacentElement("beforeend",z.parentElement)}),N(e.sv.element,function(z){e.sv.element.insertAdjacentElement("beforeend",z)}),(0,I.ib)(e.sv.element,i),Ve(e),ze(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},K=function(e,t){var n,i,a,o,d;if(e.sv.composingLock=t.isComposing,t.isComposing||(t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"))return!1;var w=(0,I.zh)(e),C=w.startContainer;w.startContainer.nodeType!==3&&w.startContainer.tagName==="DIV"&&(C=w.startContainer.childNodes[w.startOffset-1]);var v=(0,f.a1)(C,"data-type","text"),H=(0,f.a1)(C,"data-type","blockquote-marker");if(!H&&w.startOffset===0&&v&&v.previousElementSibling&&v.previousElementSibling.getAttribute("data-type")==="blockquote-marker"&&(H=v.previousElementSibling),H&&t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&H.nextElementSibling.textContent.trim()===""&&(0,I.im)(H,e.sv.element,w).start===H.textContent.length)return((n=H.previousElementSibling)===null||n===void 0?void 0:n.getAttribute("data-type"))==="padding"&&H.previousElementSibling.setAttribute("data-action","enter-remove"),H.remove(),ze(e),t.preventDefault(),!0;var j=(0,f.a1)(C,"data-type","li-marker"),Z=(0,f.a1)(C,"data-type","task-marker"),X=j;if(X||Z&&Z.nextElementSibling.getAttribute("data-type")!=="task-marker"&&(X=Z),!X&&w.startOffset===0&&v&&v.previousElementSibling&&(v.previousElementSibling.getAttribute("data-type")==="li-marker"||v.previousElementSibling.getAttribute("data-type")==="task-marker")&&(X=v.previousElementSibling),X){var G=(0,I.im)(X,e.sv.element,w).start,ce=X.getAttribute("data-type")==="task-marker",ne=X;if(ce&&(ne=X.previousElementSibling.previousElementSibling.previousElementSibling),G===X.textContent.length){if(t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&X.nextElementSibling.textContent.trim()==="")return((i=ne.previousElementSibling)===null||i===void 0?void 0:i.getAttribute("data-type"))==="padding"?(ne.previousElementSibling.remove(),B(e)):(ce&&(ne.remove(),X.previousElementSibling.previousElementSibling.remove(),X.previousElementSibling.remove()),X.nextElementSibling.remove(),X.remove(),ze(e)),t.preventDefault(),!0;if(t.key==="Tab")return t.shiftKey?ne.previousElementSibling.getAttribute("data-type")==="padding"&&ne.previousElementSibling.remove():ne.insertAdjacentHTML("beforebegin",'<span data-type="padding">'.concat(ne.textContent.replace(/\S/g," "),"</span>")),/^\d/.test(ne.textContent)&&(ne.textContent=ne.textContent.replace(/^\d{1,}/,"1"),w.selectNodeContents(X.firstChild),w.collapse(!1)),B(e),t.preventDefault(),!0}}if(Vr(e,w,t))return!0;var z=(0,f.a1)(C,"data-block","0"),de=(0,R.S)(C,"SPAN");if(t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&z){var oe=!1,me=z.textContent.match(/^\n+/);(0,I.im)(z,e.sv.element).start<=(me?me[0].length:0)&&(oe=!0);var Ie=`
`;if(de){if(((a=de.previousElementSibling)===null||a===void 0?void 0:a.getAttribute("data-action"))==="enter-remove")return de.previousElementSibling.remove(),ze(e),t.preventDefault(),!0;Ie+=ro(de)}return w.insertNode(document.createTextNode(Ie)),w.collapse(!1),z&&z.textContent.trim()!==""&&!oe?B(e):ze(e),t.preventDefault(),!0}if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey){if(de&&((o=de.previousElementSibling)===null||o===void 0?void 0:o.getAttribute("data-type"))==="newline"&&(0,I.im)(de,e.sv.element,w).start===1&&de.getAttribute("data-type").indexOf("code-block-")===-1)return w.setStart(de,0),w.extractContents(),de.textContent.trim()!==""?B(e):ze(e),t.preventDefault(),!0;if(z&&(0,I.im)(z,e.sv.element,w).start===0&&z.previousElementSibling){w.extractContents();var De=z.previousElementSibling.lastElementChild;return De.getAttribute("data-type")==="newline"&&(De.remove(),De=z.previousElementSibling.lastElementChild),De.getAttribute("data-type")!=="newline"&&(De.insertAdjacentHTML("afterend",z.innerHTML),z.remove()),z.textContent.trim()!==""&&!(!((d=z.previousElementSibling)===null||d===void 0)&&d.querySelector('[data-type="code-block-open-marker"]'))?B(e):(De.getAttribute("data-type")!=="newline"&&(w.selectNodeContents(De.lastChild),w.collapse(!1)),ze(e)),t.preventDefault(),!0}}return!1},U=h(538),F=function(e){e.options.theme==="dark"?e.element.classList.add("vditor--dark"):e.element.classList.remove("vditor--dark")},ee=function(e){e.element.innerHTML="",e.element.classList.add("vditor"),e.options.rtl&&e.element.setAttribute("dir","rtl"),F(e),(0,U.Z)(e.options.preview.theme.current,e.options.preview.theme.path),typeof e.options.height=="number"?e.element.style.height=e.options.height+"px":e.element.style.height=e.options.height,typeof e.options.minHeight=="number"&&(e.element.style.minHeight=e.options.minHeight+"px"),typeof e.options.width=="number"?e.element.style.width=e.options.width+"px":e.element.style.width=e.options.width,e.element.appendChild(e.toolbar.element);var t=document.createElement("div");if(t.className="vditor-content",e.options.outline.position==="left"&&t.appendChild(e.outline.element),t.appendChild(e.wysiwyg.element.parentElement),t.appendChild(e.sv.element),t.appendChild(e.ir.element.parentElement),t.appendChild(e.preview.element),e.toolbar.elements.devtools&&t.appendChild(e.devtools.element),e.options.outline.position==="right"&&(e.outline.element.classList.add("vditor-outline--right"),t.appendChild(e.outline.element)),e.upload&&t.appendChild(e.upload.element),e.options.resize.enable&&t.appendChild(e.resize.element),t.appendChild(e.hint.element),t.appendChild(e.tip.element),e.element.appendChild(t),t.addEventListener("click",function(){A(e,["subToolbar"])}),e.toolbar.elements.export&&e.element.insertAdjacentHTML("beforeend",'<iframe id="vditorExportIframe" style="width: 100%;height: 0;border: 0"></iframe>'),Xt(e,e.options.mode,se(e)),document.execCommand("DefaultParagraphSeparator",!1,"p"),navigator.userAgent.indexOf("iPhone")>-1&&typeof window.visualViewport<"u"){var n=!1,i=function(a){n||(n=!0,requestAnimationFrame(function(){n=!1;var o=e.toolbar.element;o.style.transform="none",o.getBoundingClientRect().top<0&&(o.style.transform="translate(0, ".concat(-o.getBoundingClientRect().top,"px)"))}))};window.visualViewport.addEventListener("scroll",i),window.visualViewport.addEventListener("resize",i)}},Y=function(e){var t=window.innerWidth<=m.g.MOBILE_WIDTH?10:35;if(e.wysiwyg.element.parentElement.style.display!=="none"){var n=(e.wysiwyg.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.wysiwyg.element.style.padding="10px ".concat(Math.max(t,n),"px")}if(e.ir.element.parentElement.style.display!=="none"){var n=(e.ir.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.ir.element.style.padding="10px ".concat(Math.max(t,n),"px")}e.preview.element.style.display!=="block"?e.toolbar.element.style.paddingLeft=Math.max(5,parseInt(e[e.currentMode].element.style.paddingLeft||"0",10)+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0))+"px":e.toolbar.element.style.paddingLeft=5+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0)+"px"},te=function(e){if(e.options.typewriterMode){var t=window.innerHeight;typeof e.options.height=="number"?(t=e.options.height,typeof e.options.minHeight=="number"&&(t=Math.max(t,e.options.minHeight)),t=Math.min(window.innerHeight,t)):t=e.element.clientHeight,e.element.classList.contains("vditor--fullscreen")&&(t=window.innerHeight),e[e.currentMode].element.style.setProperty("--editor-bottom",(t-e.toolbar.element.offsetHeight)/2+"px")}},J;function he(){window.removeEventListener("resize",J)}var se=function(e){te(e),he(),window.addEventListener("resize",J=function(){Y(e),te(e)});var t=(0,c.pK)()&&localStorage.getItem(e.options.cache.id);return(!e.options.cache.enable||!t)&&(e.options.value?t=e.options.value:e.originalInnerHTML?t=e.lute.HTML2Md(e.originalInnerHTML):e.options.cache.enable||(t="")),t||""},fe=function(e){clearTimeout(e[e.currentMode].hlToolbarTimeoutId),e[e.currentMode].hlToolbarTimeoutId=window.setTimeout(function(){if(e[e.currentMode].element.getAttribute("contenteditable")!=="false"&&(0,I.Gb)(e[e.currentMode].element)){b(e.toolbar.elements,m.g.EDIT_TOOLBARS),E(e.toolbar.elements,m.g.EDIT_TOOLBARS);var t=(0,I.zh)(e),n=t.startContainer;t.startContainer.nodeType===3&&(n=t.startContainer.parentElement),n.classList.contains("vditor-reset")&&(n=n.childNodes[t.startOffset]);var i=e.currentMode==="sv"?(0,f.a1)(n,"data-type","heading"):(0,R.W)(n);i&&g(e.toolbar.elements,["headings"]);var a=e.currentMode==="sv"?(0,f.a1)(n,"data-type","blockquote"):(0,f.lG)(n,"BLOCKQUOTE");a&&g(e.toolbar.elements,["quote"]);var o=(0,f.a1)(n,"data-type","strong");o&&g(e.toolbar.elements,["bold"]);var d=(0,f.a1)(n,"data-type","em");d&&g(e.toolbar.elements,["italic"]);var w=(0,f.a1)(n,"data-type","s");w&&g(e.toolbar.elements,["strike"]);var C=(0,f.a1)(n,"data-type","a");C&&g(e.toolbar.elements,["link"]);var v=(0,f.lG)(n,"LI");v?(v.classList.contains("vditor-task")?g(e.toolbar.elements,["check"]):v.parentElement.tagName==="OL"?g(e.toolbar.elements,["ordered-list"]):v.parentElement.tagName==="UL"&&g(e.toolbar.elements,["list"]),E(e.toolbar.elements,["outdent","indent"])):y(e.toolbar.elements,["outdent","indent"]);var H=(0,f.a1)(n,"data-type","code-block");H&&(y(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),g(e.toolbar.elements,["code"]));var j=(0,f.a1)(n,"data-type","code");j&&(y(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),g(e.toolbar.elements,["inline-code"]));var Z=(0,f.a1)(n,"data-type","table");Z&&y(e.toolbar.elements,["headings","list","ordered-list","check","line","quote","code","table"])}},200)},ae=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),clearTimeout(e.wysiwyg.afterRenderTimeoutId),e.wysiwyg.afterRenderTimeoutId=window.setTimeout(function(){if(!e.wysiwyg.composingLock){var n=D(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},Te=function(e){for(var t=e.previousSibling;t;){if(t.nodeType!==3&&t.tagName==="A"&&!t.previousSibling&&t.innerHTML.replace(m.g.ZWSP,"")===""&&t.nextSibling)return t;t=t.previousSibling}return!1},He=function(e){for(var t=e.startContainer.nextSibling;t&&t.textContent==="";)t=t.nextSibling;return!!(t&&t.nodeType!==3&&(t.tagName==="CODE"||t.getAttribute("data-type")==="math-inline"||t.getAttribute("data-type")==="html-entity"||t.getAttribute("data-type")==="html-inline"))},Le=function(e){for(var t="",n=e.nextSibling;n;)n.nodeType===3?t+=n.textContent:t+=n.outerHTML,n=n.nextSibling;return t},Je=function(e){for(var t="",n=e.previousSibling;n;)n.nodeType===3?t=n.textContent+t:t=n.outerHTML+t,n=n.previousSibling;return t},Qe=function(e){for(var t=e;t&&!t.nextSibling;)t=t.parentElement;return t.nextSibling},gt=function(e){var t=Je(e.startContainer),n=Le(e.startContainer),i=e.startContainer.textContent,a=e.startOffset,o="",d="";return(i.substr(0,a)!==""&&i.substr(0,a)!==m.g.ZWSP||t)&&(o="".concat(t).concat(i.substr(0,a))),(i.substr(a)!==""&&i.substr(a)!==m.g.ZWSP||n)&&(d="".concat(i.substr(a)).concat(n)),{afterHTML:d,beforeHTML:o}},ot=function(e,t){Array.from(e.wysiwyg.element.childNodes).find(function(n){if(n.nodeType===3){var i=document.createElement("p");i.setAttribute("data-block","0"),i.textContent=n.textContent;var a=t.startContainer.nodeType===3?t.startOffset:n.textContent.length;return n.parentNode.insertBefore(i,n),n.remove(),t.setStart(i.firstChild,Math.min(i.firstChild.textContent.length,a)),t.collapse(!0),(0,I.Hc)(t),!0}else if(!n.getAttribute("data-block"))return n.tagName==="P"?n.remove():(n.tagName==="DIV"?(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>")):n.tagName==="BR"?n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"<wbr></p>"):(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"</p>")),(0,I.ib)(e.wysiwyg.element,t),t=getSelection().getRangeAt(0)),!0})},Tt=function(e,t){var n=(0,I.zh)(e),i=(0,f.F9)(n.startContainer);i||(i=n.startContainer.childNodes[n.startOffset]),!i&&e.wysiwyg.element.children.length===0&&(i=e.wysiwyg.element),i&&!i.classList.contains("vditor-wysiwyg__block")&&(n.insertNode(document.createElement("wbr")),i.innerHTML.trim()==="<wbr>"&&(i.innerHTML="<wbr><br>"),i.tagName==="BLOCKQUOTE"||i.classList.contains("vditor-reset")?i.innerHTML="<".concat(t,' data-block="0">').concat(i.innerHTML.trim(),"</").concat(t,">"):i.outerHTML="<".concat(t,' data-block="0">').concat(i.innerHTML.trim(),"</").concat(t,">"),(0,I.ib)(e.wysiwyg.element,n),dt(e))},sn=function(e){var t=getSelection().getRangeAt(0),n=(0,f.F9)(t.startContainer);n||(n=t.startContainer.childNodes[t.startOffset]),n&&(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>"),(0,I.ib)(e.wysiwyg.element,t)),e.wysiwyg.popover.style.display="none"},qe=function(e,t,n){n===void 0&&(n=!0);var i=e.previousElementSibling,a=i.ownerDocument.createRange();i.tagName==="CODE"?(i.style.display="inline-block",n?a.setStart(i.firstChild,1):a.selectNodeContents(i)):(i.style.display="block",i.firstChild.firstChild||i.firstChild.appendChild(document.createTextNode("")),a.selectNodeContents(i.firstChild)),n?a.collapse(!0):a.collapse(!1),(0,I.Hc)(a),!e.firstElementChild.classList.contains("language-mindmap")&&Ve(t)},yt=function(e,t){if(e.wysiwyg.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,I.zh)(e),i=n.startContainer;if(!zi(t,e,i)||(Gi(n,e,t),la(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var a=(0,f.F9)(i),o=(0,f.lG)(i,"P");if(Yi(t,e,o,n)||Xi(n,e,o,t)||ra(e,t,n))return!0;var d=(0,f.fb)(i,"vditor-wysiwyg__block");if(d){if(t.key==="Escape"&&d.children.length===2)return e.wysiwyg.popover.style.display="none",d.firstElementChild.style.display="none",e.wysiwyg.element.blur(),t.preventDefault(),!0;if(!(0,c.yl)(t)&&!t.shiftKey&&t.altKey&&t.key==="Enter"&&d.getAttribute("data-type")==="code-block"){var w=e.wysiwyg.popover.querySelector(".vditor-input");return w.focus(),w.select(),t.preventDefault(),!0}if(d.getAttribute("data-block")==="0"&&(ia(e,t,d.firstElementChild,n)||Tn(e,t,n,d.firstElementChild,d)||d.getAttribute("data-type")!=="yaml-front-matter"&&un(e,t,n,d.firstElementChild,d)))return!0}if(aa(e,n,t,o))return!0;var C=(0,f.E2)(i,"BLOCKQUOTE");if(C&&!t.shiftKey&&t.altKey&&t.key==="Enter"){(0,c.yl)(t)?n.setStartBefore(C):n.setStartAfter(C),(0,I.Hc)(n);var v=document.createElement("p");return v.setAttribute("data-block","0"),v.innerHTML=`
`,n.insertNode(v),n.collapse(!0),(0,I.Hc)(n),ae(e),Ve(e),t.preventDefault(),!0}var H=(0,R.W)(i);if(H){if(H.tagName==="H6"&&i.textContent.length===n.startOffset&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"){var j=document.createElement("p");return j.textContent=`
`,j.setAttribute("data-block","0"),i.parentElement.insertAdjacentElement("afterend",j),n.setStart(j,0),(0,I.Hc)(n),ae(e),Ve(e),t.preventDefault(),!0}if(q("⌘=",t)){var Z=parseInt(H.tagName.substr(1),10)-1;return Z>0&&(Tt(e,"h".concat(Z)),ae(e)),t.preventDefault(),!0}if(q("⌘-",t)){var Z=parseInt(H.tagName.substr(1),10)+1;return Z<7&&(Tt(e,"h".concat(Z)),ae(e)),t.preventDefault(),!0}t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&H.textContent.length===1&&sn(e)}if(sa(e,n,t))return!0;if(t.altKey&&t.key==="Enter"&&!(0,c.yl)(t)&&!t.shiftKey){var X=(0,f.lG)(i,"A"),G=(0,f.a1)(i,"data-type","link-ref"),ce=(0,f.a1)(i,"data-type","footnotes-ref");if(X||G||ce||H&&H.tagName.length===2){var ne=e.wysiwyg.popover.querySelector("input");ne.focus(),ne.select()}}if(Ze(e,t))return!0;if(q("⇧⌘U",t)){var z=e.wysiwyg.popover.querySelector('[data-type="up"]');if(z)return z.click(),t.preventDefault(),!0}if(q("⇧⌘D",t)){var z=e.wysiwyg.popover.querySelector('[data-type="down"]');if(z)return z.click(),t.preventDefault(),!0}if(Vr(e,n,t))return!0;if(!(0,c.yl)(t)&&t.shiftKey&&!t.altKey&&t.key==="Enter"&&i.parentElement.tagName!=="LI"&&i.parentElement.tagName!=="P")return["STRONG","STRIKE","S","I","EM","B"].includes(i.parentElement.tagName)?n.insertNode(document.createTextNode(`
`+m.g.ZWSP)):n.insertNode(document.createTextNode(`
`)),n.collapse(!1),(0,I.Hc)(n),ae(e),Ve(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(oa(e,n,t,o))return!0;if(a){if(a.previousElementSibling&&a.previousElementSibling.classList.contains("vditor-wysiwyg__block")&&a.previousElementSibling.getAttribute("data-block")==="0"&&a.tagName!=="UL"&&a.tagName!=="OL"){var de=(0,I.im)(a,e.wysiwyg.element,n).start;if(de===0&&n.startOffset===0||de===1&&a.innerText.startsWith(m.g.ZWSP))return qe(a.previousElementSibling.lastElementChild,e,!1),a.innerHTML.trim().replace(m.g.ZWSP,"")===""&&(a.remove(),ae(e)),t.preventDefault(),!0}var oe=n.startOffset;if(n.toString()===""&&i.nodeType===3&&i.textContent.charAt(oe-2)===`
`&&i.textContent.charAt(oe-1)!==m.g.ZWSP&&["STRONG","STRIKE","S","I","EM","B"].includes(i.parentElement.tagName))return i.textContent=i.textContent.substring(0,oe-1)+m.g.ZWSP,n.setStart(i,oe),n.collapse(!0),ae(e),t.preventDefault(),!0;i.textContent===m.g.ZWSP&&n.startOffset===1&&!i.previousSibling&&He(n)&&(i.textContent=""),a.querySelectorAll("span.vditor-wysiwyg__block[data-type='math-inline']").forEach(function(Ie){Ie.firstElementChild.style.display="inline",Ie.lastElementChild.style.display="none"}),a.querySelectorAll("span.vditor-wysiwyg__block[data-type='html-entity']").forEach(function(Ie){Ie.firstElementChild.style.display="inline",Ie.lastElementChild.style.display="none"})}}if((0,c.vU)()&&n.startOffset===1&&i.textContent.indexOf(m.g.ZWSP)>-1&&i.previousSibling&&i.previousSibling.nodeType!==3&&i.previousSibling.tagName==="CODE"&&(t.key==="Backspace"||t.key==="ArrowLeft"))return n.selectNodeContents(i.previousSibling),n.collapse(!1),t.preventDefault(),!0;if(ca(t,a,n))return t.preventDefault(),!0;if($i(n,t.key),t.key==="ArrowDown"){var me=i.nextSibling;me&&me.nodeType!==3&&me.getAttribute("data-type")==="math-inline"&&n.setStartAfter(me)}return a&&an(a,e,t,n)?(t.preventDefault(),!0):!1},Ze=function(e,t){if(q("⇧⌘X",t)){var n=e.wysiwyg.popover.querySelector('[data-type="remove"]');return n&&n.click(),t.preventDefault(),!0}},Mt=function(e){clearTimeout(e.wysiwyg.hlToolbarTimeoutId),e.wysiwyg.hlToolbarTimeoutId=window.setTimeout(function(){if(e.wysiwyg.element.getAttribute("contenteditable")!=="false"&&(0,I.Gb)(e.wysiwyg.element)){b(e.toolbar.elements,m.g.EDIT_TOOLBARS),E(e.toolbar.elements,m.g.EDIT_TOOLBARS);var t=getSelection().getRangeAt(0),n=t.startContainer;t.startContainer.nodeType===3?n=t.startContainer.parentElement:n=n.childNodes[t.startOffset>=n.childNodes.length?n.childNodes.length-1:t.startOffset];var i=(0,f.a1)(n,"data-type","footnotes-block");if(i){e.wysiwyg.popover.innerHTML="",wt(i,e),bt(e,i);return}var a=(0,f.lG)(n,"LI");a?(a.classList.contains("vditor-task")?g(e.toolbar.elements,["check"]):a.parentElement.tagName==="OL"?g(e.toolbar.elements,["ordered-list"]):a.parentElement.tagName==="UL"&&g(e.toolbar.elements,["list"]),E(e.toolbar.elements,["outdent","indent"])):y(e.toolbar.elements,["outdent","indent"]),(0,f.lG)(n,"BLOCKQUOTE")&&g(e.toolbar.elements,["quote"]),((0,f.lG)(n,"B")||(0,f.lG)(n,"STRONG"))&&g(e.toolbar.elements,["bold"]),((0,f.lG)(n,"I")||(0,f.lG)(n,"EM"))&&g(e.toolbar.elements,["italic"]),((0,f.lG)(n,"STRIKE")||(0,f.lG)(n,"S"))&&g(e.toolbar.elements,["strike"]),e.wysiwyg.element.querySelectorAll(".vditor-comment--focus").forEach(function(le){le.classList.remove("vditor-comment--focus")});var o=(0,f.fb)(n,"vditor-comment");if(o){var d=o.getAttribute("data-cmtids").split(" ");if(d.length>1&&o.nextSibling.isSameNode(o.nextElementSibling)){var w=o.nextElementSibling.getAttribute("data-cmtids").split(" ");d.find(function(le){if(w.includes(le))return d=[le],!0})}e.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(le){le.getAttribute("data-cmtids").indexOf(d[0])>-1&&le.classList.add("vditor-comment--focus")})}var C=(0,f.lG)(n,"A");C&&g(e.toolbar.elements,["link"]);var v=(0,f.lG)(n,"TABLE"),H=(0,R.W)(n);(0,f.lG)(n,"CODE")?(0,f.lG)(n,"PRE")?(y(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),g(e.toolbar.elements,["code"])):(y(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),g(e.toolbar.elements,["inline-code"])):H?(y(e.toolbar.elements,["bold"]),g(e.toolbar.elements,["headings"])):v&&y(e.toolbar.elements,["table"]);var j=(0,f.fb)(n,"vditor-toc");if(j){e.wysiwyg.popover.innerHTML="",wt(j,e),bt(e,j);return}var Z=(0,R.S)(n,"BLOCKQUOTE");if(Z&&(e.wysiwyg.popover.innerHTML="",on(t,Z,e),ln(t,Z,e),wt(Z,e),bt(e,Z)),a&&(e.wysiwyg.popover.innerHTML="",on(t,a,e),ln(t,a,e),wt(a,e),bt(e,a)),v){e.options.lang,e.options,e.wysiwyg.popover.innerHTML="";var X=function(){var le=v.rows.length,_e=v.rows[0].cells.length,Kt=parseInt(Fe.value,10)||le,qt=parseInt(pt.value,10)||_e;if(!(Kt===le&&_e===qt)){if(_e!==qt)for(var An=qt-_e,At=0;At<v.rows.length;At++)if(An>0)for(var wa=0;wa<An;wa++)At===0?v.rows[At].lastElementChild.insertAdjacentHTML("afterend","<th> </th>"):v.rows[At].lastElementChild.insertAdjacentHTML("afterend","<td> </td>");else for(var Kr=_e-1;Kr>=qt;Kr--)v.rows[At].cells[Kr].remove();if(le!==Kt){var va=Kt-le;if(va>0){for(var qr="<tr>",pn=0;pn<qt;pn++)qr+="<td> </td>";for(var Ea=0;Ea<va;Ea++)v.querySelector("tbody")?v.querySelector("tbody").insertAdjacentHTML("beforeend",qr):v.querySelector("thead").insertAdjacentHTML("afterend",qr+"</tr>")}else for(var pn=le-1;pn>=Kt;pn--)v.rows[pn].remove(),v.rows.length===1&&v.querySelector("tbody").remove()}typeof e.options.input=="function"&&e.options.input(D(e))}},G=function(le){Yn(v,le),le==="right"?(de.classList.remove("vditor-icon--current"),oe.classList.remove("vditor-icon--current"),me.classList.add("vditor-icon--current")):le==="center"?(de.classList.remove("vditor-icon--current"),me.classList.remove("vditor-icon--current"),oe.classList.add("vditor-icon--current")):(oe.classList.remove("vditor-icon--current"),me.classList.remove("vditor-icon--current"),de.classList.add("vditor-icon--current")),(0,I.Hc)(t),ae(e)},ce=(0,f.lG)(n,"TD"),ne=(0,f.lG)(n,"TH"),z="left";ce?z=ce.getAttribute("align")||"left":ne&&(z=ne.getAttribute("align")||"center");var de=document.createElement("button");de.setAttribute("type","button"),de.setAttribute("aria-label",window.VditorI18n.alignLeft+"<"+(0,c.ns)("⇧⌘L")+">"),de.setAttribute("data-type","left"),de.innerHTML='<svg><use xlink:href="#vditor-icon-align-left"></use></svg>',de.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(z==="left"?" vditor-icon--current":""),de.onclick=function(){G("left")};var oe=document.createElement("button");oe.setAttribute("type","button"),oe.setAttribute("aria-label",window.VditorI18n.alignCenter+"<"+(0,c.ns)("⇧⌘C")+">"),oe.setAttribute("data-type","center"),oe.innerHTML='<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',oe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(z==="center"?" vditor-icon--current":""),oe.onclick=function(){G("center")};var me=document.createElement("button");me.setAttribute("type","button"),me.setAttribute("aria-label",window.VditorI18n.alignRight+"<"+(0,c.ns)("⇧⌘R")+">"),me.setAttribute("data-type","right"),me.innerHTML='<svg><use xlink:href="#vditor-icon-align-right"></use></svg>',me.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(z==="right"?" vditor-icon--current":""),me.onclick=function(){G("right")};var Ie=document.createElement("button");Ie.setAttribute("type","button"),Ie.setAttribute("aria-label",window.VditorI18n.insertRowBelow+"<"+(0,c.ns)("⌘=")+">"),Ie.setAttribute("data-type","insertRow"),Ie.innerHTML='<svg><use xlink:href="#vditor-icon-insert-row"></use></svg>',Ie.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Ie.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&Qi(e,t,_e)};var De=document.createElement("button");De.setAttribute("type","button"),De.setAttribute("aria-label",window.VditorI18n.insertRowAbove+"<"+(0,c.ns)("⇧⌘F")+">"),De.setAttribute("data-type","insertRow"),De.innerHTML='<svg><use xlink:href="#vditor-icon-insert-rowb"></use></svg>',De.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",De.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&ea(e,t,_e)};var Pe=document.createElement("button");Pe.setAttribute("type","button"),Pe.setAttribute("aria-label",window.VditorI18n.insertColumnRight+"<"+(0,c.ns)("⇧⌘=")+">"),Pe.setAttribute("data-type","insertColumn"),Pe.innerHTML='<svg><use xlink:href="#vditor-icon-insert-column"></use></svg>',Pe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Pe.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&Qn(e,v,_e)};var lt=document.createElement("button");lt.setAttribute("type","button"),lt.setAttribute("aria-label",window.VditorI18n.insertColumnLeft+"<"+(0,c.ns)("⇧⌘G")+">"),lt.setAttribute("data-type","insertColumn"),lt.innerHTML='<svg><use xlink:href="#vditor-icon-insert-columnb"></use></svg>',lt.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",lt.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&Qn(e,v,_e,"beforebegin")};var Xe=document.createElement("button");Xe.setAttribute("type","button"),Xe.setAttribute("aria-label",window.VditorI18n["delete-row"]+"<"+(0,c.ns)("⌘-")+">"),Xe.setAttribute("data-type","deleteRow"),Xe.innerHTML='<svg><use xlink:href="#vditor-icon-delete-row"></use></svg>',Xe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Xe.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&ta(e,t,_e)};var ct=document.createElement("button");ct.setAttribute("type","button"),ct.setAttribute("aria-label",window.VditorI18n["delete-column"]+"<"+(0,c.ns)("⇧⌘-")+">"),ct.setAttribute("data-type","deleteColumn"),ct.innerHTML='<svg><use xlink:href="#vditor-icon-delete-column"></use></svg>',ct.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",ct.onclick=function(){var le=getSelection().getRangeAt(0).startContainer,_e=(0,f.lG)(le,"TD")||(0,f.lG)(le,"TH");_e&&na(e,t,v,_e)};var we=document.createElement("span");we.setAttribute("aria-label",window.VditorI18n.row),we.className="vditor-tooltipped vditor-tooltipped__n";var Fe=document.createElement("input");we.appendChild(Fe),Fe.type="number",Fe.min="1",Fe.className="vditor-input",Fe.style.width="42px",Fe.style.textAlign="center",Fe.setAttribute("placeholder",window.VditorI18n.row),Fe.value=v.rows.length.toString(),Fe.oninput=function(){X()},Fe.onkeydown=function(le){if(!le.isComposing){if(le.key==="Tab"){pt.focus(),pt.select(),le.preventDefault();return}Ze(e,le)||Lt(le,t)}};var nr=document.createElement("span");nr.setAttribute("aria-label",window.VditorI18n.column),nr.className="vditor-tooltipped vditor-tooltipped__n";var pt=document.createElement("input");nr.appendChild(pt),pt.type="number",pt.min="1",pt.className="vditor-input",pt.style.width="42px",pt.style.textAlign="center",pt.setAttribute("placeholder",window.VditorI18n.column),pt.value=v.rows[0].cells.length.toString(),pt.oninput=function(){X()},pt.onkeydown=function(le){if(!le.isComposing){if(le.key==="Tab"){Fe.focus(),Fe.select(),le.preventDefault();return}Ze(e,le)||Lt(le,t)}},on(t,v,e),ln(t,v,e),wt(v,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",de),e.wysiwyg.popover.insertAdjacentElement("beforeend",oe),e.wysiwyg.popover.insertAdjacentElement("beforeend",me),e.wysiwyg.popover.insertAdjacentElement("beforeend",De),e.wysiwyg.popover.insertAdjacentElement("beforeend",Ie),e.wysiwyg.popover.insertAdjacentElement("beforeend",lt),e.wysiwyg.popover.insertAdjacentElement("beforeend",Pe),e.wysiwyg.popover.insertAdjacentElement("beforeend",Xe),e.wysiwyg.popover.insertAdjacentElement("beforeend",ct),e.wysiwyg.popover.insertAdjacentElement("beforeend",we),e.wysiwyg.popover.insertAdjacentHTML("beforeend"," x "),e.wysiwyg.popover.insertAdjacentElement("beforeend",nr),bt(e,v)}var Wr=(0,f.a1)(n,"data-type","link-ref");Wr&&Bi(e,Wr,t);var fn=(0,f.a1)(n,"data-type","footnotes-ref");if(fn){e.options.lang,e.options,e.wysiwyg.popover.innerHTML="";var we=document.createElement("span");we.setAttribute("aria-label",window.VditorI18n.footnoteRef+"<"+(0,c.ns)("⌥Enter")+">"),we.className="vditor-tooltipped vditor-tooltipped__n";var Nt=document.createElement("input");we.appendChild(Nt),Nt.className="vditor-input",Nt.setAttribute("placeholder",window.VditorI18n.footnoteRef+"<"+(0,c.ns)("⌥Enter")+">"),Nt.style.width="120px",Nt.value=fn.getAttribute("data-footnotes-label"),Nt.oninput=function(){Nt.value.trim()!==""&&fn.setAttribute("data-footnotes-label",Nt.value),typeof e.options.input=="function"&&e.options.input(D(e))},Nt.onkeydown=function(_e){_e.isComposing||Ze(e,_e)||Lt(_e,t)},wt(fn,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",we),bt(e,fn)}var Ge=(0,f.fb)(n,"vditor-wysiwyg__block"),ya=Ge?Ge.getAttribute("data-type").indexOf("block")>-1:!1;if(e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(le){if(!Ge||Ge&&ya&&!Ge.contains(le)){var _e=le.previousElementSibling;_e.style.display="none"}}),Ge&&ya){if(e.wysiwyg.popover.innerHTML="",on(t,Ge,e),ln(t,Ge,e),wt(Ge,e),Ge.getAttribute("data-type")==="code-block"){var rr=document.createElement("span");rr.setAttribute("aria-label",window.VditorI18n.language+"<"+(0,c.ns)("⌥Enter")+">"),rr.className="vditor-tooltipped vditor-tooltipped__n";var vt=document.createElement("input");rr.appendChild(vt);var Ln=Ge.firstElementChild.firstElementChild;vt.className="vditor-input",vt.setAttribute("placeholder",window.VditorI18n.language+"<"+(0,c.ns)("⌥Enter")+">"),vt.value=Ln.className.indexOf("language-")>-1?Ln.className.split("-")[1].split(" ")[0]:"",vt.oninput=function(le){vt.value.trim()!==""?Ln.className="language-".concat(vt.value):(Ln.className="",e.hint.recentLanguage=""),Ge.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(Ge.lastElementChild.innerHTML=Ge.firstElementChild.innerHTML,Ne(Ge.lastElementChild,e)),ae(e),le.detail===1&&(t.setStart(Ln.firstChild,0),t.collapse(!0),(0,I.Hc)(t))},vt.onkeydown=function(le){if(!le.isComposing&&!Ze(e,le)){if(le.key==="Escape"&&e.hint.element.style.display==="block"){e.hint.element.style.display="none",le.preventDefault();return}e.hint.select(le,e),Lt(le,t)}},vt.onkeyup=function(le){var _e,Kt;if(!(le.isComposing||le.key==="Enter"||le.key==="ArrowUp"||le.key==="Escape"||le.key==="ArrowDown")){var qt=[],An=vt.value.substring(0,vt.selectionStart);(e.options.preview.hljs.langs||m.g.ALIAS_CODE_LANGUAGES.concat(((Kt=(_e=window.hljs)===null||_e===void 0?void 0:_e.listLanguages())!==null&&Kt!==void 0?Kt:[]).sort())).forEach(function(At){At.indexOf(An.toLowerCase())>-1&&qt.push({html:At,value:At})}),e.hint.genHTML(qt,An,e),le.preventDefault()}},e.wysiwyg.popover.insertAdjacentElement("beforeend",rr)}bt(e,Ge)}else Ge=void 0;if(H){e.wysiwyg.popover.innerHTML="";var we=document.createElement("span");we.setAttribute("aria-label","ID<"+(0,c.ns)("⌥Enter")+">"),we.className="vditor-tooltipped vditor-tooltipped__n";var Wt=document.createElement("input");we.appendChild(Wt),Wt.className="vditor-input",Wt.setAttribute("placeholder","ID<"+(0,c.ns)("⌥Enter")+">"),Wt.style.width="120px",Wt.value=H.getAttribute("data-id")||"",Wt.oninput=function(){H.setAttribute("data-id",Wt.value),typeof e.options.input=="function"&&e.options.input(D(e))},Wt.onkeydown=function(_e){_e.isComposing||Ze(e,_e)||Lt(_e,t)},on(t,H,e),ln(t,H,e),wt(H,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",we),bt(e,H)}if(C&&Ar(e,C,t),!Z&&!a&&!v&&!Ge&&!C&&!Wr&&!fn&&!H&&!j){var dn=(0,f.a1)(n,"data-block","0");dn&&dn.parentElement.isEqualNode(e.wysiwyg.element)?(e.wysiwyg.popover.innerHTML="",on(t,dn,e),ln(t,dn,e),wt(dn,e),bt(e,dn)):e.wysiwyg.popover.style.display="none"}e.wysiwyg.element.querySelectorAll('span[data-type="backslash"] > span').forEach(function(le){le.style.display="none"});var ba=(0,f.a1)(t.startContainer,"data-type","backslash");ba&&(ba.querySelector("span").style.display="inline")}},200)},bt=function(e,t){var n=t,i=(0,f.lG)(t,"TABLE");i&&(n=i),e.wysiwyg.popover.style.left="0",e.wysiwyg.popover.style.display="block",e.wysiwyg.popover.style.top=Math.max(-8,n.offsetTop-21-e.wysiwyg.element.scrollTop)+"px",e.wysiwyg.popover.style.left=Math.min(n.offsetLeft,e.wysiwyg.element.clientWidth-e.wysiwyg.popover.clientWidth)+"px",e.wysiwyg.popover.setAttribute("data-top",(n.offsetTop-21).toString())},Bi=function(e,t,n){n===void 0&&(n=getSelection().getRangeAt(0)),e.wysiwyg.popover.innerHTML="";var i=function(){o.value.trim()!==""&&(t.tagName==="IMG"?t.setAttribute("alt",o.value):t.textContent=o.value),w.value.trim()!==""&&t.setAttribute("data-link-label",w.value),typeof e.options.input=="function"&&e.options.input(D(e))},a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),o.style.width="120px",o.value=t.getAttribute("alt")||t.textContent,o.oninput=function(){i()},o.onkeydown=function(C){Ze(e,C)||Lt(C,n)||_n(e,t,C,w)};var d=document.createElement("span");d.setAttribute("aria-label",window.VditorI18n.linkRef),d.className="vditor-tooltipped vditor-tooltipped__n";var w=document.createElement("input");d.appendChild(w),w.className="vditor-input",w.setAttribute("placeholder",window.VditorI18n.linkRef),w.value=t.getAttribute("data-link-label"),w.oninput=function(){i()},w.onkeydown=function(C){Ze(e,C)||Lt(C,n)||_n(e,t,C,o)},wt(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",a),e.wysiwyg.popover.insertAdjacentElement("beforeend",d),bt(e,t)},on=function(e,t,n){var i=t.previousElementSibling;if(!(!i||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var a=document.createElement("button");a.setAttribute("type","button"),a.setAttribute("data-type","up"),a.setAttribute("aria-label",window.VditorI18n.up+"<"+(0,c.ns)("⇧⌘U")+">"),a.innerHTML='<svg><use xlink:href="#vditor-icon-up"></use></svg>',a.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",a.onclick=function(){e.insertNode(document.createElement("wbr")),i.insertAdjacentElement("beforebegin",t),(0,I.ib)(n.wysiwyg.element,e),ae(n),Mt(n),Ve(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",a)}},ln=function(e,t,n){var i=t.nextElementSibling;if(!(!i||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var a=document.createElement("button");a.setAttribute("type","button"),a.setAttribute("data-type","down"),a.setAttribute("aria-label",window.VditorI18n.down+"<"+(0,c.ns)("⇧⌘D")+">"),a.innerHTML='<svg><use xlink:href="#vditor-icon-down"></use></svg>',a.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",a.onclick=function(){e.insertNode(document.createElement("wbr")),i.insertAdjacentElement("afterend",t),(0,I.ib)(n.wysiwyg.element,e),ae(n),Mt(n),Ve(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",a)}},wt=function(e,t){var n=document.createElement("button");n.setAttribute("type","button"),n.setAttribute("data-type","remove"),n.setAttribute("aria-label",window.VditorI18n.remove+"<"+(0,c.ns)("⇧⌘X")+">"),n.innerHTML='<svg><use xlink:href="#vditor-icon-trashcan"></use></svg>',n.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",n.onclick=function(){var i=(0,I.zh)(t);i.setStartAfter(e),(0,I.Hc)(i),e.remove(),ae(t),Mt(t),["H1","H2","H3","H4","H5","H6"].includes(e.tagName)&&dt(t)},t.wysiwyg.popover.insertAdjacentElement("beforeend",n)},_n=function(e,t,n,i){if(!n.isComposing){if(n.key==="Tab"){i.focus(),i.select(),n.preventDefault();return}if(!(0,c.yl)(n)&&!n.shiftKey&&n.altKey&&n.key==="Enter"){var a=(0,I.zh)(e);t.insertAdjacentHTML("afterend",m.g.ZWSP),a.setStartAfter(t.nextSibling),a.collapse(!0),(0,I.Hc)(a),n.preventDefault()}}},Ar=function(e,t,n){e.wysiwyg.popover.innerHTML="";var i=function(){o.value.trim()!==""&&(t.innerHTML=o.value),t.setAttribute("href",w.value),t.setAttribute("title",v.value),ae(e)};t.querySelectorAll("[data-marker]").forEach(function(H){H.removeAttribute("data-marker")});var a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),o.style.width="120px",o.value=t.innerHTML||"",o.oninput=function(){i()},o.onkeydown=function(H){Ze(e,H)||Lt(H,n)||_n(e,t,H,w)};var d=document.createElement("span");d.setAttribute("aria-label",window.VditorI18n.link),d.className="vditor-tooltipped vditor-tooltipped__n";var w=document.createElement("input");d.appendChild(w),w.className="vditor-input",w.setAttribute("placeholder",window.VditorI18n.link),w.value=t.getAttribute("href")||"",w.oninput=function(){i()},w.onkeydown=function(H){Ze(e,H)||Lt(H,n)||_n(e,t,H,v)};var C=document.createElement("span");C.setAttribute("aria-label",window.VditorI18n.tooltipText),C.className="vditor-tooltipped vditor-tooltipped__n";var v=document.createElement("input");C.appendChild(v),v.className="vditor-input",v.setAttribute("placeholder",window.VditorI18n.tooltipText),v.style.width="60px",v.value=t.getAttribute("title")||"",v.oninput=function(){i()},v.onkeydown=function(H){Ze(e,H)||Lt(H,n)||_n(e,t,H,o)},wt(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",a),e.wysiwyg.popover.insertAdjacentElement("beforeend",d),e.wysiwyg.popover.insertAdjacentElement("beforeend",C),bt(e,t)},Ys=function(e,t){var n=e.target;t.wysiwyg.popover.innerHTML="";var i=function(){n.setAttribute("src",o.value),n.setAttribute("alt",w.value),n.setAttribute("title",v.value),typeof t.options.input=="function"&&t.options.input(D(t))},a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.imageURL),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.imageURL),o.value=n.getAttribute("src")||"",o.oninput=function(){i()},o.onkeydown=function(H){Ze(t,H)};var d=document.createElement("span");d.setAttribute("aria-label",window.VditorI18n.alternateText),d.className="vditor-tooltipped vditor-tooltipped__n";var w=document.createElement("input");d.appendChild(w),w.className="vditor-input",w.setAttribute("placeholder",window.VditorI18n.alternateText),w.style.width="52px",w.value=n.getAttribute("alt")||"",w.oninput=function(){i()},w.onkeydown=function(H){Ze(t,H)};var C=document.createElement("span");C.setAttribute("aria-label",window.VditorI18n.title),C.className="vditor-tooltipped vditor-tooltipped__n";var v=document.createElement("input");C.appendChild(v),v.className="vditor-input",v.setAttribute("placeholder",window.VditorI18n.title),v.value=n.getAttribute("title")||"",v.oninput=function(){i()},v.onkeydown=function(H){Ze(t,H)},wt(n,t),t.wysiwyg.popover.insertAdjacentElement("beforeend",a),t.wysiwyg.popover.insertAdjacentElement("beforeend",d),t.wysiwyg.popover.insertAdjacentElement("beforeend",C),bt(t,n)},Lt=function(e,t){if(!(0,c.yl)(e)&&!e.shiftKey&&e.key==="Enter"||e.key==="Escape")return t&&(0,I.Hc)(t),e.preventDefault(),e.stopPropagation(),!0},cn=function(e){e.currentMode==="wysiwyg"?Mt(e):e.currentMode==="ir"&&fe(e)},Ui=function(e,t,n){n===void 0&&(n={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var i=e.wysiwyg.element;i.innerHTML=e.lute.Md2VditorDOM(t),i.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(a){Ne(a,e),a.previousElementSibling.setAttribute("style","display:none")}),ae(e,n)},Qs=function(e,t,n){for(var i=e.startContainer.parentElement,a=!1,o="",d="",w=gt(e),C=w.beforeHTML,v=w.afterHTML;i&&!a;){var H=i.tagName;if(H==="STRIKE"&&(H="S"),H==="I"&&(H="EM"),H==="B"&&(H="STRONG"),H==="S"||H==="STRONG"||H==="EM"){var j="",Z="",X="";i.parentElement.getAttribute("data-block")!=="0"&&(Z=Je(i),X=Le(i)),(C||Z)&&(j="".concat(Z,"<").concat(H,">").concat(C,"</").concat(H,">"),C=j),(n==="bold"&&H==="STRONG"||n==="italic"&&H==="EM"||n==="strikeThrough"&&H==="S")&&(j+="".concat(o).concat(m.g.ZWSP,"<wbr>").concat(d),a=!0),(v||X)&&(v="<".concat(H,">").concat(v,"</").concat(H,">").concat(X),j+=v),i.parentElement.getAttribute("data-block")!=="0"?(i=i.parentElement,i.innerHTML=j):(i.outerHTML=j,i=i.parentElement),o="<".concat(H,">")+o,d="</".concat(H,">")+d}else a=!0}(0,I.ib)(t.wysiwyg.element,e)},eo=function(e,t,n){if(!(e.wysiwyg.composingLock&&n instanceof CustomEvent)){var i=!0,a=!0;e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove();var o=(0,I.zh)(e),d=t.getAttribute("data-type");if(t.classList.contains("vditor-menu--current"))if(d==="strike"&&(d="strikeThrough"),d==="quote"){var w=(0,f.lG)(o.startContainer,"BLOCKQUOTE");w||(w=o.startContainer.childNodes[o.startOffset]),w&&(i=!1,t.classList.remove("vditor-menu--current"),o.insertNode(document.createElement("wbr")),w.outerHTML=w.innerHTML.trim()===""?'<p data-block="0">'.concat(w.innerHTML,"</p>"):w.innerHTML,(0,I.ib)(e.wysiwyg.element,o))}else if(d==="inline-code"){var C=(0,f.lG)(o.startContainer,"CODE");C||(C=o.startContainer.childNodes[o.startOffset]),C&&(C.outerHTML=C.innerHTML.replace(m.g.ZWSP,"")+"<wbr>",(0,I.ib)(e.wysiwyg.element,o))}else d==="link"?(o.collapsed&&o.selectNode(o.startContainer.parentElement),document.execCommand("unlink",!1,"")):d==="check"||d==="list"||d==="ordered-list"?(Xn(e,o,d),(0,I.ib)(e.wysiwyg.element,o),i=!1,t.classList.remove("vditor-menu--current")):(i=!1,t.classList.remove("vditor-menu--current"),o.toString()===""?Qs(o,e,d):document.execCommand(d,!1,""));else{e.wysiwyg.element.childNodes.length===0&&(e.wysiwyg.element.innerHTML='<p data-block="0"><wbr></p>',(0,I.ib)(e.wysiwyg.element,o));var v=(0,f.F9)(o.startContainer);if(d==="quote"){if(v||(v=o.startContainer.childNodes[o.startOffset]),v){i=!1,t.classList.add("vditor-menu--current"),o.insertNode(document.createElement("wbr"));var H=(0,f.lG)(o.startContainer,"LI");H&&v.contains(H)?H.innerHTML='<blockquote data-block="0">'.concat(H.innerHTML,"</blockquote>"):v.outerHTML='<blockquote data-block="0">'.concat(v.outerHTML,"</blockquote>"),(0,I.ib)(e.wysiwyg.element,o)}}else if(d==="check"||d==="list"||d==="ordered-list")Xn(e,o,d,!1),(0,I.ib)(e.wysiwyg.element,o),i=!1,b(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current");else if(d==="inline-code"){if(o.toString()===""){var j=document.createElement("code");j.textContent=m.g.ZWSP,o.insertNode(j),o.setStart(j.firstChild,1),o.collapse(!0),(0,I.Hc)(o)}else if(o.startContainer.nodeType===3){var j=document.createElement("code");o.surroundContents(j),o.insertNode(j),(0,I.Hc)(o)}t.classList.add("vditor-menu--current")}else if(d==="code"){var j=document.createElement("div");j.className="vditor-wysiwyg__block",j.setAttribute("data-type","code-block"),j.setAttribute("data-block","0"),j.setAttribute("data-marker","```"),o.toString()===""?j.innerHTML=`<pre><code><wbr>
</code></pre>`:(j.innerHTML="<pre><code>".concat(o.toString(),"<wbr></code></pre>"),o.deleteContents()),o.insertNode(j),v&&(v.outerHTML=e.lute.SpinVditorDOM(v.outerHTML)),(0,I.ib)(e.wysiwyg.element,o),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(Ie){Ne(Ie,e)}),t.classList.add("vditor-menu--disabled")}else if(d==="link"){if(o.toString()===""){var Z=document.createElement("a");Z.innerText=m.g.ZWSP,o.insertNode(Z),o.setStart(Z.firstChild,1),o.collapse(!0),Ar(e,Z,o);var X=e.wysiwyg.popover.querySelector("input");X.value="",X.focus(),a=!1}else{var j=document.createElement("a");j.setAttribute("href",""),j.innerHTML=o.toString(),o.surroundContents(j),o.insertNode(j),(0,I.Hc)(o),Ar(e,j,o);var G=e.wysiwyg.popover.querySelectorAll("input");G[0].value=j.innerText,G[1].focus()}i=!1,t.classList.add("vditor-menu--current")}else if(d==="table"){var ce='<table data-block="0"><thead><tr><th>col1<wbr></th><th>col2</th><th>col3</th></tr></thead><tbody><tr><td> </td><td> </td><td> </td></tr><tr><td> </td><td> </td><td> </td></tr></tbody></table>';if(o.toString().trim()==="")v&&v.innerHTML.trim().replace(m.g.ZWSP,"")===""?v.outerHTML=ce:document.execCommand("insertHTML",!1,ce),o.selectNode(e.wysiwyg.element.querySelector("wbr").previousSibling),e.wysiwyg.element.querySelector("wbr").remove(),(0,I.Hc)(o);else{ce='<table data-block="0"><thead><tr>';var ne=o.toString().split(`
`),z=ne[0].split(",").length>ne[0].split("	").length?",":"	";ne.forEach(function(me,Ie){Ie===0?(me.split(z).forEach(function(De,Pe){Pe===0?ce+="<th>".concat(De,"<wbr></th>"):ce+="<th>".concat(De,"</th>")}),ce+="</tr></thead>"):(Ie===1?ce+="<tbody><tr>":ce+="<tr>",me.split(z).forEach(function(De){ce+="<td>".concat(De,"</td>")}),ce+="</tr>")}),ce+="</tbody></table>",document.execCommand("insertHTML",!1,ce),(0,I.ib)(e.wysiwyg.element,o)}i=!1,t.classList.add("vditor-menu--disabled")}else if(d==="line"){if(v){var de=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;v.innerHTML.trim()===""?v.outerHTML=de:v.insertAdjacentHTML("afterend",de),(0,I.ib)(e.wysiwyg.element,o)}}else if(i=!1,t.classList.add("vditor-menu--current"),d==="strike"&&(d="strikeThrough"),o.toString()===""&&(d==="bold"||d==="italic"||d==="strikeThrough")){var oe="strong";d==="italic"?oe="em":d==="strikeThrough"&&(oe="s");var j=document.createElement(oe);j.textContent=m.g.ZWSP,o.insertNode(j),j.previousSibling&&j.previousSibling.textContent===m.g.ZWSP&&(j.previousSibling.textContent=""),o.setStart(j.firstChild,1),o.collapse(!0),(0,I.Hc)(o)}else document.execCommand(d,!1,"")}i&&Mt(e),a&&ae(e)}},Ue=(function(){function e(t,n){var i,a=this;this.element=document.createElement("div"),n.className&&(i=this.element.classList).add.apply(i,n.className.split(" "));var o=n.hotkey?" <".concat((0,c.ns)(n.hotkey),">"):"";n.level===2&&(o=n.hotkey?" &lt;".concat((0,c.ns)(n.hotkey),"&gt;"):"");var d=n.tip?n.tip+o:"".concat(window.VditorI18n[n.name]).concat(o),w=n.name==="upload"?"div":"button";if(n.level===2)this.element.innerHTML="<".concat(w,' data-type="').concat(n.name,'">').concat(d,"</").concat(w,">");else{this.element.classList.add("vditor-toolbar__item");var C=document.createElement(w);C.setAttribute("data-type",n.name),C.className="vditor-tooltipped vditor-tooltipped__".concat(n.tipPosition),C.setAttribute("aria-label",d),C.innerHTML=n.icon,this.element.appendChild(C)}n.prefix&&this.element.children[0].addEventListener((0,c.Le)(),function(v){v.preventDefault(),!a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)&&(t.currentMode==="wysiwyg"?eo(t,a.element.children[0],v):t.currentMode==="ir"?go(t,a.element.children[0],n.prefix||"",n.suffix||""):io(t,a.element.children[0],n.prefix||"",n.suffix||""))})}return e})(),to=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Xt=function(e,t,n){var i;if(typeof n!="string"?(A(e,["subToolbar","hint"]),n.preventDefault(),i=D(e)):i=n,!(e.currentMode===t&&typeof n!="string")){if(e.devtools&&e.devtools.renderEchart(e),e.options.preview.mode==="both"&&t==="sv"?e.preview.element.style.display="block":e.preview.element.style.display="none",E(e.toolbar.elements,m.g.EDIT_TOOLBARS),b(e.toolbar.elements,m.g.EDIT_TOOLBARS),y(e.toolbar.elements,["outdent","indent"]),t==="ir")O(e.toolbar.elements,["both"]),L(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="block",e.lute.SetVditorIR(!0),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!1),e.currentMode="ir",e.ir.element.innerHTML=e.lute.Md2VditorIRDOM(i),Rt(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Y(e),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(o){Ne(o,e)}),e.ir.element.querySelectorAll(".vditor-toc").forEach(function(o){(0,Se.H)(o,{cdn:e.options.cdn,math:e.options.preview.math})});else if(t==="wysiwyg")O(e.toolbar.elements,["both"]),L(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="block",e.ir.element.parentElement.style.display="none",e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!0),e.lute.SetVditorSV(!1),e.currentMode="wysiwyg",Y(e),Ui(e,i,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),e.wysiwyg.element.querySelectorAll(".vditor-toc").forEach(function(o){(0,Se.H)(o,{cdn:e.options.cdn,math:e.options.preview.math})}),e.wysiwyg.popover.style.display="none";else if(t==="sv"){L(e.toolbar.elements,["both"]),O(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="none",(e.options.preview.mode==="both"||e.options.preview.mode==="editor")&&(e.sv.element.style.display="block"),e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!0),e.currentMode="sv";var a=Fi(i,e);a==="<div data-block='0'></div>"&&(a=""),e.sv.element.innerHTML=a,N(e.sv.element),ze(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Y(e)}e.undo.resetIcon(e),typeof n!="string"&&(e[e.currentMode].element.focus(),cn(e)),dt(e),te(e),e.toolbar.elements["edit-mode"]&&(e.toolbar.elements["edit-mode"].querySelectorAll("button").forEach(function(o){o.classList.remove("vditor-menu--current")}),e.toolbar.elements["edit-mode"].querySelector('button[data-mode="'.concat(e.currentMode,'"]')).classList.add("vditor-menu--current")),e.outline.toggle(e,e.currentMode!=="sv"&&e.options.outline.enable,typeof n!="string")}},no=(function(e){to(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=document.createElement("div");return o.className="vditor-hint".concat(i.level===2?"":" vditor-panel--arrow"),o.innerHTML='<button data-mode="wysiwyg">'.concat(window.VditorI18n.wysiwyg," &lt;").concat((0,c.ns)("⌥⌘7"),`></button>
<button data-mode="ir">`).concat(window.VditorI18n.instantRendering," &lt;").concat((0,c.ns)("⌥⌘8"),`></button>
<button data-mode="sv">`).concat(window.VditorI18n.splitView," &lt;").concat((0,c.ns)("⌥⌘9"),"></button>"),a.element.appendChild(o),a._bindEvent(n,o,i),a}return t.prototype._bindEvent=function(n,i,a){var o=this.element.children[0];T(n,i,o,a.level),i.children.item(0).addEventListener((0,c.Le)(),function(d){Xt(n,"wysiwyg",d),d.preventDefault(),d.stopPropagation()}),i.children.item(1).addEventListener((0,c.Le)(),function(d){Xt(n,"ir",d),d.preventDefault(),d.stopPropagation()}),i.children.item(2).addEventListener((0,c.Le)(),function(d){Xt(n,"sv",d),d.preventDefault(),d.stopPropagation()})},t})(Ue),Sn=function(e,t){return(0,I.Gb)(e,t)?getSelection().toString():""},kr=function(e,t){t.addEventListener("focus",function(){e.options.focus&&e.options.focus(D(e)),A(e,["subToolbar","hint"])})},Vi=function(e,t){t.addEventListener("dblclick",function(n){n.target.tagName==="IMG"&&(e.options.image.preview?e.options.image.preview(n.target):e.options.image.isPreview&&(0,x.E)(n.target,e.options.lang,e.options.theme))})},xr=function(e,t){t.addEventListener("blur",function(n){if(e.currentMode==="ir"){var i=e.ir.element.querySelector(".vditor-ir__node--expand");i&&i.classList.remove("vditor-ir__node--expand")}else e.currentMode==="wysiwyg"&&!e.wysiwyg.selectPopover.contains(n.relatedTarget)&&e.wysiwyg.hideComment();e[e.currentMode].range=(0,I.zh)(e),e.options.blur&&e.options.blur(D(e))})},Or=function(e,t){t.addEventListener("dragstart",function(n){n.dataTransfer.setData(m.g.DROP_EDITOR,m.g.DROP_EDITOR)}),t.addEventListener("drop",function(n){n.dataTransfer.getData(m.g.DROP_EDITOR)?Ee(e):(n.dataTransfer.types.includes("Files")||n.dataTransfer.types.includes("text/html"))&&er(e,n,{pasteCode:function(i){document.execCommand("insertHTML",!1,i)}})})},Dr=function(e,t,n){t.addEventListener("copy",function(i){return n(i,e)})},Hr=function(e,t,n){t.addEventListener("cut",function(i){n(i,e),e.options.comment.enable&&e.currentMode==="wysiwyg"&&e.wysiwyg.getComments(e),document.execCommand("delete")})},Ve=function(e){if(e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)),!!e.options.typewriterMode){var t=e[e.currentMode].element,n=(0,I.Ny)(t).top;e.options.height==="auto"&&!e.element.classList.contains("vditor--fullscreen")&&window.scrollTo(window.scrollX,n+e.element.offsetTop+e.toolbar.element.offsetHeight-window.innerHeight/2+10),(e.options.height!=="auto"||e.element.classList.contains("vditor--fullscreen"))&&(t.scrollTop=n+t.scrollTop-t.clientHeight/2+10)}},Rr=function(e,t){t.addEventListener("keydown",function(n){if(!n.isComposing&&e.options.keydown&&e.options.keydown(n),!((e.options.hint.extend.length>1||e.toolbar.elements.emoji)&&e.hint.select(n,e))){if(e.options.comment.enable&&e.currentMode==="wysiwyg"&&(n.key==="Backspace"||q("⌘X",n))&&e.wysiwyg.getComments(e),e.currentMode==="sv"){if(K(e,n))return}else if(e.currentMode==="wysiwyg"){if(yt(e,n))return}else if(e.currentMode==="ir"&&Be(e,n))return;if(e.options.ctrlEnter&&q("⌘Enter",n)){e.options.ctrlEnter(D(e)),n.preventDefault();return}if(q("⌘Z",n)&&!e.toolbar.elements.undo){e.undo.undo(e),n.preventDefault();return}if(q("⌘Y",n)&&!e.toolbar.elements.redo){e.undo.redo(e),n.preventDefault();return}if(n.key==="Escape"){e.hint.element.style.display==="block"?e.hint.element.style.display="none":e.options.esc&&!n.isComposing&&e.options.esc(D(e)),n.preventDefault();return}if((0,c.yl)(n)&&n.altKey&&!n.shiftKey&&/^Digit[1-6]$/.test(n.code)){if(e.currentMode==="wysiwyg"){var i=n.code.replace("Digit","H");(0,f.lG)(getSelection().getRangeAt(0).startContainer,i)?sn(e):Tt(e,i),ae(e)}else e.currentMode==="sv"?Wi(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" "):e.currentMode==="ir"&&Mn(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" ");return n.preventDefault(),!0}if((0,c.yl)(n)&&n.altKey&&!n.shiftKey&&/^Digit[7-9]$/.test(n.code))return n.code==="Digit7"?Xt(e,"wysiwyg",n):n.code==="Digit8"?Xt(e,"ir",n):n.code==="Digit9"&&Xt(e,"sv",n),!0;e.options.toolbar.find(function(a){if(!a.hotkey||a.toolbar){if(a.toolbar){var o=a.toolbar.find(function(d){if(!d.hotkey)return!1;if(q(d.hotkey,n))return e.toolbar.elements[d.name].children[0].dispatchEvent(new CustomEvent((0,c.Le)())),n.preventDefault(),!0});return!!o}return!1}if(q(a.hotkey,n))return e.toolbar.elements[a.name].children[0].dispatchEvent(new CustomEvent((0,c.Le)())),n.preventDefault(),!0})}})},Nr=function(e,t){t.addEventListener("selectstart",function(n){t.onmouseup=function(){setTimeout(function(){var i=Sn(e[e.currentMode].element);i.trim()?(e.currentMode==="wysiwyg"&&e.options.comment.enable&&(!(0,f.a1)(n.target,"data-type","footnotes-block")&&!(0,f.a1)(n.target,"data-type","link-ref-defs-block")?e.wysiwyg.showComment():e.wysiwyg.hideComment()),e.options.select&&e.options.select(i)):(e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.wysiwyg.hideComment(),typeof e.options.unSelect=="function"&&e.options.unSelect())})}})},Ir=function(e,t){var n=(0,I.zh)(e);n.extractContents(),n.insertNode(document.createTextNode(Lute.Caret)),n.insertNode(document.createTextNode(t));var i=(0,f.a1)(n.startContainer,"data-block","0");i||(i=e.sv.element);var a=e.lute.SpinVditorSVDOM(i.textContent);a="<div data-block='0'>"+a.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",i.isEqualNode(e.sv.element)?i.innerHTML=a:i.outerHTML=a,N(e.sv.element),(0,I.ib)(e.sv.element,n),Ve(e)},Jn=function(e,t,n){n===void 0&&(n=!0);var i=e;for(i.nodeType===3&&(i=i.parentElement);i;){if(i.getAttribute("data-type")===t)return i;n?i=i.previousElementSibling:i=i.nextElementSibling}return!1},Fi=function(e,t){V("SpinVditorSVDOM",e,"argument",t.options.debugger);var n=t.lute.SpinVditorSVDOM(e);return e="<div data-block='0'>"+n.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",V("SpinVditorSVDOM",e,"result",t.options.debugger),e},ro=function(e){var t=e.getAttribute("data-type"),n=e.previousElementSibling,i=t&&t!=="text"&&t!=="table"&&t!=="heading-marker"&&t!=="newline"&&t!=="yaml-front-matter-open-marker"&&t!=="yaml-front-matter-close-marker"&&t!=="code-block-info"&&t!=="code-block-close-marker"&&t!=="code-block-open-marker"?e.textContent:"",a=!1;for(t==="newline"&&(a=!0);n&&!a;){var o=n.getAttribute("data-type");if(o==="li-marker"||o==="blockquote-marker"||o==="task-marker"||o==="padding"){var d=n.textContent;if(o==="li-marker"&&(t==="code-block-open-marker"||t==="code-block-info"))i=d.replace(/\S/g," ")+i;else if(t==="code-block-close-marker"&&n.nextElementSibling.isSameNode(e)){var w=Jn(e,"code-block-open-marker");w&&w.previousElementSibling&&(n=w.previousElementSibling,i=d+i)}else i=d+i}else o==="newline"&&(a=!0);n=n.previousElementSibling}return i},ze=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),e.preview.render(e);var n=D(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),clearTimeout(e.sv.processTimeoutId),e.sv.processTimeoutId=window.setTimeout(function(){t.enableAddUndoStack&&!e.sv.composingLock&&e.undo.addToUndoStack(e)},e.options.undoDelay)},Wi=function(e,t){var n=(0,I.zh)(e),i=(0,R.S)(n.startContainer,"SPAN");i&&i.textContent.trim()!==""&&(t=`
`+t),n.collapse(!0),document.execCommand("insertHTML",!1,t)},io=function(e,t,n,i){var a=(0,I.zh)(e),o=t.getAttribute("data-type");e.sv.element.childNodes.length===0&&(e.sv.element.innerHTML=`<span data-type="p" data-block="0"><span data-type="text"><wbr></span></span><span data-type="newline"><br><span style="display: none">
</span></span>`,(0,I.ib)(e.sv.element,a));var d=(0,f.F9)(a.startContainer),w=(0,R.S)(a.startContainer,"SPAN");if(d){if(o==="link"){var C=void 0;a.toString()===""?C="".concat(n).concat(Lute.Caret).concat(i):C="".concat(n).concat(a.toString()).concat(i.replace(")",Lute.Caret+")")),document.execCommand("insertHTML",!1,C);return}else if(o==="italic"||o==="bold"||o==="strike"||o==="inline-code"||o==="code"||o==="table"||o==="line"){var C=void 0;a.toString()===""?C="".concat(n).concat(Lute.Caret).concat(o==="code"?"":i):C="".concat(n).concat(a.toString()).concat(Lute.Caret).concat(o==="code"?"":i),o==="table"||o==="code"&&w&&w.textContent!==""?C=`

`+C:o==="line"&&(C=`

`.concat(n,`
`).concat(Lute.Caret)),document.execCommand("insertHTML",!1,C);return}else if((o==="check"||o==="list"||o==="ordered-list"||o==="quote")&&w){var v="* ";o==="check"?v="* [ ] ":o==="ordered-list"?v="1. ":o==="quote"&&(v="> ");var H=Jn(w,"newline");H?H.insertAdjacentText("afterend",v):d.insertAdjacentText("afterbegin",v),B(e);return}(0,I.ib)(e.sv.element,a),ze(e)}},Ki=function(e){switch(e.currentMode){case"ir":return e.ir.element;case"wysiwyg":return e.wysiwyg.element;case"sv":return e.sv.element}},qi=function(e,t){e.options.upload.setHeaders&&(e.options.upload.headers=e.options.upload.setHeaders()),e.options.upload.headers&&Object.keys(e.options.upload.headers).forEach(function(n){t.setRequestHeader(n,e.options.upload.headers[n])})},ao=function(e,t,n,i){function a(o){return o instanceof n?o:new n(function(d){d(o)})}return new(n||(n=Promise))(function(o,d){function w(H){try{v(i.next(H))}catch(j){d(j)}}function C(H){try{v(i.throw(H))}catch(j){d(j)}}function v(H){H.done?o(H.value):a(H.value).then(w,C)}v((i=i.apply(e,t||[])).next())})},so=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},i,a,o,d;return d={next:w(0),throw:w(1),return:w(2)},typeof Symbol=="function"&&(d[Symbol.iterator]=function(){return this}),d;function w(v){return function(H){return C([v,H])}}function C(v){if(i)throw new TypeError("Generator is already executing.");for(;d&&(d=0,v[0]&&(n=0)),n;)try{if(i=1,a&&(o=v[0]&2?a.return:v[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,v[1])).done)return o;switch(a=0,o&&(v=[v[0]&2,o.value]),v[0]){case 0:case 1:o=v;break;case 4:return n.label++,{value:v[1],done:!1};case 5:n.label++,a=v[1],v=[0];continue;case 7:v=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(v[0]===6||v[0]===2)){n=0;continue}if(v[0]===3&&(!o||v[1]>o[0]&&v[1]<o[3])){n.label=v[1];break}if(v[0]===6&&n.label<o[1]){n.label=o[1],o=v;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(v);break}o[2]&&n.ops.pop(),n.trys.pop();continue}v=t.call(e,n)}catch(H){v=[6,H],a=0}finally{i=o=0}if(v[0]&5)throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}},oo=(function(){function e(){this.isUploading=!1,this.element=document.createElement("div"),this.element.className="vditor-upload"}return e})(),lo=function(e,t){e.tip.hide();var n=[],i="",a="";e.options.lang,e.options;for(var o=function(v,H){var j=t[H],Z=!0;j.name||(i+="<li>".concat(window.VditorI18n.nameEmpty,"</li>"),Z=!1),j.size>e.options.upload.max&&(i+="<li>".concat(j.name," ").concat(window.VditorI18n.over," ").concat(e.options.upload.max/1024/1024,"M</li>"),Z=!1);var X=j.name.lastIndexOf("."),G=j.name.substr(X),ce=e.options.upload.filename(j.name.substr(0,X))+G;if(e.options.upload.accept){var ne=e.options.upload.accept.split(",").some(function(z){var de=z.trim();if(de.indexOf(".")===0){if(G.toLowerCase()===de.toLowerCase())return!0}else if(j.type.split("/")[0]===de.split("/")[0])return!0;return!1});ne||(i+="<li>".concat(j.name," ").concat(window.VditorI18n.fileTypeError,"</li>"),Z=!1)}Z&&(n.push(j),a+="<li>".concat(ce," ").concat(window.VditorI18n.uploading,' <a class="vditorCancelUpload" href="javascript:void(0)">').concat(window.VditorI18n.cancelUpload,"</a></li>"))},d=t.length,w=0;w<d;w++)o(d,w);if(e.tip.show("<ul>".concat(i).concat(a,"</ul>")),e.options.upload.cancel){var C=e.tip.element.querySelector(".vditorCancelUpload");C&&C.addEventListener("click",function(){e.options.upload.cancel(n),e.tip.hide(),e.upload.isUploading=!1})}return n},co=function(e,t){var n=Ki(t);n.focus();var i=JSON.parse(e),a="";i.code===1&&(a="".concat(i.msg)),i.data.errFiles&&i.data.errFiles.length>0&&(a="<ul><li>".concat(a,"</li>"),i.data.errFiles.forEach(function(d){var w=d.lastIndexOf("."),C=t.options.upload.filename(d.substr(0,w))+d.substr(w);a+="<li>".concat(C," ").concat(window.VditorI18n.uploadError,"</li>")}),a+="</ul>"),a?t.tip.show(a):t.tip.hide();var o="";Object.keys(i.data.succMap).forEach(function(d){var w=i.data.succMap[d],C=d.lastIndexOf("."),v=d.substr(C),H=t.options.upload.filename(d.substr(0,C))+v;v=v.toLowerCase(),v.indexOf(".wav")===0||v.indexOf(".mp3")===0||v.indexOf(".ogg")===0?t.currentMode==="wysiwyg"?o+=`<div class="vditor-wysiwyg__block" data-type="html-block"
 data-block="0"><pre><code>&lt;audio controls="controls" src="`.concat(w,'"&gt;&lt;/audio&gt;</code></pre><pre class="vditor-wysiwyg__preview" data-render="1"><audio controls="controls" src="').concat(w,`"></audio></pre></div>
`):t.currentMode==="ir"?o+='<audio controls="controls" src="'.concat(w,`"></audio>
`):o+="[".concat(H,"](").concat(w,`)
`):v.indexOf(".apng")===0||v.indexOf(".bmp")===0||v.indexOf(".gif")===0||v.indexOf(".ico")===0||v.indexOf(".cur")===0||v.indexOf(".jpg")===0||v.indexOf(".jpeg")===0||v.indexOf(".jfif")===0||v.indexOf(".pjp")===0||v.indexOf(".pjpeg")===0||v.indexOf(".png")===0||v.indexOf(".svg")===0||v.indexOf(".webp")===0?t.currentMode==="wysiwyg"?o+='<img alt="'.concat(H,'" src="').concat(w,`">
`):o+="![".concat(H,"](").concat(w,`)
`):t.currentMode==="wysiwyg"?o+='<a href="'.concat(w,'">').concat(H,`</a>
`):o+="[".concat(H,"](").concat(w,`)
`)}),(0,I.Hc)(t.upload.range),document.execCommand("insertHTML",!1,o),t.upload.range=getSelection().getRangeAt(0).cloneRange()},Pr=function(e,t,n){return ao(void 0,void 0,void 0,function(){var i,a,G,o,d,d,w,C,v,H,j,Z,X,G,ce,ne;return so(this,function(z){switch(z.label){case 0:for(i=[],a=e.options.upload.multiple===!0?t.length:1,G=0;G<a;G++)o=t[G],o instanceof DataTransferItem&&(o=o.getAsFile()),i.push(o);return e.options.upload.handler?[4,e.options.upload.handler(i)]:[3,2];case 1:return d=z.sent(),n&&(n.value=""),typeof d=="string"?(e.tip.show(d),[2]):[2];case 2:return!e.options.upload.url||!e.upload?(n&&(n.value=""),e.tip.show("please config: options.upload.url"),[2]):e.options.upload.file?[4,e.options.upload.file(i)]:[3,4];case 3:i=z.sent(),z.label=4;case 4:if(e.options.upload.validate&&(d=e.options.upload.validate(i),typeof d=="string"))return e.tip.show(d),[2];if(w=Ki(e),e.upload.range=(0,I.zh)(e),C=lo(e,i),C.length===0)return n&&(n.value=""),[2];for(v=new FormData,H=e.options.upload.extraData,j=0,Z=Object.keys(H);j<Z.length;j++)X=Z[j],v.append(X,H[X]);for(G=0,ce=C.length;G<ce;G++)v.append(e.options.upload.fieldName,C[G]);return ne=new XMLHttpRequest,e.upload.xhr=ne,ne.open("POST",e.options.upload.url),e.options.upload.token&&ne.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(ne.withCredentials=!0),qi(e,ne),e.upload.isUploading=!0,w.setAttribute("contenteditable","false"),ne.onreadystatechange=function(){if(ne.readyState===XMLHttpRequest.DONE){if(e.upload.isUploading=!1,w.setAttribute("contenteditable","true"),ne.status>=200&&ne.status<300)if(e.options.upload.success)e.options.upload.success(w,ne.responseText);else{var de=ne.responseText;e.options.upload.format&&(de=e.options.upload.format(t,ne.responseText)),co(de,e)}else e.options.upload.error?e.options.upload.error(ne.responseText):e.tip.show(ne.responseText);n&&(n.value=""),e.upload.element.style.display="none",e.upload.xhr=void 0}},ne.upload.onprogress=function(de){if(de.lengthComputable){var oe=de.loaded/de.total*100;e.upload.element.style.display="block";var me=e.upload.element;me.style.width=oe+"%"}},ne.send(v),[2]}})})},Zn=function(e,t,n){var i,a=(0,f.F9)(t.startContainer);if(a||(a=e.wysiwyg.element),n&&n.inputType!=="formatItalic"&&n.inputType!=="deleteByDrag"&&n.inputType!=="insertFromDrop"&&n.inputType!=="formatBold"&&n.inputType!=="formatRemove"&&n.inputType!=="formatStrikeThrough"&&n.inputType!=="insertUnorderedList"&&n.inputType!=="insertOrderedList"&&n.inputType!=="formatOutdent"&&n.inputType!=="formatIndent"&&n.inputType!==""||!n){var o=Te(t.startContainer);o&&o.remove(),e.wysiwyg.element.querySelectorAll("wbr").forEach(function(oe){oe.remove()}),t.insertNode(document.createElement("wbr")),a.querySelectorAll("[style]").forEach(function(oe){oe.removeAttribute("style")}),a.querySelectorAll(".vditor-comment").forEach(function(oe){oe.textContent.trim()===""&&(oe.classList.remove("vditor-comment","vditor-comment--focus"),oe.removeAttribute("data-cmtids"))}),(i=a.previousElementSibling)===null||i===void 0||i.querySelectorAll(".vditor-comment").forEach(function(oe){oe.textContent.trim()===""&&(oe.classList.remove("vditor-comment","vditor-comment--focus"),oe.removeAttribute("data-cmtids"))});var d="";a.getAttribute("data-type")==="link-ref-defs-block"&&(a=e.wysiwyg.element);var w=a.isEqualNode(e.wysiwyg.element),C=(0,f.a1)(a,"data-type","footnotes-block");if(w)d=a.innerHTML;else{var v=(0,f.O9)(t.startContainer);if(v&&!C){var H=(0,R.S)(t.startContainer,"BLOCKQUOTE");H?a=(0,f.F9)(t.startContainer)||a:a=v}if(C&&(a=C),d=a.outerHTML,a.tagName==="UL"||a.tagName==="OL"){var j=a.previousElementSibling,Z=a.nextElementSibling;j&&(j.tagName==="UL"||j.tagName==="OL")&&(d=j.outerHTML+d,j.remove()),Z&&(Z.tagName==="UL"||Z.tagName==="OL")&&(d=d+Z.outerHTML,Z.remove()),d=d.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}a.innerText.startsWith("```")||(e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(oe){oe&&!a.isEqualNode(oe)&&(d+=oe.outerHTML,oe.remove())}),e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']").forEach(function(oe){oe&&!a.isEqualNode(oe)&&(d+=oe.outerHTML,oe.remove())}))}if(d=d.replace(/<\/(strong|b)><strong data-marker="\W{2}">/g,"").replace(/<\/(em|i)><em data-marker="\W{1}">/g,"").replace(/<\/(s|strike)><s data-marker="~{1,2}">/g,""),d==='<p data-block="0">```<wbr></p>'&&e.hint.recentLanguage&&(d='<p data-block="0">```<wbr></p>'.replace("```","```"+e.hint.recentLanguage)),V("SpinVditorDOM",d,"argument",e.options.debugger),d=e.lute.SpinVditorDOM(d),V("SpinVditorDOM",d,"result",e.options.debugger),w)a.innerHTML=d;else if(a.outerHTML=d,C){var X=(0,f.E2)(e.wysiwyg.element.querySelector("wbr"),"LI");if(X){var G=e.wysiwyg.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(X.getAttribute("data-marker"),'"]'));G&&G.setAttribute("aria-label",X.textContent.trim().substr(0,24))}}var ce,ne=e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']");ne.forEach(function(oe,me){me===0?ce=oe:(ce.insertAdjacentHTML("beforeend",oe.innerHTML),oe.remove())}),ne.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",ne[0]);var z,de=e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']");de.forEach(function(oe,me){me===0?z=oe:(z.insertAdjacentHTML("beforeend",oe.innerHTML),oe.remove())}),de.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",de[0]),(0,I.ib)(e.wysiwyg.element,t),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(oe){Ne(oe,e)}),n&&(n.inputType==="deleteContentBackward"||n.inputType==="deleteContentForward")&&e.options.comment.enable&&(e.wysiwyg.triggerRemoveComment(e),e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)))}dt(e),ae(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},uo=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},fo=function(e,t,n,i){function a(o){return o instanceof n?o:new n(function(d){d(o)})}return new(n||(n=Promise))(function(o,d){function w(H){try{v(i.next(H))}catch(j){d(j)}}function C(H){try{v(i.throw(H))}catch(j){d(j)}}function v(H){H.done?o(H.value):a(H.value).then(w,C)}v((i=i.apply(e,t||[])).next())})},po=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},i,a,o,d;return d={next:w(0),throw:w(1),return:w(2)},typeof Symbol=="function"&&(d[Symbol.iterator]=function(){return this}),d;function w(v){return function(H){return C([v,H])}}function C(v){if(i)throw new TypeError("Generator is already executing.");for(;d&&(d=0,v[0]&&(n=0)),n;)try{if(i=1,a&&(o=v[0]&2?a.return:v[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,v[1])).done)return o;switch(a=0,o&&(v=[v[0]&2,o.value]),v[0]){case 0:case 1:o=v;break;case 4:return n.label++,{value:v[1],done:!1};case 5:n.label++,a=v[1],v=[0];continue;case 7:v=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(v[0]===6||v[0]===2)){n=0;continue}if(v[0]===3&&(!o||v[1]>o[0]&&v[1]<o[3])){n.label=v[1];break}if(v[0]===6&&n.label<o[1]){n.label=o[1],o=v;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(v);break}o[2]&&n.ops.pop(),n.trys.pop();continue}v=t.call(e,n)}catch(H){v=[6,H],a=0}finally{i=o=0}if(v[0]&5)throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}},zi=function(e,t,n){if(e.keyCode===229&&e.code===""&&e.key==="Unidentified"&&t.currentMode!=="sv"){var i=(0,f.F9)(n);if(i&&i.textContent.trim()==="")return t[t.currentMode].composingLock=!0,!1}return!0},Gi=function(e,t,n){if(!(n.key==="Enter"||n.key==="Tab"||n.key==="Backspace"||n.key.indexOf("Arrow")>-1||(0,c.yl)(n)||n.key==="Escape"||n.shiftKey||n.altKey)){var i=(0,f.lG)(e.startContainer,"P")||(0,f.lG)(e.startContainer,"LI");if(i&&(0,I.im)(i,t[t.currentMode].element,e).start===0){i.nodeValue&&(i.nodeValue=i.nodeValue.replace(/\u2006/g,""));var a=document.createTextNode(m.g.ZWSP);e.insertNode(a),e.setStartAfter(a)}}},$i=function(e,t){if(t==="ArrowDown"||t==="ArrowUp"){var n=(0,f.a1)(e.startContainer,"data-type","math-inline")||(0,f.a1)(e.startContainer,"data-type","html-entity")||(0,f.a1)(e.startContainer,"data-type","html-inline");n&&(t==="ArrowDown"&&e.setStartAfter(n.parentElement),t==="ArrowUp"&&e.setStartBefore(n.parentElement))}},Cn=function(e,t){var n=(0,I.zh)(e),i=(0,f.F9)(n.startContainer);i&&(i.insertAdjacentHTML(t,'<p data-block="0">'.concat(m.g.ZWSP,`<wbr>
</p>`)),(0,I.ib)(e[e.currentMode].element,n),cn(e),Ee(e))},ho=function(e){var t=(0,f.lG)(e,"TABLE");return t&&t.rows[0].cells[0].isSameNode(e)?t:!1},mo=function(e){var t=(0,f.lG)(e,"TABLE");return t&&t.lastElementChild.lastElementChild.lastElementChild.isSameNode(e)?t:!1},Ji=function(e,t,n){n===void 0&&(n=!0);var i=e.previousElementSibling;return i||(e.parentElement.previousElementSibling?i=e.parentElement.previousElementSibling.lastElementChild:e.parentElement.parentElement.tagName==="TBODY"&&e.parentElement.parentElement.previousElementSibling?i=e.parentElement.parentElement.previousElementSibling.lastElementChild.lastElementChild:i=null),i&&(t.selectNodeContents(i),n||t.collapse(!1),(0,I.Hc)(t)),i},Tn=function(e,t,n,i,a){var o=(0,I.im)(i,e[e.currentMode].element,n);if(t.key==="ArrowDown"&&i.textContent.trimRight().substr(o.start).indexOf(`
`)===-1||t.key==="ArrowRight"&&o.start>=i.textContent.trimRight().length){var d=a.nextElementSibling;return!d||d&&(d.tagName==="TABLE"||d.getAttribute("data-type"))?(a.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(m.g.ZWSP,"<wbr></p>")),(0,I.ib)(e[e.currentMode].element,n)):(n.selectNodeContents(d),n.collapse(!0),(0,I.Hc)(n)),t.preventDefault(),!0}return!1},un=function(e,t,n,i,a){var o=(0,I.im)(i,e[e.currentMode].element,n);if(t.key==="ArrowUp"&&i.textContent.substr(0,o.start).indexOf(`
`)===-1||(t.key==="ArrowLeft"||t.key==="Backspace"&&n.toString()==="")&&o.start===0){var d=a.previousElementSibling;return!d||d&&(d.tagName==="TABLE"||d.getAttribute("data-type"))?(a.insertAdjacentHTML("beforebegin",'<p data-block="0">'.concat(m.g.ZWSP,"<wbr></p>")),(0,I.ib)(e[e.currentMode].element,n)):(n.selectNodeContents(d),n.collapse(!1),(0,I.Hc)(n)),t.preventDefault(),!0}return!1},Xn=function(e,t,n,i){i===void 0&&(i=!0);var a=(0,f.lG)(t.startContainer,"LI");if(e[e.currentMode].element.querySelectorAll("wbr").forEach(function(H){H.remove()}),t.insertNode(document.createElement("wbr")),i&&a){for(var o="",d=0;d<a.parentElement.childElementCount;d++){var w=a.parentElement.children[d].querySelector("input");w&&w.remove(),o+='<p data-block="0">'.concat(a.parentElement.children[d].innerHTML.trimLeft(),"</p>")}a.parentElement.insertAdjacentHTML("beforebegin",o),a.parentElement.remove()}else if(a)if(n==="check")a.parentElement.querySelectorAll("li").forEach(function(H){H.insertAdjacentHTML("afterbegin",'<input type="checkbox" />'.concat(H.textContent.indexOf(" ")===0?"":" ")),H.classList.add("vditor-task")});else{a.querySelector("input")&&a.parentElement.querySelectorAll("li").forEach(function(H){H.querySelector("input").remove(),H.classList.remove("vditor-task")});var v=void 0;n==="list"?(v=document.createElement("ul"),v.setAttribute("data-marker","*")):(v=document.createElement("ol"),v.setAttribute("data-marker","1.")),v.setAttribute("data-block","0"),v.setAttribute("data-tight",a.parentElement.getAttribute("data-tight")),v.innerHTML=a.parentElement.innerHTML,a.parentElement.parentNode.replaceChild(v,a.parentElement)}else{var C=(0,f.a1)(t.startContainer,"data-block","0");C||(e[e.currentMode].element.querySelector("wbr").remove(),C=e[e.currentMode].element.querySelector("p"),C.innerHTML="<wbr>"),n==="check"?(C.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li class="vditor-task"><input type="checkbox" /> '.concat(C.innerHTML,"</li></ul>")),C.remove()):n==="list"?(C.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li>'.concat(C.innerHTML,"</li></ul>")),C.remove()):n==="ordered-list"&&(C.insertAdjacentHTML("beforebegin",'<ol data-block="0"><li>'.concat(C.innerHTML,"</li></ol>")),C.remove())}},Zi=function(e,t,n){var i=t.previousElementSibling;if(t&&i){var a=[t];Array.from(n.cloneContents().children).forEach(function(C,v){C.nodeType!==3&&t&&C.textContent.trim()!==""&&t.getAttribute("data-node-id")===C.getAttribute("data-node-id")&&(v!==0&&a.push(t),t=t.nextElementSibling)}),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(C){C.remove()}),n.insertNode(document.createElement("wbr"));var o=i.parentElement,d="";a.forEach(function(C){var v=C.getAttribute("data-marker");v.length!==1&&(v="1".concat(v.slice(-1))),d+='<li data-node-id="'.concat(C.getAttribute("data-node-id"),'" data-marker="').concat(v,'">').concat(C.innerHTML,"</li>"),C.remove()}),i.insertAdjacentHTML("beforeend","<".concat(o.tagName,' data-block="0">').concat(d,"</").concat(o.tagName,">")),e.currentMode==="wysiwyg"?o.outerHTML=e.lute.SpinVditorDOM(o.outerHTML):o.outerHTML=e.lute.SpinVditorIRDOM(o.outerHTML),(0,I.ib)(e[e.currentMode].element,n);var w=(0,f.O9)(n.startContainer);w&&w.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(C){Ne(C,e),e.currentMode==="wysiwyg"&&C.previousElementSibling.setAttribute("style","display:none")}),Ee(e),cn(e)}else e[e.currentMode].element.focus()},jr=function(e,t,n,i){var a=(0,f.lG)(t.parentElement,"LI");if(a){e[e.currentMode].element.querySelectorAll("wbr").forEach(function(j){j.remove()}),n.insertNode(document.createElement("wbr"));var o=t.parentElement,d=o.cloneNode(),w=[t];Array.from(n.cloneContents().children).forEach(function(j,Z){j.nodeType!==3&&t&&j.textContent.trim()!==""&&t.getAttribute("data-node-id")===j.getAttribute("data-node-id")&&(Z!==0&&w.push(t),t=t.nextElementSibling)});var C=!1,v="";o.querySelectorAll("li").forEach(function(j){C&&(v+=j.outerHTML,!j.nextElementSibling&&!j.previousElementSibling?j.parentElement.remove():j.remove()),j.isSameNode(w[w.length-1])&&(C=!0)}),w.reverse().forEach(function(j){a.insertAdjacentElement("afterend",j)}),v&&(d.innerHTML=v,w[0].insertAdjacentElement("beforeend",d)),e.currentMode==="wysiwyg"?i.outerHTML=e.lute.SpinVditorDOM(i.outerHTML):i.outerHTML=e.lute.SpinVditorIRDOM(i.outerHTML),(0,I.ib)(e[e.currentMode].element,n);var H=(0,f.O9)(n.startContainer);H&&H.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(j){Ne(j,e),e.currentMode==="wysiwyg"&&j.previousElementSibling.setAttribute("style","display:none")}),Ee(e),cn(e)}else e[e.currentMode].element.focus()},Yn=function(e,t){for(var n=getSelection().getRangeAt(0).startContainer.parentElement,i=e.rows[0].cells.length,a=e.rows.length,o=0,d=0;d<a;d++)for(var w=0;w<i;w++)if(e.rows[d].cells[w].isSameNode(n)){o=w;break}for(var C=0;C<a;C++)e.rows[C].cells[o].setAttribute("align",t)},Br=function(e){var t=e.trimRight().split(`
`).pop();return t===""?!1:(t.replace(/ |-/g,"")===""||t.replace(/ |_/g,"")===""||t.replace(/ |\*/g,"")==="")&&t.replace(/ /g,"").length>2?!(t.indexOf("-")>-1&&t.trimLeft().indexOf(" ")===-1&&e.trimRight().split(`
`).length>1||t.indexOf("    ")===0||t.indexOf("	")===0):!1},Ur=function(e){var t=e.trimRight().split(`
`);return e=t.pop(),e.indexOf("    ")===0||e.indexOf("	")===0||(e=e.trimLeft(),e===""||t.length===0)?!1:e.replace(/-/g,"")===""||e.replace(/=/g,"")===""},Ee=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),e.currentMode==="wysiwyg"?ae(e,t):e.currentMode==="ir"?Rt(e,t):e.currentMode==="sv"&&ze(e,t)},Xi=function(e,t,n,i){var a,o=e.startContainer,d=(0,f.lG)(o,"LI");if(d){if(!(0,c.yl)(i)&&!i.altKey&&i.key==="Enter"&&!i.shiftKey&&n&&d.contains(n)&&n.nextElementSibling)return d&&!d.textContent.endsWith(`
`)&&d.insertAdjacentText("beforeend",`
`),e.insertNode(document.createTextNode(`

`)),e.collapse(!1),Ee(t),i.preventDefault(),!0;if(!(0,c.yl)(i)&&!i.shiftKey&&!i.altKey&&i.key==="Backspace"&&!d.previousElementSibling&&e.toString()===""&&(0,I.im)(d,t[t.currentMode].element,e).start===0)return d.nextElementSibling?(d.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(d.innerHTML,"</p>")),d.remove()):d.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(d.innerHTML,"</p>"),(0,I.ib)(t[t.currentMode].element,e),Ee(t),i.preventDefault(),!0;if(!(0,c.yl)(i)&&!i.shiftKey&&!i.altKey&&i.key==="Backspace"&&d.textContent.trim().replace(m.g.ZWSP,"")===""&&e.toString()===""&&((a=d.previousElementSibling)===null||a===void 0?void 0:a.tagName)==="LI")return d.previousElementSibling.insertAdjacentText("beforeend",`

`),e.selectNodeContents(d.previousElementSibling),e.collapse(!1),d.remove(),(0,I.ib)(t[t.currentMode].element,e),Ee(t),i.preventDefault(),!0;if(!(0,c.yl)(i)&&!i.altKey&&i.key==="Tab"){var w=!1;if((e.startOffset===0&&(o.nodeType===3&&!o.previousSibling||o.nodeType!==3&&o.nodeName==="LI")||d.classList.contains("vditor-task")&&e.startOffset===1&&o.previousSibling.nodeType!==3&&o.previousSibling.tagName==="INPUT")&&(w=!0),w||e.toString()!=="")return i.shiftKey?jr(t,d,e,d.parentElement):Zi(t,d,e),i.preventDefault(),!0}}return!1},Vr=function(e,t,n){if(e.options.tab&&n.key==="Tab")return n.shiftKey||(t.toString()===""?(t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1)):(t.extractContents(),t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1))),(0,I.Hc)(t),Ee(e),n.preventDefault(),!0},Yi=function(e,t,n,i){if(n){if(!(0,c.yl)(e)&&!e.altKey&&e.key==="Enter"){var a=String.raw(ua||(ua=uo(["",""],["",""])),n.textContent).replace(/\\\|/g,"").trim(),o=a.split("|");if(a.startsWith("|")&&a.endsWith("|")&&o.length>3){var d=o.map(function(){return"---"}).join("|");return d=n.textContent+`
`+d.substring(3,d.length-3)+`
|<wbr>`,n.outerHTML=t.lute.SpinVditorDOM(d),(0,I.ib)(t[t.currentMode].element,i),Ee(t),Ve(t),e.preventDefault(),!0}if(Br(n.innerHTML)&&n.previousElementSibling){var w="",C=n.innerHTML.trimRight().split(`
`);return C.length>1&&(C.pop(),w='<p data-block="0">'.concat(C.join(`
`),"</p>")),n.insertAdjacentHTML("afterend","".concat(w,`<hr data-block="0"><p data-block="0"><wbr>
</p>`)),n.remove(),(0,I.ib)(t[t.currentMode].element,i),Ee(t),Ve(t),e.preventDefault(),!0}if(Ur(n.innerHTML))return t.currentMode==="wysiwyg"?n.outerHTML=t.lute.SpinVditorDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`):n.outerHTML=t.lute.SpinVditorIRDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`),(0,I.ib)(t[t.currentMode].element,i),Ee(t),Ve(t),e.preventDefault(),!0}if(i.collapsed&&n.previousElementSibling&&e.key==="Backspace"&&!(0,c.yl)(e)&&!e.altKey&&!e.shiftKey&&n.textContent.trimRight().split(`
`).length>1&&(0,I.im)(n,t[t.currentMode].element,i).start===0){var v=(0,f.DX)(n.previousElementSibling);return v.textContent.endsWith(`
`)||(v.textContent=v.textContent+`
`),v.parentElement.insertAdjacentHTML("beforeend","<wbr>".concat(n.innerHTML)),n.remove(),(0,I.ib)(t[t.currentMode].element,i),!1}return!1}},Qi=function(e,t,n){for(var i="",a=0;a<n.parentElement.childElementCount;a++)i+='<td align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </td>');n.tagName==="TH"?n.parentElement.parentElement.insertAdjacentHTML("afterend","<tbody><tr>".concat(i,"</tr></tbody>")):n.parentElement.insertAdjacentHTML("afterend","<tr>".concat(i,"</tr>")),Ee(e)},ea=function(e,t,n){for(var i="",a=0;a<n.parentElement.childElementCount;a++)n.tagName==="TH"?i+='<th align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </th>'):i+='<td align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </td>');if(n.tagName==="TH"){n.parentElement.parentElement.insertAdjacentHTML("beforebegin","<thead><tr>".concat(i,"</tr></thead>")),t.insertNode(document.createElement("wbr"));var o=n.parentElement.innerHTML.replace(/<th>/g,"<td>").replace(/<\/th>/g,"</td>");n.parentElement.parentElement.nextElementSibling.insertAdjacentHTML("afterbegin",o),n.parentElement.parentElement.remove(),(0,I.ib)(e.ir.element,t)}else n.parentElement.insertAdjacentHTML("beforebegin","<tr>".concat(i,"</tr>"));Ee(e)},Qn=function(e,t,n,i){i===void 0&&(i="afterend");for(var a=0,o=n.previousElementSibling;o;)a++,o=o.previousElementSibling;for(var d=0;d<t.rows.length;d++)d===0?t.rows[d].cells[a].insertAdjacentHTML(i,"<th> </th>"):t.rows[d].cells[a].insertAdjacentHTML(i,"<td> </td>");Ee(e)},ta=function(e,t,n){if(n.tagName==="TD"){var i=n.parentElement.parentElement;n.parentElement.previousElementSibling?t.selectNodeContents(n.parentElement.previousElementSibling.lastElementChild):t.selectNodeContents(i.previousElementSibling.lastElementChild.lastElementChild),i.childElementCount===1?i.remove():n.parentElement.remove(),t.collapse(!1),(0,I.Hc)(t),Ee(e)}},na=function(e,t,n,i){for(var a=0,o=i.previousElementSibling;o;)a++,o=o.previousElementSibling;(i.previousElementSibling||i.nextElementSibling)&&(t.selectNodeContents(i.previousElementSibling||i.nextElementSibling),t.collapse(!0));for(var d=0;d<n.rows.length;d++){var w=n.rows[d].cells;if(w.length===1){n.remove(),cn(e);break}w[a].remove()}(0,I.Hc)(t),Ee(e)},ra=function(e,t,n){var i=n.startContainer,a=(0,f.lG)(i,"TD")||(0,f.lG)(i,"TH");if(a){if(!(0,c.yl)(t)&&!t.altKey&&t.key==="Enter"){(!a.lastElementChild||a.lastElementChild&&(!a.lastElementChild.isSameNode(a.lastChild)||a.lastElementChild.tagName!=="BR"))&&a.insertAdjacentHTML("beforeend","<br>");var o=document.createElement("br");return n.insertNode(o),n.setStartAfter(o),Ee(e),Ve(e),t.preventDefault(),!0}if(t.key==="Tab"){if(t.shiftKey)return Ji(a,n),t.preventDefault(),!0;var d=a.nextElementSibling;return d||(a.parentElement.nextElementSibling?d=a.parentElement.nextElementSibling.firstElementChild:a.parentElement.parentElement.tagName==="THEAD"&&a.parentElement.parentElement.nextElementSibling?d=a.parentElement.parentElement.nextElementSibling.firstElementChild.firstElementChild:d=null),d&&(n.selectNodeContents(d),(0,I.Hc)(n)),t.preventDefault(),!0}var w=a.parentElement.parentElement.parentElement;if(t.key==="ArrowUp"){if(t.preventDefault(),a.tagName==="TH")return w.previousElementSibling?(n.selectNodeContents(w.previousElementSibling),n.collapse(!1),(0,I.Hc)(n)):Cn(e,"beforebegin"),!0;for(var C=0,v=a.parentElement;C<v.cells.length&&!v.cells[C].isSameNode(a);C++);var H=v.previousElementSibling;return H||(H=v.parentElement.previousElementSibling.firstChild),n.selectNodeContents(H.cells[C]),n.collapse(!1),(0,I.Hc)(n),!0}if(t.key==="ArrowDown"){t.preventDefault();var v=a.parentElement;if(!v.nextElementSibling&&a.tagName==="TD")return w.nextElementSibling?(n.selectNodeContents(w.nextElementSibling),n.collapse(!0),(0,I.Hc)(n)):Cn(e,"afterend"),!0;for(var C=0;C<v.cells.length&&!v.cells[C].isSameNode(a);C++);var d=v.nextElementSibling;return d||(d=v.parentElement.nextElementSibling.firstChild),n.selectNodeContents(d.cells[C]),n.collapse(!0),(0,I.Hc)(n),!0}if(e.currentMode==="wysiwyg"&&!(0,c.yl)(t)&&t.key==="Enter"&&!t.shiftKey&&t.altKey){var j=e.wysiwyg.popover.querySelector(".vditor-input");return j.focus(),j.select(),t.preventDefault(),!0}if(!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Backspace"&&n.startOffset===0&&n.toString()===""){var Z=Ji(a,n,!1);return!Z&&w&&(w.textContent.trim()===""?(w.outerHTML=`<p data-block="0"><wbr>
</p>`,(0,I.ib)(e[e.currentMode].element,n)):(n.setStartBefore(w),n.collapse(!0)),Ee(e)),t.preventDefault(),!0}if(q("⇧⌘F",t))return ea(e,n,a),t.preventDefault(),!0;if(q("⌘=",t))return Qi(e,n,a),t.preventDefault(),!0;if(q("⇧⌘G",t))return Qn(e,w,a,"beforebegin"),t.preventDefault(),!0;if(q("⇧⌘=",t))return Qn(e,w,a),t.preventDefault(),!0;if(q("⌘-",t))return ta(e,n,a),t.preventDefault(),!0;if(q("⇧⌘-",t))return na(e,n,w,a),t.preventDefault(),!0;if(q("⇧⌘L",t)){if(e.currentMode==="ir")return Yn(w,"left"),Ee(e),t.preventDefault(),!0;var X=e.wysiwyg.popover.querySelector('[data-type="left"]');if(X)return X.click(),t.preventDefault(),!0}if(q("⇧⌘C",t)){if(e.currentMode==="ir")return Yn(w,"center"),Ee(e),t.preventDefault(),!0;var X=e.wysiwyg.popover.querySelector('[data-type="center"]');if(X)return X.click(),t.preventDefault(),!0}if(q("⇧⌘R",t)){if(e.currentMode==="ir")return Yn(w,"right"),Ee(e),t.preventDefault(),!0;var X=e.wysiwyg.popover.querySelector('[data-type="right"]');if(X)return X.click(),t.preventDefault(),!0}}return!1},ia=function(e,t,n,i){if(n.tagName==="PRE"&&q("⌘A",t))return i.selectNodeContents(n.firstElementChild),t.preventDefault(),!0;if(e.options.tab&&t.key==="Tab"&&!t.shiftKey&&i.toString()==="")return i.insertNode(document.createTextNode(e.options.tab)),i.collapse(!1),Ee(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey){var a=(0,I.im)(n,e[e.currentMode].element,i);if((a.start===0||a.start===1&&n.innerText===`
`)&&i.toString()==="")return n.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(n.firstElementChild.innerHTML,"</p>"),(0,I.ib)(e[e.currentMode].element,i),Ee(e),t.preventDefault(),!0}return!(0,c.yl)(t)&&!t.altKey&&t.key==="Enter"?(n.firstElementChild.textContent.endsWith(`
`)||n.firstElementChild.insertAdjacentText("beforeend",`
`),i.extractContents(),i.insertNode(document.createTextNode(`
`)),i.collapse(!1),(0,I.Hc)(i),(0,c.vU)()||(e.currentMode==="wysiwyg"?Zn(e,i):st(e,i)),Ve(e),t.preventDefault(),!0):!1},aa=function(e,t,n,i){var a=t.startContainer,o=(0,f.lG)(a,"BLOCKQUOTE");if(o&&t.toString()===""){if(n.key==="Backspace"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&(0,I.im)(o,e[e.currentMode].element,t).start===0)return t.insertNode(document.createElement("wbr")),o.outerHTML=o.innerHTML,(0,I.ib)(e[e.currentMode].element,t),Ee(e),n.preventDefault(),!0;if(i&&n.key==="Enter"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&i.parentElement.tagName==="BLOCKQUOTE"){var d=!1;if(i.innerHTML.replace(m.g.ZWSP,"")===`
`||i.innerHTML.replace(m.g.ZWSP,"")===""?(d=!0,i.remove()):i.innerHTML.endsWith(`

`)&&(0,I.im)(i,e[e.currentMode].element,t).start===i.textContent.length-1&&(i.innerHTML=i.innerHTML.substr(0,i.innerHTML.length-2),d=!0),d)return o.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(m.g.ZWSP,`<wbr>
</p>`)),(0,I.ib)(e[e.currentMode].element,t),Ee(e),n.preventDefault(),!0}var w=(0,f.F9)(a);if(e.currentMode==="wysiwyg"&&w&&q("⇧⌘;",n))return t.insertNode(document.createElement("wbr")),w.outerHTML='<blockquote data-block="0">'.concat(w.outerHTML,"</blockquote>"),(0,I.ib)(e.wysiwyg.element,t),ae(e),n.preventDefault(),!0;if(Tn(e,n,t,o,o)||un(e,n,t,o,o))return!0}return!1},sa=function(e,t,n){var i=t.startContainer,a=(0,f.lG)(i,"LI");if(a&&a.classList.contains("vditor-task")){if(q("⇧⌘J",n)){var o=a.firstElementChild;return o.checked?o.removeAttribute("checked"):o.setAttribute("checked","checked"),Ee(e),n.preventDefault(),!0}if(n.key==="Backspace"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&t.toString()===""&&t.startOffset===1&&(i.nodeType===3&&i.previousSibling&&i.previousSibling.tagName==="INPUT"||i.nodeType!==3)){var d=a.previousElementSibling;if(a.querySelector("input").remove(),d){var w=(0,f.DX)(d);w.parentElement.insertAdjacentHTML("beforeend","<wbr>"+a.innerHTML.trim()),a.remove()}else a.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(a.innerHTML.trim()||`
`,"</p>")),a.nextElementSibling?a.remove():a.parentElement.remove();return(0,I.ib)(e[e.currentMode].element,t),Ee(e),n.preventDefault(),!0}if(n.key==="Enter"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey){if(a.textContent.trim()==="")if((0,f.fb)(a.parentElement,"vditor-task")){var C=(0,f.O9)(i);C&&jr(e,a,t,C)}else if(a.nextElementSibling){var v="",H="",j=!1;Array.from(a.parentElement.children).forEach(function(ce){a.isSameNode(ce)?j=!0:j?v+=ce.outerHTML:H+=ce.outerHTML});var Z=a.parentElement.tagName,X=a.parentElement.tagName==="OL"?"":' data-marker="'.concat(a.parentElement.getAttribute("data-marker"),'"'),G="";H&&(G=a.parentElement.tagName==="UL"?"":' start="1"',H="<".concat(Z,' data-tight="true"').concat(X,' data-block="0">').concat(H,"</").concat(Z,">")),a.parentElement.outerHTML="".concat(H,`<p data-block="0"><wbr>
</p><`).concat(Z,`
 data-tight="true"`).concat(X,' data-block="0"').concat(G,">").concat(v,"</").concat(Z,">")}else a.parentElement.insertAdjacentHTML("afterend",`<p data-block="0"><wbr>
</p>`),a.parentElement.querySelectorAll("li").length===1?a.parentElement.remove():a.remove();else i.nodeType!==3&&t.startOffset===0&&i.firstChild.tagName==="INPUT"?t.setStart(i.childNodes[1],1):(t.setEndAfter(a.lastChild),a.insertAdjacentHTML("afterend",'<li class="vditor-task" data-marker="'.concat(a.getAttribute("data-marker"),'"><input type="checkbox"> <wbr></li>')),document.querySelector("wbr").after(t.extractContents()));return(0,I.ib)(e[e.currentMode].element,t),Ee(e),Ve(e),n.preventDefault(),!0}}return!1},oa=function(e,t,n,i){if(t.startContainer.nodeType!==3){var a=t.startContainer.children[t.startOffset];if(a&&a.tagName==="HR")return t.selectNodeContents(a.previousElementSibling),t.collapse(!1),n.preventDefault(),!0}if(i){var o=i.previousElementSibling;if(o&&(0,I.im)(i,e[e.currentMode].element,t).start===0&&((0,c.vU)()&&o.tagName==="HR"||o.tagName==="TABLE")){if(o.tagName==="TABLE"){var d=o.lastElementChild.lastElementChild.lastElementChild;d.innerHTML=d.innerHTML.trimLeft()+"<wbr>"+i.textContent.trim(),i.remove()}else o.remove();return(0,I.ib)(e[e.currentMode].element,t),Ee(e),n.preventDefault(),!0}}return!1},la=function(e){(0,c.vU)()&&e.startContainer.nodeType!==3&&e.startContainer.tagName==="HR"&&e.setStartBefore(e.startContainer)},ca=function(e,t,n){var i,a;if(!(0,c.vU)())return!1;if(e.key==="ArrowUp"&&t&&((i=t.previousElementSibling)===null||i===void 0?void 0:i.tagName)==="TABLE"){var o=t.previousElementSibling;return n.selectNodeContents(o.rows[o.rows.length-1].lastElementChild),n.collapse(!1),e.preventDefault(),!0}return e.key==="ArrowDown"&&t&&((a=t.nextElementSibling)===null||a===void 0?void 0:a.tagName)==="TABLE"?(n.selectNodeContents(t.nextElementSibling.rows[0].cells[0]),n.collapse(!0),e.preventDefault(),!0):!1},er=function(e,t,n){return fo(void 0,void 0,void 0,function(){var i,a,o,d,w,C,v,H,j,Z,X,G,ce,z,ne,z,de;return po(this,function(oe){switch(oe.label){case 0:return e[e.currentMode].element.getAttribute("contenteditable")!=="true"?[2]:(t.stopPropagation(),t.preventDefault(),"clipboardData"in t?(i=t.clipboardData.getData("text/html"),a=t.clipboardData.getData("text/plain"),o=t.clipboardData.files):(i=t.dataTransfer.getData("text/html"),a=t.dataTransfer.getData("text/plain"),t.dataTransfer.types.includes("Files")&&(o=t.dataTransfer.items)),d={},w=function(me,Ie){if(!Ie)return["",Lute.WalkContinue];if(e.options.upload.renderLinkDest)return e.options.upload.renderLinkDest(e,me,Ie);var De=me.TokensStr();if(me.__internal_object__.Parent.Type===34&&De&&De.indexOf("file://")===-1&&e.options.upload.linkToImgUrl){var Pe=new XMLHttpRequest;Pe.open("POST",e.options.upload.linkToImgUrl),e.options.upload.token&&Pe.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(Pe.withCredentials=!0),qi(e,Pe),Pe.setRequestHeader("Content-Type","application/json; charset=utf-8"),Pe.onreadystatechange=function(){if(Pe.readyState===XMLHttpRequest.DONE){if(Pe.status===200){var lt=Pe.responseText;e.options.upload.linkToImgFormat&&(lt=e.options.upload.linkToImgFormat(Pe.responseText));var Xe=JSON.parse(lt);if(Xe.code!==0){e.tip.show(Xe.msg);return}var ct=Xe.data.originalURL;if(e.currentMode==="sv")e.sv.element.querySelectorAll(".vditor-sv__marker--link").forEach(function(Fe){Fe.textContent===ct&&(Fe.textContent=Xe.data.url)});else{var we=e[e.currentMode].element.querySelector('img[src="'.concat(ct,'"]'));we.src=Xe.data.url,e.currentMode==="ir"&&(we.previousElementSibling.previousElementSibling.innerHTML=Xe.data.url)}Ee(e)}else e.tip.show(Pe.responseText);e.options.upload.linkToImgCallback&&e.options.upload.linkToImgCallback(Pe.responseText)}},Pe.send(JSON.stringify({url:De}))}return e.currentMode==="ir"?['<span class="vditor-ir__marker vditor-ir__marker--link">'.concat(Lute.EscapeHTMLStr(De),"</span>"),Lute.WalkContinue]:e.currentMode==="wysiwyg"?["",Lute.WalkContinue]:['<span class="vditor-sv__marker--link">'.concat(Lute.EscapeHTMLStr(De),"</span>"),Lute.WalkContinue]},(i.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<a href="'.concat(a,'">').concat(a,"</a>")||i.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<!--StartFragment--><a href="'.concat(a,'">').concat(a,"</a><!--EndFragment-->"))&&(i=""),C=new DOMParser().parseFromString(i,"text/html"),C.body&&(i=C.body.innerHTML),i=Lute.Sanitize(i),e.wysiwyg.getComments(e),v=e[e.currentMode].element.scrollHeight,H=Ke(i,a,e.currentMode),j=e.currentMode==="sv"?(0,f.a1)(t.target,"data-type","code-block"):(0,f.lG)(t.target,"CODE"),j?(e.currentMode==="sv"?document.execCommand("insertHTML",!1,a.replace(/&/g,"&amp;").replace(/</g,"&lt;")):(Z=(0,I.im)(t.target,e[e.currentMode].element),j.parentElement.tagName!=="PRE"&&(a+=m.g.ZWSP),j.textContent=j.textContent.substring(0,Z.start)+a+j.textContent.substring(Z.end),(0,I.$j)(Z.start+a.length,Z.start+a.length,j.parentElement),!((de=j.parentElement)===null||de===void 0)&&de.nextElementSibling.classList.contains("vditor-".concat(e.currentMode,"__preview"))&&(j.parentElement.nextElementSibling.innerHTML=j.outerHTML,Ne(j.parentElement.nextElementSibling,e))),[3,8]):[3,1]);case 1:return H?(n.pasteCode(H),[3,8]):[3,2];case 2:return i.trim()===""?[3,3]:(X=document.createElement("div"),X.innerHTML=i,X.querySelectorAll("[style]").forEach(function(me){me.removeAttribute("style")}),X.querySelectorAll(".vditor-copy").forEach(function(me){me.remove()}),e.currentMode==="ir"?(d.HTML2VditorIRDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),(0,I.oC)(e.lute.HTML2VditorIRDOM(X.innerHTML),e)):e.currentMode==="wysiwyg"?(d.HTML2VditorDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),(0,I.oC)(e.lute.HTML2VditorDOM(X.innerHTML),e)):(d.Md2VditorSVDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),Ir(e,e.lute.HTML2Md(X.innerHTML).trimRight())),e.outline.render(e),[3,8]);case 3:return o.length>0?e.options.upload.url||e.options.upload.handler?[4,Pr(e,o)]:[3,5]:[3,7];case 4:return oe.sent(),[3,6];case 5:G=new FileReader,"clipboardData"in t?(o=t.clipboardData.files,ce=o[0]):t.dataTransfer.types.includes("Files")&&(o=t.dataTransfer.items,ce=o[0].getAsFile()),ce&&ce.type.startsWith("image")&&(G.readAsDataURL(ce),G.onload=function(){var me="";e.currentMode==="wysiwyg"?me+='<img alt="'.concat(ce.name,'" src="').concat(G.result.toString(),`">
`):me+="![".concat(ce.name,"](").concat(G.result.toString(),`)
`),document.execCommand("insertHTML",!1,me)}),oe.label=6;case 6:return[3,8];case 7:a.trim()!==""&&o.length===0&&(z=(0,I.zh)(e),z.toString()!==""&&e.lute.IsValidLinkDest(a)&&(a="[".concat(z.toString(),"](").concat(a,")")),e.currentMode==="ir"?(d.Md2VditorIRDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),(0,I.oC)(e.lute.Md2VditorIRDOM(a),e)):e.currentMode==="wysiwyg"?(d.Md2VditorDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),(0,I.oC)(e.lute.Md2VditorDOM(a),e)):(d.Md2VditorSVDOM={renderLinkDest:w},e.lute.SetJSRenderers({renderers:d}),Ir(e,a)),e.outline.render(e)),oe.label=8;case 8:return e.currentMode!=="sv"&&(ne=(0,f.F9)((0,I.zh)(e).startContainer),ne&&(z=(0,I.zh)(e),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(me){me.remove()}),z.insertNode(document.createElement("wbr")),e.currentMode==="wysiwyg"?ne.outerHTML=e.lute.SpinVditorDOM(ne.outerHTML):ne.outerHTML=e.lute.SpinVditorIRDOM(ne.outerHTML),(0,I.ib)(e[e.currentMode].element,z)),e[e.currentMode].element.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(me){Ne(me,e)})),e.wysiwyg.triggerRemoveComment(e),Ee(e),e[e.currentMode].element.scrollHeight-v>Math.min(e[e.currentMode].element.clientHeight,window.innerHeight)/2&&Ve(e),[2]}})})},ua,fa=function(e){var t,n;e.hint.render(e);var i=(0,I.zh)(e).startContainer,a=(0,f.a1)(i,"data-type","code-block-info");if(a)if(a.textContent.replace(m.g.ZWSP,"")===""&&e.hint.recentLanguage){a.textContent=m.g.ZWSP+e.hint.recentLanguage;var o=(0,I.zh)(e);o.selectNodeContents(a)}else{var d=[],w=a.textContent.substring(0,(0,I.im)(a,e.ir.element).start).replace(m.g.ZWSP,"");(e.options.preview.hljs.langs||m.g.ALIAS_CODE_LANGUAGES.concat(((n=(t=window.hljs)===null||t===void 0?void 0:t.listLanguages())!==null&&n!==void 0?n:[]).sort())).forEach(function(C){C.indexOf(w.toLowerCase())>-1&&d.push({html:C,value:C})}),e.hint.genHTML(d,w,e)}},Rt=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&fa(e),clearTimeout(e.ir.processTimeoutId),e.ir.processTimeoutId=window.setTimeout(function(){if(!e.ir.composingLock){var n=D(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},Mn=function(e,t){var n=(0,I.zh)(e),i=(0,f.F9)(n.startContainer)||n.startContainer;if(i){var a=i.querySelector(".vditor-ir__marker--heading");a?a.innerHTML=t:(i.insertAdjacentText("afterbegin",t),n.selectNodeContents(i),n.collapse(!1)),st(e,n.cloneRange()),fe(e)}},tr=function(e,t,n){var i=(0,f.a1)(e.startContainer,"data-type",n);if(i){i.firstElementChild.remove(),i.lastElementChild.remove(),e.insertNode(document.createElement("wbr"));var a=document.createElement("div");a.innerHTML=t.lute.SpinVditorIRDOM(i.outerHTML),i.outerHTML=a.firstElementChild.innerHTML.trim()}},go=function(e,t,n,i){var a=(0,I.zh)(e),o=t.getAttribute("data-type"),d=a.startContainer;d.nodeType===3&&(d=d.parentElement);var w=!0;if(t.classList.contains("vditor-menu--current"))if(o==="quote"){var C=(0,f.lG)(d,"BLOCKQUOTE");C&&(a.insertNode(document.createElement("wbr")),C.outerHTML=C.innerHTML.trim()===""?'<p data-block="0">'.concat(C.innerHTML,"</p>"):C.innerHTML)}else if(o==="link"){var v=(0,f.a1)(a.startContainer,"data-type","a");if(v){var H=(0,f.fb)(a.startContainer,"vditor-ir__link");H?(a.insertNode(document.createElement("wbr")),v.outerHTML=H.innerHTML):v.outerHTML=v.querySelector(".vditor-ir__link").innerHTML+"<wbr>"}}else o==="italic"?tr(a,e,"em"):o==="bold"?tr(a,e,"strong"):o==="strike"?tr(a,e,"s"):o==="inline-code"?tr(a,e,"code"):(o==="check"||o==="list"||o==="ordered-list")&&(Xn(e,a,o),w=!1,t.classList.remove("vditor-menu--current"));else{e.ir.element.childNodes.length===0&&(e.ir.element.innerHTML='<p data-block="0"><wbr></p>',(0,I.ib)(e.ir.element,a));var j=(0,f.F9)(a.startContainer);if(o==="line"){if(j){var Z=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;j.innerHTML.trim()===""?j.outerHTML=Z:j.insertAdjacentHTML("afterend",Z)}}else if(o==="quote")j&&(a.insertNode(document.createElement("wbr")),j.outerHTML='<blockquote data-block="0">'.concat(j.outerHTML,"</blockquote>"),w=!1,t.classList.add("vditor-menu--current"));else if(o==="link"){var X=void 0;a.toString()===""?X="".concat(n,"<wbr>").concat(i):X="".concat(n).concat(a.toString()).concat(i.replace(")","<wbr>)")),document.execCommand("insertHTML",!1,X),w=!1,t.classList.add("vditor-menu--current")}else if(o==="italic"||o==="bold"||o==="strike"||o==="inline-code"||o==="code"||o==="table"){var X=void 0;a.toString()===""?X="".concat(n,"<wbr>").concat(i):(o==="code"?X="".concat(n,`
`).concat(a.toString(),"<wbr>").concat(i):o==="table"?X="".concat(n).concat(a.toString(),"<wbr>").concat(i):X="".concat(n).concat(a.toString()).concat(i,"<wbr>"),a.deleteContents()),(o==="table"||o==="code")&&(X=`
`+X+`

`);var G=document.createElement("span");G.innerHTML=X,a.insertNode(G),st(e,a),o==="table"&&(a.selectNodeContents(getSelection().getRangeAt(0).startContainer.parentElement),(0,I.Hc)(a))}else(o==="check"||o==="list"||o==="ordered-list")&&(Xn(e,a,o,!1),w=!1,b(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current"))}(0,I.ib)(e.ir.element,a),Rt(e),w&&fe(e)},yo=function(e,t,n,i){function a(o){return o instanceof n?o:new n(function(d){d(o)})}return new(n||(n=Promise))(function(o,d){function w(H){try{v(i.next(H))}catch(j){d(j)}}function C(H){try{v(i.throw(H))}catch(j){d(j)}}function v(H){H.done?o(H.value):a(H.value).then(w,C)}v((i=i.apply(e,t||[])).next())})},bo=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},i,a,o,d;return d={next:w(0),throw:w(1),return:w(2)},typeof Symbol=="function"&&(d[Symbol.iterator]=function(){return this}),d;function w(v){return function(H){return C([v,H])}}function C(v){if(i)throw new TypeError("Generator is already executing.");for(;d&&(d=0,v[0]&&(n=0)),n;)try{if(i=1,a&&(o=v[0]&2?a.return:v[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,v[1])).done)return o;switch(a=0,o&&(v=[v[0]&2,o.value]),v[0]){case 0:case 1:o=v;break;case 4:return n.label++,{value:v[1],done:!1};case 5:n.label++,a=v[1],v=[0];continue;case 7:v=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(v[0]===6||v[0]===2)){n=0;continue}if(v[0]===3&&(!o||v[1]>o[0]&&v[1]<o[3])){n.label=v[1];break}if(v[0]===6&&n.label<o[1]){n.label=o[1],o=v;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(v);break}o[2]&&n.ops.pop(),n.trys.pop();continue}v=t.call(e,n)}catch(H){v=[6,H],a=0}finally{i=o=0}if(v[0]&5)throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}},wo=(function(){function e(t){var n=this;this.splitChar="",this.lastIndex=-1,this.fillEmoji=function(i,a){n.element.style.display="none";var o=decodeURIComponent(i.getAttribute("data-value")),d=window.getSelection().getRangeAt(0);if(a.currentMode==="ir"){var w=(0,f.a1)(d.startContainer,"data-type","code-block-info");if(w){w.textContent=m.g.ZWSP+o.trimRight(),d.selectNodeContents(w),d.collapse(!1),Rt(a),w.parentElement.querySelectorAll("code").forEach(function(j){j.className="language-"+o.trimRight()}),Ne(w.parentElement.querySelector(".vditor-ir__preview"),a),n.recentLanguage=o.trimRight();return}}if(a.currentMode==="wysiwyg"&&d.startContainer.nodeType!==3){var C=d.startContainer,v=void 0;if(C.classList.contains("vditor-input")?v=C:v=C.firstElementChild,v&&v.classList.contains("vditor-input")){v.value=o.trimRight(),d.selectNodeContents(v),d.collapse(!1),v.dispatchEvent(new CustomEvent("input",{detail:1})),n.recentLanguage=o.trimRight();return}}if(d.setStart(d.startContainer,n.lastIndex),d.deleteContents(),a.options.hint.parse?a.currentMode==="sv"?(0,I.oC)(a.lute.SpinVditorSVDOM(o),a):a.currentMode==="wysiwyg"?(0,I.oC)(a.lute.SpinVditorDOM(o),a):(0,I.oC)(a.lute.SpinVditorIRDOM(o),a):(0,I.oC)(o,a),n.splitChar===":"&&o.indexOf(":")>-1&&a.currentMode!=="sv"&&d.insertNode(document.createTextNode(" ")),d.collapse(!1),(0,I.Hc)(d),a.currentMode==="wysiwyg"){var H=(0,f.fb)(d.startContainer,"vditor-wysiwyg__block");H&&H.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(H.lastElementChild.innerHTML=H.firstElementChild.innerHTML,Ne(H.lastElementChild,a))}else if(a.currentMode==="ir"){var H=(0,f.fb)(d.startContainer,"vditor-ir__marker--pre");H&&H.nextElementSibling.classList.contains("vditor-ir__preview")&&(H.nextElementSibling.innerHTML=H.innerHTML,Ne(H.nextElementSibling,a))}Ee(a)},this.timeId=-1,this.element=document.createElement("div"),this.element.className="vditor-hint",this.recentLanguage="",t.push({key:":"})}return e.prototype.render=function(t){var n=this;if(window.getSelection().focusNode){var i,a=getSelection().getRangeAt(0);i=a.startContainer.textContent.substring(0,a.startOffset)||"";var o=this.getKey(i,t.options.hint.extend);if(typeof o>"u")this.element.style.display="none",clearTimeout(this.timeId);else if(this.splitChar===":"){var d=o===""?t.options.hint.emoji:t.lute.GetEmojis(),w=[];Object.keys(d).forEach(function(C){C.indexOf(o.toLowerCase())===0&&(d[C].indexOf(".")>-1?w.push({html:'<img src="'.concat(d[C],'" title=":').concat(C,':"/> :').concat(C,":"),value:":".concat(C,":")}):w.push({html:'<span class="vditor-hint__emoji">'.concat(d[C],"</span>").concat(C),value:d[C]}))}),this.genHTML(w,o,t)}else t.options.hint.extend.forEach(function(C){C.key===n.splitChar&&(clearTimeout(n.timeId),n.timeId=window.setTimeout(function(){return yo(n,void 0,void 0,function(){var v;return bo(this,function(H){switch(H.label){case 0:return v=this.genHTML,[4,C.hint(o)];case 1:return v.apply(this,[H.sent(),o,t]),[2]}})})},t.options.hint.delay))})}},e.prototype.genHTML=function(t,n,i){var a=this;if(t.length===0){this.element.style.display="none";return}var o=i[i.currentMode].element,d=(0,I.Ny)(o),w=d.left+(i.options.outline.position==="left"?i.outline.element.offsetWidth:0),C=d.top,v="";t.forEach(function(j,Z){if(!(Z>7)){var X=j.html;if(n!==""){var G=X.lastIndexOf(">")+1,ce=X.substr(G),ne=ce.toLowerCase().indexOf(n.toLowerCase());ne>-1&&(ce=ce.substring(0,ne)+"<b>"+ce.substring(ne,ne+n.length)+"</b>"+ce.substring(ne+n.length),X=X.substr(0,G)+ce)}v+='<button type="button" data-value="'.concat(encodeURIComponent(j.value),` "
`).concat(Z===0?"class='vditor-hint--current'":"","> ").concat(X,"</button>")}}),this.element.innerHTML=v;var H=parseInt(document.defaultView.getComputedStyle(o,null).getPropertyValue("line-height"),10);this.element.style.top="".concat(C+(H||22),"px"),this.element.style.left="".concat(w,"px"),this.element.style.display="block",this.element.style.right="auto",this.element.querySelectorAll("button").forEach(function(j){j.addEventListener("click",function(Z){a.fillEmoji(j,i),Z.preventDefault()})}),this.element.getBoundingClientRect().bottom>window.innerHeight&&(this.element.style.top="".concat(C-this.element.offsetHeight,"px")),this.element.getBoundingClientRect().right>window.innerWidth&&(this.element.style.left="auto",this.element.style.right="0")},e.prototype.select=function(t,n){if(this.element.querySelectorAll("button").length===0||this.element.style.display==="none")return!1;var i=this.element.querySelector(".vditor-hint--current");if(t.key==="ArrowDown")return t.preventDefault(),t.stopPropagation(),i.removeAttribute("class"),i.nextElementSibling?i.nextElementSibling.className="vditor-hint--current":this.element.children[0].className="vditor-hint--current",!0;if(t.key==="ArrowUp"){if(t.preventDefault(),t.stopPropagation(),i.removeAttribute("class"),i.previousElementSibling)i.previousElementSibling.className="vditor-hint--current";else{var a=this.element.children.length;this.element.children[a-1].className="vditor-hint--current"}return!0}else if(!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"&&!t.isComposing)return t.preventDefault(),t.stopPropagation(),this.fillEmoji(i,n),!0;return!1},e.prototype.getKey=function(t,n){var i=this;this.lastIndex=-1,this.splitChar="",n.forEach(function(v){var H=t.lastIndexOf(v.key);i.lastIndex<H&&(i.splitChar=v.key,i.lastIndex=H)});var a;if(this.lastIndex===-1)return a;var o=t.split(this.splitChar),d=o[o.length-1],w=32;if(o.length>1&&d.trim()===d)if(o.length===2&&o[0]===""&&o[1].length<w)a=o[1];else{var C=o[o.length-2].slice(-1);(0,_.X)(C)===" "&&d.length<w&&(a=d)}return a},e})(),vo=(function(){function e(t){this.composingLock=!1;var n=document.createElement("div");n.className="vditor-ir",n.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>`),this.element=n.firstElementChild,this.bindEvent(t),kr(t,this.element),Vi(t,this.element),xr(t,this.element),Rr(t,this.element),Nr(t,this.element),Or(t,this.element),Dr(t,this.element,this.copy),Hr(t,this.element,this.copy)}return e.prototype.copy=function(t,n){var i=getSelection().getRangeAt(0);if(i.toString()!==""){t.stopPropagation(),t.preventDefault();var a=document.createElement("div");a.appendChild(i.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorIRDOM2Md(a.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(i){er(t,i,{pasteCode:function(a){document.execCommand("insertHTML",!1,a)}})}),this.element.addEventListener("scroll",function(){A(t,["hint"])}),this.element.addEventListener("compositionstart",function(i){n.composingLock=!0}),this.element.addEventListener("compositionend",function(i){(0,c.vU)()||st(t,getSelection().getRangeAt(0).cloneRange()),n.composingLock=!1}),this.element.addEventListener("input",function(i){if(!(i.inputType==="deleteByDrag"||i.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,Rt(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}n.composingLock||i.data==="‘"||i.data==="“"||i.data==="《"||st(t,getSelection().getRangeAt(0).cloneRange(),!1,i)}}),this.element.addEventListener("click",function(i){if(i.target.tagName==="INPUT"){i.target.checked?i.target.setAttribute("checked","checked"):i.target.removeAttribute("checked"),n.preventInput=!0,Rt(t);return}var a=(0,I.zh)(t),o=(0,f.fb)(i.target,"vditor-ir__preview");if(o||(o=(0,f.fb)(a.startContainer,"vditor-ir__preview")),o&&(o.previousElementSibling.firstElementChild?a.selectNodeContents(o.previousElementSibling.firstElementChild):a.selectNodeContents(o.previousElementSibling),a.collapse(!0),(0,I.Hc)(a),Ve(t)),i.target.tagName==="IMG"){var d=i.target.parentElement.querySelector(".vditor-ir__marker--link");d&&(a.selectNode(d),(0,I.Hc)(a))}var w=(0,f.a1)(i.target,"data-type","a");if(w&&!w.classList.contains("vditor-ir__node--expand")){t.options.link.click?t.options.link.click(w.querySelector(":scope > .vditor-ir__marker--link")):t.options.link.isOpen&&window.open(w.querySelector(":scope > .vditor-ir__marker--link").textContent);return}if(i.target.isEqualNode(n.element)&&n.element.lastElementChild&&a.collapsed){var C=n.element.lastElementChild.getBoundingClientRect();i.y>C.top+C.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(m.g.ZWSP,"")===""?(a.selectNodeContents(n.element.lastElementChild),a.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(m.g.ZWSP,"<wbr></p>")),(0,I.ib)(n.element,a)))}a.toString()===""?Q(a,t):setTimeout(function(){Q((0,I.zh)(t),t)}),rn(i,t),fe(t)}),this.element.addEventListener("keyup",function(i){if(!(i.isComposing||(0,c.yl)(i))){if(i.key==="Enter"&&Ve(t),fe(t),(i.key==="Backspace"||i.key==="Delete")&&t.ir.element.innerHTML!==""&&t.ir.element.childNodes.length===1&&t.ir.element.firstElementChild&&t.ir.element.firstElementChild.tagName==="P"&&t.ir.element.firstElementChild.childElementCount===0&&(t.ir.element.textContent===""||t.ir.element.textContent===`
`)){t.ir.element.innerHTML="";return}var a=(0,I.zh)(t);i.key==="Backspace"?((0,c.vU)()&&a.startContainer.textContent===`
`&&a.startOffset===1&&(a.startContainer.textContent="",Q(a,t)),n.element.querySelectorAll(".language-math").forEach(function(d){var w=d.querySelector("br");w&&w.remove()})):i.key.indexOf("Arrow")>-1?((i.key==="ArrowLeft"||i.key==="ArrowRight")&&fa(t),Q(a,t)):i.keyCode===229&&i.code===""&&i.key==="Unidentified"&&Q(a,t);var o=(0,f.fb)(a.startContainer,"vditor-ir__preview");if(o){if(i.key==="ArrowUp"||i.key==="ArrowLeft")return o.previousElementSibling.firstElementChild?a.selectNodeContents(o.previousElementSibling.firstElementChild):a.selectNodeContents(o.previousElementSibling),a.collapse(!1),i.preventDefault(),!0;if(o.tagName==="SPAN"&&(i.key==="ArrowDown"||i.key==="ArrowRight"))return o.parentElement.getAttribute("data-type")==="html-entity"?(o.parentElement.insertAdjacentText("afterend",m.g.ZWSP),a.setStart(o.parentElement.nextSibling,1)):a.selectNodeContents(o.parentElement.lastElementChild),a.collapse(!1),i.preventDefault(),!0}}})},e})(),da=function(e){if(e.currentMode==="sv")return e.lute.Md2HTML(D(e));if(e.currentMode==="wysiwyg")return e.lute.VditorDOM2HTML(e.wysiwyg.element.innerHTML);if(e.currentMode==="ir")return e.lute.VditorIRDOM2HTML(e.ir.element.innerHTML)},Eo=h(214),pa=h(436),_o=(function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-outline",this.element.innerHTML='<div class="vditor-outline__title">'.concat(t,`</div>
<div class="vditor-outline__content"></div>`)}return e.prototype.render=function(t){var n="";return t.preview.element.style.display==="block"?n=(0,pa.k)(t.preview.previewElement,this.element.lastElementChild,t):n=(0,pa.k)(t[t.currentMode].element,this.element.lastElementChild,t),n},e.prototype.toggle=function(t,n,i){var a;n===void 0&&(n=!0),i===void 0&&(i=!0);var o=(a=t.toolbar.elements.outline)===null||a===void 0?void 0:a.firstElementChild;if(n&&window.innerWidth>=m.g.MOBILE_WIDTH?(this.element.style.display="block",this.render(t),o?.classList.add("vditor-menu--current")):(this.element.style.display="none",o?.classList.remove("vditor-menu--current")),i&&getSelection().rangeCount>0){var d=getSelection().getRangeAt(0);t[t.currentMode].element.contains(d.startContainer)&&(0,I.Hc)(d)}Y(t)},e})(),So=h(280),Co=(function(){function e(t){var n=this;this.element=document.createElement("div"),this.element.className="vditor-preview",this.previewElement=document.createElement("div"),this.previewElement.className="vditor-reset",t.options.classes.preview&&this.previewElement.classList.add(t.options.classes.preview),this.previewElement.style.maxWidth=t.options.preview.maxWidth+"px",this.previewElement.addEventListener("copy",function(C){if(C.target.tagName!=="TEXTAREA"){var v=document.createElement("div");v.className="vditor-reset",v.appendChild(getSelection().getRangeAt(0).cloneContents()),n.copyToX(t,v,"default"),C.preventDefault()}}),this.previewElement.addEventListener("click",function(C){var v=(0,f.lG)(C.target,"SPAN");if(v&&(0,f.fb)(v,"vditor-toc")){var H=n.previewElement.querySelector("#"+v.getAttribute("data-target-id"));H&&(n.element.scrollTop=H.offsetTop);return}if(C.target.tagName==="A"){t.options.link.click?t.options.link.click(C.target):t.options.link.isOpen&&window.open(C.target.getAttribute("href")),C.preventDefault();return}C.target.tagName==="IMG"&&(t.options.image.preview?t.options.image.preview(C.target):t.options.image.isPreview&&(0,x.E)(C.target,t.options.lang,t.options.theme))}),this.element.appendChild(this.previewElement);var i=t.options.preview.actions;if(i.length!==0){var a=document.createElement("div");a.className="vditor-preview__action";for(var o=[],d=0;d<i.length;d++){var w=i[d];if(typeof w=="object"){o.push('<button type="button" data-type="'.concat(w.key,'" class="').concat(w.className,'"').concat(w.tooltip?' aria-label="'.concat(w.tooltip,'"'):"",'">').concat(w.text,"</button>"));continue}switch(w){case"desktop":o.push('<button type="button" class="vditor-preview__action--current" data-type="desktop">Desktop</button>');break;case"tablet":o.push('<button type="button" data-type="tablet">Tablet</button>');break;case"mobile":o.push('<button type="button" data-type="mobile">Mobile/Wechat</button>');break;case"mp-wechat":o.push('<button type="button" data-type="mp-wechat" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到公众号"><svg><use xlink:href="#vditor-icon-mp-wechat"></use></svg></button>');break;case"zhihu":o.push('<button type="button" data-type="zhihu" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到知乎"><svg><use xlink:href="#vditor-icon-zhihu"></use></svg></button>');break}}a.innerHTML=o.join(""),a.addEventListener((0,c.Le)(),function(C){var v=(0,R.S)(C.target,"BUTTON");if(v){var H=v.getAttribute("data-type"),j=i.find(function(Z){return Z?.key===H});if(j){j.click(H);return}if(H==="mp-wechat"||H==="zhihu"){n.copyToX(t,n.previewElement.cloneNode(!0),H);return}H==="desktop"?n.previewElement.style.width="auto":H==="tablet"?n.previewElement.style.width="780px":n.previewElement.style.width="360px",n.previewElement.scrollWidth>n.previewElement.parentElement.clientWidth&&(n.previewElement.style.width="auto"),n.render(t),a.querySelectorAll("button").forEach(function(Z){Z.classList.remove("vditor-preview__action--current")}),v.classList.add("vditor-preview__action--current")}}),this.element.insertBefore(a,this.previewElement)}}return e.prototype.render=function(t,n){var i=this;if(clearTimeout(this.mdTimeoutId),this.element.style.display==="none"){this.element.getAttribute("data-type")==="renderPerformance"&&t.tip.hide();return}if(n){this.previewElement.innerHTML=n;return}if(D(t).replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")===""){this.previewElement.innerHTML="";return}var a=new Date().getTime(),o=D(t);this.mdTimeoutId=window.setTimeout(function(){if(t.options.preview.url){var d=new XMLHttpRequest;d.open("POST",t.options.preview.url),d.setRequestHeader("Content-Type","application/json;charset=UTF-8"),d.onreadystatechange=function(){if(d.readyState===XMLHttpRequest.DONE)if(d.status===200){var C=JSON.parse(d.responseText);if(C.code!==0){t.tip.show(C.msg);return}t.options.preview.transform&&(C.data=t.options.preview.transform(C.data)),i.previewElement.innerHTML=C.data,i.afterRender(t,a)}else{var v=t.lute.Md2HTML(o);t.options.preview.transform&&(v=t.options.preview.transform(v)),i.previewElement.innerHTML=v,i.afterRender(t,a)}},d.send(JSON.stringify({markdownText:o}))}else{var w=t.lute.Md2HTML(o);t.options.preview.transform&&(w=t.options.preview.transform(w)),i.previewElement.innerHTML=w,i.afterRender(t,a)}},t.options.preview.delay)},e.prototype.afterRender=function(t,n){t.options.preview.parse&&t.options.preview.parse(this.element);var i=new Date().getTime()-n;new Date().getTime()-n>2600?(t.tip.show(window.VditorI18n.performanceTip.replace("${x}",i.toString())),t.preview.element.setAttribute("data-type","renderPerformance")):t.preview.element.getAttribute("data-type")==="renderPerformance"&&(t.tip.hide(),t.preview.element.removeAttribute("data-type"));var a=t.preview.element.querySelector(".vditor-comment--focus");a&&a.classList.remove("vditor-comment--focus"),(0,$.O)(t.preview.previewElement,t.options.preview.hljs),(0,ye.s)(t.options.preview.hljs,t.preview.previewElement,t.options.cdn),(0,Ae.i)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,Ce.K)(t.preview.previewElement,t.options.cdn),(0,Re.J)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,re.P)(t.preview.previewElement,t.options.cdn),(0,ue.v)(t.preview.previewElement,t.options.cdn),(0,P.p)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,ge.P)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,pe.B)(t.preview.previewElement,t.options.cdn),(0,W.Q)(t.preview.previewElement,t.options.cdn),t.options.preview.render.media.enable&&(0,So.Y)(t.preview.previewElement),t.options.customRenders.forEach(function(w){w.render(t.preview.previewElement,t)});var o=t.preview.element,d=t.outline.render(t);d===""&&(d="[ToC]"),o.querySelectorAll('[data-type="toc-block"]').forEach(function(w){w.innerHTML=d,(0,Se.H)(w,{cdn:t.options.cdn,math:t.options.preview.math})}),(0,Se.H)(t.preview.previewElement,{cdn:t.options.cdn,math:t.options.preview.math})},e.prototype.copyToX=function(t,n,i){i===void 0&&(i="mp-wechat"),i!=="zhihu"?n.querySelectorAll(".katex-html .base").forEach(function(o){o.style.display="initial"}):n.querySelectorAll(".language-math").forEach(function(o){o.outerHTML='<img class="Formula-image" data-eeimg="true" src="//www.zhihu.com/equation?tex=" alt="'.concat(o.getAttribute("data-math"),'\\" style="display: block; margin: 0 auto; max-width: 100%;">')}),n.style.backgroundColor="#fff",n.querySelectorAll("code").forEach(function(o){o.style.backgroundImage="none"}),this.element.append(n);var a=n.ownerDocument.createRange();a.selectNode(n),(0,I.Hc)(a),document.execCommand("copy"),n.remove(),t.tip.show(["zhihu","mp-wechat"].includes(i)?"已复制，可到".concat(i==="zhihu"?"知乎":"微信公众号平台","进行粘贴"):"已复制到剪切板")},e})(),To=(function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-resize vditor-resize--".concat(t.options.resize.position),this.element.innerHTML='<div><svg><use xlink:href="#vditor-icon-resize"></use></svg></div>',this.bindEvent(t)}return e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("mousedown",function(i){var a=document,o=i.clientY,d=t.element.offsetHeight,w=63+t.element.querySelector(".vditor-toolbar").clientHeight;a.ondragstart=function(){return!1},window.captureEvents&&window.captureEvents(),n.element.classList.add("vditor-resize--selected"),a.onmousemove=function(C){t.options.resize.position==="top"?t.element.style.height=Math.max(w,d+(o-C.clientY))+"px":t.element.style.height=Math.max(w,d+(C.clientY-o))+"px",t.options.typewriterMode&&(t.sv.element.style.paddingBottom=t.sv.element.parentElement.offsetHeight/2+"px")},a.onmouseup=function(){t.options.resize.after&&t.options.resize.after(t.element.offsetHeight-d),window.captureEvents&&window.captureEvents(),a.onmousemove=null,a.onmouseup=null,a.ondragstart=null,a.onselectstart=null,a.onselect=null,n.element.classList.remove("vditor-resize--selected")}})},e})(),Mo=(function(){function e(t){this.composingLock=!1,this.element=document.createElement("pre"),this.element.className="vditor-sv vditor-reset",this.element.setAttribute("placeholder",t.options.placeholder),this.element.setAttribute("contenteditable","true"),this.element.setAttribute("spellcheck","false"),this.bindEvent(t),kr(t,this.element),xr(t,this.element),Rr(t,this.element),Nr(t,this.element),Or(t,this.element),Dr(t,this.element,this.copy),Hr(t,this.element,this.copy)}return e.prototype.copy=function(t,n){t.stopPropagation(),t.preventDefault(),t.clipboardData.setData("text/plain",Sn(n[n.currentMode].element))},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(i){er(t,i,{pasteCode:function(a){document.execCommand("insertHTML",!1,a)}})}),this.element.addEventListener("scroll",function(){if(t.preview.element.style.display==="block"){var i=n.element.scrollTop,a=n.element.clientHeight,o=n.element.scrollHeight-parseFloat(n.element.style.paddingBottom||"0"),d=t.preview.element;i/a>.5?d.scrollTop=(i+a)*d.scrollHeight/o-a:d.scrollTop=i*d.scrollHeight/o}}),this.element.addEventListener("compositionstart",function(i){n.composingLock=!0}),this.element.addEventListener("compositionend",function(i){(0,c.vU)()||B(t,i),n.composingLock=!1}),this.element.addEventListener("input",function(i){if(!(i.inputType==="deleteByDrag"||i.inputType==="insertFromDrop")&&!(n.composingLock||i.data==="‘"||i.data==="“"||i.data==="《")){if(n.preventInput){n.preventInput=!1,ze(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}B(t,i)}}),this.element.addEventListener("keyup",function(i){if(!(i.isComposing||(0,c.yl)(i))){if((i.key==="Backspace"||i.key==="Delete")&&t.sv.element.innerHTML!==""&&t.sv.element.childNodes.length===1&&t.sv.element.firstElementChild&&t.sv.element.firstElementChild.tagName==="DIV"&&t.sv.element.firstElementChild.childElementCount===2&&(t.sv.element.firstElementChild.textContent===""||t.sv.element.textContent===`
`)){t.sv.element.innerHTML="";return}i.key==="Enter"&&Ve(t)}})},e})(),ha=(function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-tip"}return e.prototype.show=function(t,n){var i=this;n===void 0&&(n=6e3),this.element.className="vditor-tip vditor-tip--show",n===0?(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,`
<div class="vditor-tip__close">X</div></div>`),this.element.querySelector(".vditor-tip__close").addEventListener("click",function(){i.hide()})):(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,"</div>"),setTimeout(function(){i.hide()},n)),this.element.removeAttribute("style"),setTimeout(function(){var a=i.element.getBoundingClientRect();a.top<46&&(i.element.style.position="fixed",i.element.style.top="46px")},150)},e.prototype.hide=function(){this.element.className="vditor-messageElementtip",this.element.innerHTML=""},e})(),Fr=function(e,t){if(t.options.preview.mode!==e){switch(t.options.preview.mode=e,e){case"both":t.sv.element.style.display="block",t.preview.element.style.display="block",t.preview.render(t),g(t.toolbar.elements,["both"]);break;case"editor":t.sv.element.style.display="block",t.preview.element.style.display="none",b(t.toolbar.elements,["both"]);break}t.devtools&&t.devtools.renderEchart(t)}},Lo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Ao=(function(e){Lo(t,e);function t(n,i){var a=e.call(this,n,i)||this;return n.options.preview.mode==="both"&&a.element.children[0].classList.add("vditor-menu--current"),a.element.children[0].addEventListener((0,c.Le)(),function(o){var d=a.element.firstElementChild;d.classList.contains(m.g.CLASS_MENU_DISABLED)||(o.preventDefault(),n.currentMode==="sv"&&(n.options.preview.mode==="both"?Fr("editor",n):Fr("both",n)))}),a}return t})(Ue),ko=(function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__br"}return e})(),ma=h(580),xo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Oo=(function(e){xo(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=a.element.children[0],d=document.createElement("div");d.className="vditor-hint".concat(i.level===2?"":" vditor-panel--arrow");var w="";return m.g.CODE_THEME.forEach(function(C){w+="<button>".concat(C,"</button>")}),d.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(w,"</div>"),d.addEventListener((0,c.Le)(),function(C){C.target.tagName==="BUTTON"&&(A(n,["subToolbar"]),n.options.preview.hljs.style=C.target.textContent,(0,ma.Y)(C.target.textContent,n.options.cdn),C.preventDefault(),C.stopPropagation())}),a.element.appendChild(d),T(n,d,o,i.level),a}return t})(Ue),Do=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Ho=(function(e){Do(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=a.element.children[0],d=document.createElement("div");d.className="vditor-hint".concat(i.level===2?"":" vditor-panel--arrow");var w="";return Object.keys(n.options.preview.theme.list).forEach(function(C){w+='<button data-type="'.concat(C,'">').concat(n.options.preview.theme.list[C],"</button>")}),d.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(w,"</div>"),d.addEventListener((0,c.Le)(),function(C){C.target.tagName==="BUTTON"&&(A(n,["subToolbar"]),n.options.preview.theme.current=C.target.getAttribute("data-type"),(0,U.Z)(n.options.preview.theme.current,n.options.preview.theme.path),C.preventDefault(),C.stopPropagation())}),a.element.appendChild(d),T(n,d,o,i.level),a}return t})(Ue),Ro=(function(){function e(t){this.element=document.createElement("span"),this.element.className="vditor-counter vditor-tooltipped vditor-tooltipped__nw",this.render(t,"")}return e.prototype.render=function(t,n){var i=n.endsWith(`
`)?n.length-1:n.length;if(t.options.counter.type==="text"&&t[t.currentMode]){var a=t[t.currentMode].element.cloneNode(!0);a.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(o){o.remove()}),i=a.textContent.length}typeof t.options.counter.max=="number"?(i>t.options.counter.max?this.element.className="vditor-counter vditor-counter--error":this.element.className="vditor-counter",this.element.innerHTML="".concat(i,"/").concat(t.options.counter.max)):this.element.innerHTML="".concat(i),this.element.setAttribute("aria-label",t.options.counter.type),t.options.counter.after&&t.options.counter.after(i,{enable:t.options.counter.enable,max:t.options.counter.max,type:t.options.counter.type})},e})(),No=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Io=(function(e){No(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].innerHTML=i.icon,a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!o.currentTarget.classList.contains(m.g.CLASS_MENU_DISABLED)&&i.click(o,n)}),a}return t})(Ue),Po=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),jo=(function(e){Po(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.firstElementChild.addEventListener((0,c.Le)(),function(o){var d=a.element.firstElementChild;d.classList.contains(m.g.CLASS_MENU_DISABLED)||(o.preventDefault(),d.classList.contains("vditor-menu--current")?(d.classList.remove("vditor-menu--current"),n.devtools.element.style.display="none",Y(n)):(d.classList.add("vditor-menu--current"),n.devtools.element.style.display="block",Y(n),n.devtools.renderEchart(n)))}),a}return t})(Ue),Bo=(function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__divider"}return e})(),Uo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Vo=(function(e){Uo(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=document.createElement("div");o.className="vditor-panel vditor-panel--arrow";var d="";return Object.keys(n.options.hint.emoji).forEach(function(w){var C=n.options.hint.emoji[w];C.indexOf(".")>-1?d+='<button data-value=":'.concat(w,': " data-key=":').concat(w,`:"><img
data-value=":`).concat(w,': " data-key=":').concat(w,':" class="vditor-emojis__icon" src="').concat(C,'"/></button>'):d+='<button data-value="'.concat(C,` "
 data-key="`).concat(w,'"><span class="vditor-emojis__icon">').concat(C,"</span></button>")}),o.innerHTML='<div class="vditor-emojis" style="max-height: '.concat(n.options.height==="auto"?"auto":n.options.height-80,'px">').concat(d,`</div><div class="vditor-emojis__tail">
    <span class="vditor-emojis__tip"></span><span>`).concat(n.options.hint.emojiTail||"",`</span>
</div>`),a.element.appendChild(o),T(n,o,a.element.firstElementChild,i.level),a.bindEvent(n),a}return t.prototype.bindEvent=function(n){var i=this;this.element.lastElementChild.addEventListener((0,c.Le)(),function(a){var o=(0,R.S)(a.target,"BUTTON");if(o){a.preventDefault();var d=o.getAttribute("data-value"),w=(0,I.zh)(n),C=d;if(n.currentMode==="wysiwyg"?C=n.lute.SpinVditorDOM(d):n.currentMode==="ir"&&(C=n.lute.SpinVditorIRDOM(d)),d.indexOf(":")>-1&&n.currentMode!=="sv"){var v=document.createElement("div");v.innerHTML=C,C=v.firstElementChild.firstElementChild.outerHTML+" ",(0,I.oC)(C,n)}else w.extractContents(),w.insertNode(document.createTextNode(d)),(0,f.F9)(w.startContainer)||ot(n,w);w.collapse(!1),(0,I.Hc)(w),i.element.lastElementChild.style.display="none",Ee(n)}}),this.element.lastElementChild.addEventListener("mouseover",function(a){var o=(0,R.S)(a.target,"BUTTON");o&&(i.element.querySelector(".vditor-emojis__tip").innerHTML=o.getAttribute("data-key"))})},t})(Ue),ga=function(e,t,n){var i=document.createElement("a");"download"in i?(i.download=n,i.style.display="none",i.href=URL.createObjectURL(new Blob([t])),document.body.appendChild(i),i.click(),i.remove()):e.tip.show(window.VditorI18n.downloadTip,0)},Fo=function(e){var t=D(e);ga(e,t,t.substr(0,10)+".md")},Wo=function(e){e.tip.show(window.VditorI18n.generate,3800);var t=document.querySelector("#vditorExportIframe");t.contentDocument.open(),t.contentDocument.write('<link rel="stylesheet" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script>
<div id="preview" style="width: 800px"></div>
<script>
window.addEventListener("message", (e) => {
  if(!e.data) {
    return;
  }
  Vditor.preview(document.getElementById('preview'), e.data, {
    cdn: "`).concat(e.options.cdn,`",
    markdown: {
      theme: `).concat(JSON.stringify(e.options.preview.theme),`
    },
    hljs: {
      style: "`).concat(e.options.preview.hljs.style,`"
    }
  });
  setTimeout(() => {
        window.print();
    }, 3600);
}, false);
<\/script>`)),t.contentDocument.close(),setTimeout(function(){t.contentWindow.postMessage(D(e),"*")},200)},Ko=function(e){var t=da(e),n='<html><head><link rel="stylesheet" type="text/css" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,"/dist/js/i18n/").concat(e.options.lang,`.js"><\/script>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script></head>
<body><div class="vditor-reset" id="preview">`).concat(t,`</div>
<script>
    const previewElement = document.getElementById('preview')
    Vditor.setContentTheme('`).concat(e.options.preview.theme.current,"', '").concat(e.options.preview.theme.path,`');
    Vditor.codeRender(previewElement);
    Vditor.highlightRender(`).concat(JSON.stringify(e.options.preview.hljs),", previewElement, '").concat(e.options.cdn,`');
    Vditor.mathRender(previewElement, {
        cdn: '`).concat(e.options.cdn,`',
        math: `).concat(JSON.stringify(e.options.preview.math),`,
    });
    Vditor.mermaidRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.SMILESRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.markmapRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.flowchartRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.graphvizRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.chartRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.mindmapRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.abcRender(previewElement, '`).concat(e.options.cdn,`');
    `).concat(e.options.preview.render.media.enable?"Vditor.mediaRender(previewElement);":"",`
    Vditor.speechRender(previewElement);
<\/script>
<script src="`).concat(e.options.cdn,"/dist/js/icons/").concat(e.options.icon,'.js"><\/script></body></html>');ga(e,n,t.substr(0,10)+".html")},qo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),zo=(function(e){qo(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=a.element.children[0],d=document.createElement("div");return d.className="vditor-hint".concat(i.level===2?"":" vditor-panel--arrow"),d.innerHTML=`<button data-type="markdown">Markdown</button>
<button data-type="pdf">PDF</button>
<button data-type="html">HTML</button>`,d.addEventListener((0,c.Le)(),function(w){var C=w.target;if(C.tagName==="BUTTON"){switch(C.getAttribute("data-type")){case"markdown":Fo(n);break;case"pdf":Wo(n);break;case"html":Ko(n);break}A(n,["subToolbar"]),w.preventDefault(),w.stopPropagation()}}),a.element.appendChild(d),T(n,d,o,i.level),a}return t})(Ue),Go=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),$o=(function(e){Go(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a._bindEvent(n,i),a}return t.prototype._bindEvent=function(n,i){this.element.children[0].addEventListener((0,c.Le)(),function(a){a.preventDefault(),n.element.className.includes("vditor--fullscreen")?(i.level||(this.innerHTML=i.icon),n.element.style.zIndex="",document.body.style.overflow="",n.element.classList.remove("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(o){var d=n.toolbar.elements[o].firstChild;d&&(d.className=d.className.replace("__s","__n"),n.options.toolbar.forEach(function(w){typeof w!="string"&&w.tipPosition&&w.name===d.dataset.type&&(d.className="vditor-tooltipped vditor-tooltipped__".concat(w.tipPosition))}))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__s","__n"))):(i.level||(this.innerHTML='<svg><use xlink:href="#vditor-icon-contract"></use></svg>'),n.element.style.zIndex=n.options.fullscreen.index.toString(),document.body.style.overflow="hidden",n.element.classList.add("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(o){var d=n.toolbar.elements[o].firstChild;d&&(d.className=d.className.replace("__n","__s"))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__n","__s"))),n.devtools&&n.devtools.renderEchart(n),i.click&&i.click(a,n),Y(n),te(n)})},t})(Ue),Jo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Zo=(function(e){Jo(t,e);function t(n,i){var a=e.call(this,n,i)||this,o=document.createElement("div");return o.className="vditor-hint vditor-panel--arrow",o.innerHTML='<button data-tag="h1" data-value="# ">'.concat(window.VditorI18n.heading1," ").concat((0,c.ns)("&lt;⌥⌘1>"),`</button>
<button data-tag="h2" data-value="## ">`).concat(window.VditorI18n.heading2," &lt;").concat((0,c.ns)("⌥⌘2"),`></button>
<button data-tag="h3" data-value="### ">`).concat(window.VditorI18n.heading3," &lt;").concat((0,c.ns)("⌥⌘3"),`></button>
<button data-tag="h4" data-value="#### ">`).concat(window.VditorI18n.heading4," &lt;").concat((0,c.ns)("⌥⌘4"),`></button>
<button data-tag="h5" data-value="##### ">`).concat(window.VditorI18n.heading5," &lt;").concat((0,c.ns)("⌥⌘5"),`></button>
<button data-tag="h6" data-value="###### ">`).concat(window.VditorI18n.heading6," &lt;").concat((0,c.ns)("⌥⌘6"),"></button>"),a.element.appendChild(o),a._bindEvent(n,o),a}return t.prototype._bindEvent=function(n,i){var a=this.element.children[0];a.addEventListener((0,c.Le)(),function(d){d.preventDefault(),clearTimeout(n.wysiwyg.afterRenderTimeoutId),clearTimeout(n.ir.processTimeoutId),clearTimeout(n.sv.processTimeoutId),!a.classList.contains(m.g.CLASS_MENU_DISABLED)&&(a.blur(),a.classList.contains("vditor-menu--current")?(n.currentMode==="wysiwyg"?(sn(n),ae(n)):n.currentMode==="ir"&&Mn(n,""),a.classList.remove("vditor-menu--current")):(A(n,["subToolbar"]),i.style.display="block"))});for(var o=0;o<6;o++)i.children.item(o).addEventListener((0,c.Le)(),function(d){d.preventDefault(),n.currentMode==="wysiwyg"?(Tt(n,d.target.getAttribute("data-tag")),ae(n),a.classList.add("vditor-menu--current")):n.currentMode==="ir"?(Mn(n,d.target.getAttribute("data-value")),a.classList.add("vditor-menu--current")):Wi(n,d.target.getAttribute("data-value")),i.style.display="none"})},t})(Ue),Xo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),Yo=(function(e){Xo(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),n.tip.show(`<div style="margin-bottom:14px;font-size: 14px;line-height: 22px;min-width:300px;max-width: 360px;display: flex;">
<div style="margin-top: 14px;flex: 1">
    <div>Markdown 使用指南</div>
    <ul style="list-style: none">
        <li><a href="https://ld246.com/article/1583308420519" target="_blank">语法速查手册</a></li>
        <li><a href="https://ld246.com/article/1583129520165" target="_blank">基础语法</a></li>
        <li><a href="https://ld246.com/article/1583305480675" target="_blank">扩展语法</a></li>
        <li><a href="https://ld246.com/article/1582778815353" target="_blank">键盘快捷键</a></li>
    </ul>
</div>
<div style="margin-top: 14px;flex: 1">
    <div>Vditor 支持</div>
    <ul style="list-style: none">
        <li><a href="https://github.com/Vanessa219/vditor/issues" target="_blank">Issues</a></li>
        <li><a href="https://ld246.com/tag/vditor" target="_blank">官方讨论区</a></li>
        <li><a href="https://ld246.com/article/1549638745630" target="_blank">开发手册</a></li>
        <li><a href="https://ld246.com/guide/markdown" target="_blank">演示地址</a></li>
    </ul>
</div></div>`,0)}),a}return t})(Ue),Qo=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),el=(function(e){Qo(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!(a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var d=(0,I.zh)(n),w=(0,f.lG)(d.startContainer,"LI");w&&Zi(n,w,d)}}),a}return t})(Ue),tl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),nl=(function(e){tl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),n.tip.show(`<div style="max-width: 520px; font-size: 14px;line-height: 22px;margin-bottom: 14px;">
<p style="text-align: center;margin: 14px 0">
    <em>下一代的 Markdown 编辑器，为未来而构建</em>
</p>
<div style="display: flex;margin-bottom: 14px;flex-wrap: wrap;align-items: center">
    <img src="https://unpkg.com/vditor/dist/images/logo.png" style="margin: 0 auto;height: 68px"/>
    <div>&nbsp;&nbsp;</div>
    <div style="flex: 1;min-width: 250px">
        Vditor 是一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。
        它使用 TypeScript 实现，支持原生 JavaScript 以及 Vue、React、Angular 和 Svelte 等框架。
    </div>
</div>
<div style="display: flex;flex-wrap: wrap;">
    <ul style="list-style: none;flex: 1;min-width:148px">
        <li>
        项目地址：<a href="https://b3log.org/vditor" target="_blank">b3log.org/vditor</a>
        </li>
        <li>
        开源协议：MIT
        </li>
    </ul>
    <ul style="list-style: none;margin-right: 18px">
        <li>
        组件版本：Vditor v`.concat(m.H," / Lute v").concat(Lute.Version,`
        </li>
        <li>
        赞助捐赠：<a href="https://ld246.com/sponsor" target="_blank">https://ld246.com/sponsor</a>
        </li>
    </ul>
</div>
</div>`),0)}),a}return t})(Ue),rl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),il=(function(e){rl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!(a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&Cn(n,"afterend")}),a}return t})(Ue),al=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),sl=(function(e){al(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!(a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&Cn(n,"beforebegin")}),a}return t})(Ue),ol=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),ll=(function(e){ol(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!(a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var d=(0,I.zh)(n),w=(0,f.lG)(d.startContainer,"LI");w&&jr(n,w,d,w.parentElement)}}),a}return t})(Ue),cl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),ul=(function(e){cl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return n.options.outline&&a.element.firstElementChild.classList.add("vditor-menu--current"),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault();var d=n.toolbar.elements.outline.firstElementChild;d.classList.contains(m.g.CLASS_MENU_DISABLED)||(n.options.outline.enable=!a.element.firstElementChild.classList.contains("vditor-menu--current"),n.outline.toggle(n,n.options.outline.enable))}),a}return t})(Ue),fl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),dl=(function(e){fl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var i=this;this.element.children[0].addEventListener((0,c.Le)(),function(a){a.preventDefault();var o=i.element.firstElementChild;if(!o.classList.contains(m.g.CLASS_MENU_DISABLED)){var d=m.g.EDIT_TOOLBARS.concat(["both","edit-mode","devtools"]);o.classList.contains("vditor-menu--current")?(o.classList.remove("vditor-menu--current"),n.currentMode==="sv"?(n.sv.element.style.display="block",n.options.preview.mode==="both"?n.preview.element.style.display="block":n.preview.element.style.display="none"):(n[n.currentMode].element.parentElement.style.display="block",n.preview.element.style.display="none"),E(n.toolbar.elements,d),n.outline.render(n)):(y(n.toolbar.elements,d),n.preview.element.style.display="block",n.currentMode==="sv"?n.sv.element.style.display="none":n[n.currentMode].element.parentElement.style.display="none",n.preview.render(n),o.classList.add("vditor-menu--current"),A(n,["subToolbar","hint","popover"]),setTimeout(function(){n.outline.render(n)},n.options.preview.delay+10)),Y(n)}})},t})(Ue),pl=(function(){function e(t){this.SAMPLE_RATE=5e3,this.isRecording=!1,this.readyFlag=!1,this.leftChannel=[],this.rightChannel=[],this.recordingLength=0;var n;if(typeof AudioContext<"u")n=new AudioContext;else if(webkitAudioContext)n=new webkitAudioContext;else return;this.DEFAULT_SAMPLE_RATE=n.sampleRate;var i=n.createGain(),a=n.createMediaStreamSource(t);a.connect(i),this.recorder=n.createScriptProcessor(2048,2,1),this.recorder.onaudioprocess=null,i.connect(this.recorder),this.recorder.connect(n.destination),this.readyFlag=!0}return e.prototype.cloneChannelData=function(t,n){this.leftChannel.push(new Float32Array(t)),this.rightChannel.push(new Float32Array(n)),this.recordingLength+=2048},e.prototype.startRecordingNewWavFile=function(){this.readyFlag&&(this.isRecording=!0,this.leftChannel.length=this.rightChannel.length=0,this.recordingLength=0)},e.prototype.stopRecording=function(){this.isRecording=!1},e.prototype.buildWavFileBlob=function(){for(var t=this.mergeBuffers(this.leftChannel),n=this.mergeBuffers(this.rightChannel),i=new Float32Array(t.length),a=0;a<t.length;++a)i[a]=.5*(t[a]+n[a]);this.DEFAULT_SAMPLE_RATE>this.SAMPLE_RATE&&(i=this.downSampleBuffer(i,this.SAMPLE_RATE));var o=44+i.length*2,d=new ArrayBuffer(o),w=new DataView(d);this.writeUTFBytes(w,0,"RIFF"),w.setUint32(4,o,!0),this.writeUTFBytes(w,8,"WAVE"),this.writeUTFBytes(w,12,"fmt "),w.setUint32(16,16,!0),w.setUint16(20,1,!0),w.setUint16(22,1,!0),w.setUint32(24,this.SAMPLE_RATE,!0),w.setUint32(28,this.SAMPLE_RATE*2,!0),w.setUint16(32,2,!0),w.setUint16(34,16,!0);var C=i.length*2;this.writeUTFBytes(w,36,"data"),w.setUint32(40,C,!0);for(var v=i.length,H=44,j=1,Z=0;Z<v;Z++)w.setInt16(H,i[Z]*(32767*j),!0),H+=2;return new Blob([w],{type:"audio/wav"})},e.prototype.downSampleBuffer=function(t,n){if(n===this.DEFAULT_SAMPLE_RATE||n>this.DEFAULT_SAMPLE_RATE)return t;for(var i=this.DEFAULT_SAMPLE_RATE/n,a=Math.round(t.length/i),o=new Float32Array(a),d=0,w=0;d<o.length;){for(var C=Math.round((d+1)*i),v=0,H=0,j=w;j<C&&j<t.length;j++)v+=t[j],H++;o[d]=v/H,d++,w=C}return o},e.prototype.mergeBuffers=function(t){for(var n=new Float32Array(this.recordingLength),i=0,a=t.length,o=0;o<a;++o){var d=t[o];n.set(d,i),i+=d.length}return n},e.prototype.writeUTFBytes=function(t,n,i){for(var a=i.length,o=0;o<a;o++)t.setUint8(n+o,i.charCodeAt(o))},e})(),hl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),ml=(function(e){hl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var i=this,a;this.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!i.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)){var d=n[n.currentMode].element;if(!a){navigator.mediaDevices.getUserMedia({audio:!0}).then(function(C){a=new pl(C),a.recorder.onaudioprocess=function(v){if(a.isRecording){var H=v.inputBuffer.getChannelData(0),j=v.inputBuffer.getChannelData(1);a.cloneChannelData(H,j)}},a.startRecordingNewWavFile(),n.tip.show(window.VditorI18n.recording),d.setAttribute("contenteditable","false"),i.element.children[0].classList.add("vditor-menu--current")}).catch(function(){n.tip.show(window.VditorI18n["record-tip"])});return}if(a.isRecording){a.stopRecording(),n.tip.hide();var w=new File([a.buildWavFileBlob()],"record".concat(new Date().getTime(),".wav"),{type:"video/webm"});Pr(n,[w]),i.element.children[0].classList.remove("vditor-menu--current")}else n.tip.show(window.VditorI18n.recording),d.setAttribute("contenteditable","false"),a.startRecordingNewWavFile(),i.element.children[0].classList.add("vditor-menu--current")}})},t})(Ue),gl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),yl=(function(e){gl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return y({redo:a.element},["redo"]),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)&&n.undo.redo(n)}),a}return t})(Ue),bl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),wl=(function(e){bl(t,e);function t(n,i){var a=e.call(this,n,i)||this;return y({undo:a.element},["undo"]),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!a.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)&&n.undo.undo(n)}),a}return t})(Ue),vl=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),El=(function(e){vl(t,e);function t(n,i){var a=e.call(this,n,i)||this,o='<input type="file"';return n.options.upload.multiple&&(o+=' multiple="multiple"'),n.options.upload.accept&&(o+=' accept="'.concat(n.options.upload.accept,'"')),a.element.children[0].innerHTML="".concat(i.icon||'<svg><use xlink:href="#vditor-icon-upload"></use></svg>').concat(o,">"),a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var i=this;this.element.children[0].addEventListener((0,c.Le)(),function(a){if(i.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)){a.stopPropagation(),a.preventDefault();return}}),this.element.querySelector("input").addEventListener("change",function(a){if(i.element.firstElementChild.classList.contains(m.g.CLASS_MENU_DISABLED)){a.stopPropagation(),a.preventDefault();return}a.target.files.length!==0&&Pr(n,a.target.files,a.target)})},t})(Ue),_l=(function(){function e(t){var n=this,i=t.options;this.elements={},this.element=document.createElement("div"),this.element.className="vditor-toolbar",i.toolbar.forEach(function(a,o){var d=n.genItem(t,a,o);if(n.element.appendChild(d),a.toolbar){var w=document.createElement("div");w.className="vditor-hint vditor-panel--arrow",w.addEventListener((0,c.Le)(),function(C){w.style.display="none"}),a.toolbar.forEach(function(C,v){C.level=2,w.appendChild(n.genItem(t,C,o+v))}),d.appendChild(w),T(t,w,d.children[0])}}),t.options.toolbarConfig.hide&&this.element.classList.add("vditor-toolbar--hide"),t.options.toolbarConfig.pin&&this.element.classList.add("vditor-toolbar--pin"),t.options.counter.enable&&(t.counter=new Ro(t),this.element.appendChild(t.counter.element))}return e.prototype.updateConfig=function(t,n){t.options.toolbarConfig=Object.assign({hide:!1,pin:!1},n),t.options.toolbarConfig.hide?this.element.classList.add("vditor-toolbar--hide"):this.element.classList.remove("vditor-toolbar--hide"),t.options.toolbarConfig.pin?this.element.classList.add("vditor-toolbar--pin"):this.element.classList.remove("vditor-toolbar--pin")},e.prototype.genItem=function(t,n,i){var a;switch(n.name){case"bold":case"italic":case"more":case"strike":case"line":case"quote":case"list":case"ordered-list":case"check":case"code":case"inline-code":case"link":case"table":a=new Ue(t,n);break;case"emoji":a=new Vo(t,n);break;case"headings":a=new Zo(t,n);break;case"|":a=new Bo;break;case"br":a=new ko;break;case"undo":a=new wl(t,n);break;case"redo":a=new yl(t,n);break;case"help":a=new Yo(t,n);break;case"both":a=new Ao(t,n);break;case"preview":a=new dl(t,n);break;case"fullscreen":a=new $o(t,n);break;case"upload":a=new El(t,n);break;case"record":a=new ml(t,n);break;case"info":a=new nl(t,n);break;case"edit-mode":a=new no(t,n);break;case"devtools":a=new jo(t,n);break;case"outdent":a=new ll(t,n);break;case"indent":a=new el(t,n);break;case"outline":a=new ul(t,n);break;case"insert-after":a=new il(t,n);break;case"insert-before":a=new sl(t,n);break;case"code-theme":a=new Oo(t,n);break;case"content-theme":a=new Ho(t,n);break;case"export":a=new zo(t,n);break;default:a=new Io(t,n);break}if(a){var o=n.name;return(o==="br"||o==="|")&&(o=o+i),this.elements[o]=a.element,a.element}},e})(),Sl=h(173),Cl=(function(){function e(){this.stackSize=50,this.resetStack(),this.dmp=new Sl}return e.prototype.clearStack=function(t){this.resetStack(),this.resetIcon(t)},e.prototype.resetIcon=function(t){t.toolbar&&(this[t.currentMode].undoStack.length>1?E(t.toolbar.elements,["undo"]):y(t.toolbar.elements,["undo"]),this[t.currentMode].redoStack.length!==0?E(t.toolbar.elements,["redo"]):y(t.toolbar.elements,["redo"]))},e.prototype.undo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"&&!(this[t.currentMode].undoStack.length<2)){var n=this[t.currentMode].undoStack.pop();n&&(this[t.currentMode].redoStack.push(n),this.renderDiff(n,t),this[t.currentMode].hasUndo=!0,A(t,["hint"]))}},e.prototype.redo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"){var n=this[t.currentMode].redoStack.pop();n&&(this[t.currentMode].undoStack.push(n),this.renderDiff(n,t,!0))}},e.prototype.recordFirstPosition=function(t,n){if(getSelection().rangeCount!==0&&!(this[t.currentMode].undoStack.length!==1||this[t.currentMode].undoStack[0].length===0||this[t.currentMode].redoStack.length>0)&&!((0,c.vU)()&&n.key==="Backspace")&&!(0,c.G6)()){var i=this.addCaret(t);i.replace("<wbr>","").replace(" vditor-ir__node--expand","")===this[t.currentMode].undoStack[0][0].diffs[0][1].replace("<wbr>","")&&(this[t.currentMode].undoStack[0][0].diffs[0][1]=i,this[t.currentMode].lastText=i)}},e.prototype.addToUndoStack=function(t){var n=this.addCaret(t,!0),i=this.dmp.diff_main(n,this[t.currentMode].lastText,!0),a=this.dmp.patch_make(n,this[t.currentMode].lastText,i);a.length===0&&this[t.currentMode].undoStack.length>0||(this[t.currentMode].lastText=n,this[t.currentMode].undoStack.push(a),this[t.currentMode].undoStack.length>this.stackSize&&this[t.currentMode].undoStack.shift(),this[t.currentMode].hasUndo&&(this[t.currentMode].redoStack=[],this[t.currentMode].hasUndo=!1,y(t.toolbar.elements,["redo"])),this[t.currentMode].undoStack.length>1&&E(t.toolbar.elements,["undo"]))},e.prototype.renderDiff=function(t,n,i){i===void 0&&(i=!1);var a;if(i){var o=this.dmp.patch_deepCopy(t).reverse();o.forEach(function(w){w.diffs.forEach(function(C){C[0]=-C[0]})}),a=this.dmp.patch_apply(o,this[n.currentMode].lastText)[0]}else a=this.dmp.patch_apply(t,this[n.currentMode].lastText)[0];if(this[n.currentMode].lastText=a,n[n.currentMode].element.innerHTML=a,n.currentMode!=="sv"&&(n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview")).forEach(function(w){w.parentElement.querySelector(".language-echarts")&&(n.currentMode==="ir"?w.parentElement.outerHTML=n.lute.SpinVditorIRDOM(w.parentElement.outerHTML):w.parentElement.outerHTML=n.lute.SpinVditorDOM(w.parentElement.outerHTML))}),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(w){Ne(w,n)})),n[n.currentMode].element.querySelector("wbr"))(0,I.ib)(n[n.currentMode].element,n[n.currentMode].element.ownerDocument.createRange()),Ve(n);else{var d=getSelection().getRangeAt(0);d.setEndBefore(n[n.currentMode].element),d.collapse(!1)}dt(n),Ee(n,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),cn(n),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(w){Ne(w,n)}),this[n.currentMode].undoStack.length>1?E(n.toolbar.elements,["undo"]):y(n.toolbar.elements,["undo"]),this[n.currentMode].redoStack.length!==0?E(n.toolbar.elements,["redo"]):y(n.toolbar.elements,["redo"])},e.prototype.resetStack=function(){this.ir={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.sv={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.wysiwyg={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]}},e.prototype.addCaret=function(t,n){n===void 0&&(n=!1);var i;if(getSelection().rangeCount!==0&&!t[t.currentMode].element.querySelector("wbr")){var a=getSelection().getRangeAt(0);if(t[t.currentMode].element.contains(a.startContainer)){i=a.cloneRange();var o=document.createElement("span");o.className="vditor-wbr",a.insertNode(o)}}var d=t[t.currentMode].element.cloneNode(!0);d.querySelectorAll(".vditor-".concat(t.currentMode,"__preview[data-render='1']")).forEach(function(C){C.firstElementChild&&(C.firstElementChild.classList.contains("language-echarts")||C.firstElementChild.classList.contains("language-plantuml")||C.firstElementChild.classList.contains("language-mindmap")?(C.firstElementChild.removeAttribute("_echarts_instance_"),C.firstElementChild.removeAttribute("data-processed"),C.firstElementChild.innerHTML=C.previousElementSibling.firstElementChild.innerHTML,C.setAttribute("data-render","2")):C.firstElementChild.classList.contains("language-math")&&(C.setAttribute("data-render","2"),C.firstElementChild.textContent=C.firstElementChild.getAttribute("data-math"),C.firstElementChild.removeAttribute("data-math")))});var w=d.innerHTML;return t[t.currentMode].element.querySelectorAll(".vditor-wbr").forEach(function(C){C.remove()}),n&&i&&(0,I.Hc)(i),w.replace('<span class="vditor-wbr"></span>',"<wbr>")},e})(),Tl=h(673),Ml=(function(){function e(t){this.defaultOptions={rtl:!1,after:void 0,cache:{enable:!0},cdn:m.g.CDN,classes:{preview:""},comment:{enable:!1},counter:{enable:!1,type:"markdown"},customRenders:[],debugger:!1,fullscreen:{index:90},height:"auto",hint:{delay:200,emoji:{"+1":"👍","-1":"👎",confused:"😕",eyes:"👀️",heart:"❤️",rocket:"🚀️",smile:"😄",tada:"🎉️"},emojiPath:"".concat(m.g.CDN,"/dist/images/emoji"),extend:[],parse:!0},icon:"ant",lang:"zh_CN",mode:"ir",outline:{enable:!1,position:"left"},placeholder:"",preview:{actions:["desktop","tablet","mobile","mp-wechat","zhihu"],delay:1e3,hljs:m.g.HLJS_OPTIONS,markdown:m.g.MARKDOWN_OPTIONS,math:m.g.MATH_OPTIONS,maxWidth:800,mode:"both",theme:m.g.THEME_OPTIONS,render:{media:{enable:!0}}},link:{isOpen:!0},image:{isPreview:!0},resize:{enable:!1,position:"bottom"},theme:"classic",toolbar:["emoji","headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","quote","line","code","inline-code","insert-before","insert-after","|","upload","record","table","|","undo","redo","|","fullscreen","edit-mode",{name:"more",toolbar:["both","code-theme","content-theme","export","outline","preview","devtools","info","help"]}],toolbarConfig:{hide:!1,pin:!1},typewriterMode:!1,undoDelay:800,upload:{extraData:{},fieldName:"file[]",filename:function(n){return n.replace(/\W/g,"")},linkToImgUrl:"",max:10*1024*1024,multiple:!0,url:"",withCredentials:!1},value:"",width:"auto"},this.options=t}return e.prototype.merge=function(){var t,n,i,a,o,d,w,C,v;this.options&&(this.options.toolbar?this.options.toolbar=this.mergeToolbar(this.options.toolbar):this.options.toolbar=this.mergeToolbar(this.defaultOptions.toolbar),!((n=(t=this.options.preview)===null||t===void 0?void 0:t.theme)===null||n===void 0)&&n.list&&(this.defaultOptions.preview.theme.list=this.options.preview.theme.list),!((o=(a=(i=this.options.preview)===null||i===void 0?void 0:i.render)===null||a===void 0?void 0:a.media)===null||o===void 0)&&o.enable&&(this.defaultOptions.preview.render.media.enable=this.options.preview.render.media.enable),!((d=this.options.hint)===null||d===void 0)&&d.emoji&&(this.defaultOptions.hint.emoji=this.options.hint.emoji),this.options.comment&&(this.defaultOptions.comment=this.options.comment),this.options.cdn&&(!((C=(w=this.options.preview)===null||w===void 0?void 0:w.theme)===null||C===void 0)&&C.path||(this.defaultOptions.preview.theme.path="".concat(this.options.cdn,"/dist/css/content-theme")),!((v=this.options.hint)===null||v===void 0)&&v.emojiPath||(this.defaultOptions.hint.emojiPath="".concat(this.options.cdn,"/dist/images/emoji"))));var H=(0,Tl.T)(this.defaultOptions,this.options);if(H.cache.enable&&!H.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");return H},e.prototype.mergeToolbar=function(t){var n=this,i=[{icon:'<svg><use xlink:href="#vditor-icon-export"></use></svg>',name:"export",tipPosition:"ne"},{hotkey:"⌘E",icon:'<svg><use xlink:href="#vditor-icon-emoji"></use></svg>',name:"emoji",tipPosition:"ne"},{hotkey:"⌘H",icon:'<svg><use xlink:href="#vditor-icon-headings"></use></svg>',name:"headings",tipPosition:"ne"},{hotkey:"⌘B",icon:'<svg><use xlink:href="#vditor-icon-bold"></use></svg>',name:"bold",prefix:"**",suffix:"**",tipPosition:"ne"},{hotkey:"⌘I",icon:'<svg><use xlink:href="#vditor-icon-italic"></use></svg>',name:"italic",prefix:"*",suffix:"*",tipPosition:"ne"},{hotkey:"⌘D",icon:'<svg><use xlink:href="#vditor-icon-strike"></use></svg>',name:"strike",prefix:"~~",suffix:"~~",tipPosition:"ne"},{hotkey:"⌘K",icon:'<svg><use xlink:href="#vditor-icon-link"></use></svg>',name:"link",prefix:"[",suffix:"](https://)",tipPosition:"n"},{name:"|"},{hotkey:"⌘L",icon:'<svg><use xlink:href="#vditor-icon-list"></use></svg>',name:"list",prefix:"* ",tipPosition:"n"},{hotkey:"⌘O",icon:'<svg><use xlink:href="#vditor-icon-ordered-list"></use></svg>',name:"ordered-list",prefix:"1. ",tipPosition:"n"},{hotkey:"⌘J",icon:'<svg><use xlink:href="#vditor-icon-check"></use></svg>',name:"check",prefix:"* [ ] ",tipPosition:"n"},{hotkey:"⇧⌘I",icon:'<svg><use xlink:href="#vditor-icon-outdent"></use></svg>',name:"outdent",tipPosition:"n"},{hotkey:"⇧⌘O",icon:'<svg><use xlink:href="#vditor-icon-indent"></use></svg>',name:"indent",tipPosition:"n"},{name:"|"},{hotkey:"⌘;",icon:'<svg><use xlink:href="#vditor-icon-quote"></use></svg>',name:"quote",prefix:"> ",tipPosition:"n"},{hotkey:"⇧⌘H",icon:'<svg><use xlink:href="#vditor-icon-line"></use></svg>',name:"line",prefix:"---",tipPosition:"n"},{hotkey:"⌘U",icon:'<svg><use xlink:href="#vditor-icon-code"></use></svg>',name:"code",prefix:"```",suffix:"\n```",tipPosition:"n"},{hotkey:"⌘G",icon:'<svg><use xlink:href="#vditor-icon-inline-code"></use></svg>',name:"inline-code",prefix:"`",suffix:"`",tipPosition:"n"},{hotkey:"⇧⌘B",icon:'<svg><use xlink:href="#vditor-icon-before"></use></svg>',name:"insert-before",tipPosition:"n"},{hotkey:"⇧⌘E",icon:'<svg><use xlink:href="#vditor-icon-after"></use></svg>',name:"insert-after",tipPosition:"n"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-upload"></use></svg>',name:"upload",tipPosition:"n"},{icon:'<svg><use xlink:href="#vditor-icon-record"></use></svg>',name:"record",tipPosition:"n"},{hotkey:"⌘M",icon:'<svg><use xlink:href="#vditor-icon-table"></use></svg>',name:"table",prefix:"| col1",suffix:` | col2 | col3 |
| --- | --- | --- |
|  |  |  |
|  |  |  |`,tipPosition:"n"},{name:"|"},{hotkey:"⌘Z",icon:'<svg><use xlink:href="#vditor-icon-undo"></use></svg>',name:"undo",tipPosition:"nw"},{hotkey:"⌘Y",icon:'<svg><use xlink:href="#vditor-icon-redo"></use></svg>',name:"redo",tipPosition:"nw"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-more"></use></svg>',name:"more",tipPosition:"e"},{hotkey:"⌘'",icon:'<svg><use xlink:href="#vditor-icon-fullscreen"></use></svg>',name:"fullscreen",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-edit"></use></svg>',name:"edit-mode",tipPosition:"nw"},{hotkey:"⌘P",icon:'<svg><use xlink:href="#vditor-icon-both"></use></svg>',name:"both",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-preview"></use></svg>',name:"preview",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',name:"outline",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-theme"></use></svg>',name:"content-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-code-theme"></use></svg>',name:"code-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-bug"></use></svg>',name:"devtools",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-info"></use></svg>',name:"info",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-help"></use></svg>',name:"help",tipPosition:"nw"},{name:"br"}],a=[];return t.forEach(function(o){var d=o;i.forEach(function(w){typeof o=="string"&&w.name===o&&(d=w),typeof o=="object"&&w.name===o.name&&(d=Object.assign({},w,o))}),o.toolbar&&(d.toolbar=n.mergeToolbar(o.toolbar)),a.push(d)}),a},e})(),Ll=(function(){function e(t){var n=this;this.composingLock=!1,this.commentIds=[];var i=document.createElement("div");i.className="vditor-wysiwyg",i.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>
<div class="vditor-panel vditor-panel--none"></div>
<div class="vditor-panel vditor-panel--none">
    <button type="button" aria-label="`).concat(window.VditorI18n.comment,`" class="vditor-icon vditor-tooltipped vditor-tooltipped__n">
        <svg><use xlink:href="#vditor-icon-comment"></use></svg>
    </button>
</div>`),this.element=i.firstElementChild,this.popover=i.firstElementChild.nextElementSibling,this.selectPopover=i.lastElementChild,this.bindEvent(t),kr(t,this.element),Vi(t,this.element),xr(t,this.element),Rr(t,this.element),Nr(t,this.element),Or(t,this.element),Dr(t,this.element,this.copy),Hr(t,this.element,this.copy),t.options.comment.enable&&(this.selectPopover.querySelector("button").onclick=function(){var a=Lute.NewNodeID(),o=getSelection().getRangeAt(0),d=o.cloneRange(),w=o.extractContents(),C,v,H=!1,j=!1;w.childNodes.forEach(function(G,ce){var ne=!1;if(G.nodeType===3?ne=!0:G.classList.contains("vditor-comment")?G.classList.contains("vditor-comment")&&G.setAttribute("data-cmtids",G.getAttribute("data-cmtids")+" "+a):ne=!0,ne)if(G.nodeType!==3&&G.getAttribute("data-block")==="0"&&ce===0&&d.startOffset>0)G.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(G.innerHTML,"</span>"),C=G;else if(G.nodeType!==3&&G.getAttribute("data-block")==="0"&&ce===w.childNodes.length-1&&d.endOffset<d.endContainer.textContent.length)G.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(G.innerHTML,"</span>"),v=G;else if(G.nodeType!==3&&G.getAttribute("data-block")==="0")ce===0?H=!0:ce===w.childNodes.length-1&&(j=!0),G.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(G.innerHTML,"</span>");else{var z=document.createElement("span");z.classList.add("vditor-comment"),z.setAttribute("data-cmtids",a),G.parentNode.insertBefore(z,G),z.appendChild(G)}});var Z=(0,f.F9)(d.startContainer);Z&&(C?(Z.insertAdjacentHTML("beforeend",C.innerHTML),C.remove()):Z.textContent.trim().replace(m.g.ZWSP,"")===""&&H&&Z.remove());var X=(0,f.F9)(d.endContainer);X&&(v?(X.insertAdjacentHTML("afterbegin",v.innerHTML),v.remove()):X.textContent.trim().replace(m.g.ZWSP,"")===""&&j&&X.remove()),o.insertNode(w),t.options.comment.add(a,o.toString(),n.getComments(t,!0)),ae(t,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),n.hideComment()})}return e.prototype.getComments=function(t,n){var i=this;if(n===void 0&&(n=!1),t.currentMode==="wysiwyg"&&t.options.comment.enable){this.commentIds=[],this.element.querySelectorAll(".vditor-comment").forEach(function(o){i.commentIds=i.commentIds.concat(o.getAttribute("data-cmtids").split(" "))}),this.commentIds=Array.from(new Set(this.commentIds));var a=[];if(n)return this.commentIds.forEach(function(o){a.push({id:o,top:i.element.querySelector('.vditor-comment[data-cmtids="'.concat(o,'"]')).offsetTop})}),a}else return[]},e.prototype.triggerRemoveComment=function(t){var n=function(o,d){var w=new Set(d);return o.filter(function(C){return!w.has(C)})};if(t.currentMode==="wysiwyg"&&t.options.comment.enable&&t.wysiwyg.commentIds.length>0){var i=JSON.parse(JSON.stringify(this.commentIds));this.getComments(t);var a=n(i,this.commentIds);a.length>0&&t.options.comment.remove(a)}},e.prototype.showComment=function(){var t=(0,I.Ny)(this.element);this.selectPopover.setAttribute("style","left:".concat(t.left,"px;display:block;top:").concat(Math.max(-8,t.top-21),"px"))},e.prototype.hideComment=function(){this.selectPopover.setAttribute("style","display:none")},e.prototype.unbindListener=function(){window.removeEventListener("scroll",this.scrollListener)},e.prototype.copy=function(t,n){var i=getSelection().getRangeAt(0);if(i.toString()!==""){t.stopPropagation(),t.preventDefault();var a=(0,f.lG)(i.startContainer,"CODE"),o=(0,f.lG)(i.endContainer,"CODE");if(a&&o&&o.isSameNode(a)){var d="";a.parentElement.tagName==="PRE"?d=i.toString():d="`"+i.toString()+"`",t.clipboardData.setData("text/plain",d),t.clipboardData.setData("text/html","");return}var w=(0,f.lG)(i.startContainer,"A"),C=(0,f.lG)(i.endContainer,"A");if(w&&C&&C.isSameNode(w)){var v=w.getAttribute("title")||"";v&&(v=' "'.concat(v,'"')),t.clipboardData.setData("text/plain","[".concat(i.toString(),"](").concat(w.getAttribute("href")).concat(v,")")),t.clipboardData.setData("text/html","");return}var H=document.createElement("div");H.appendChild(i.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorDOM2Md(H.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.unbindListener(),window.addEventListener("scroll",this.scrollListener=function(){if(A(t,["hint"]),!(n.popover.style.display!=="block"||n.selectPopover.style.display!=="block")){var i=parseInt(n.popover.getAttribute("data-top"),10);if(t.options.height!=="auto"){if(t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0){var a=Math.max(window.scrollY-t.element.offsetTop-8,Math.min(i-t.wysiwyg.element.scrollTop,n.element.clientHeight-21))+"px";n.popover.style.display==="block"&&(n.popover.style.top=a),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=a)}return}else if(!t.options.toolbarConfig.pin)return;var o=Math.max(i,window.scrollY-t.element.offsetTop-8)+"px";n.popover.style.display==="block"&&(n.popover.style.top=o),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=o)}}),this.element.addEventListener("scroll",function(){if(A(t,["hint"]),t.options.comment&&t.options.comment.enable&&t.options.comment.scroll&&t.options.comment.scroll(t.wysiwyg.element.scrollTop),n.popover.style.display==="block"){var i=parseInt(n.popover.getAttribute("data-top"),10)-t.wysiwyg.element.scrollTop,a=-8;t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0&&(a=window.scrollY-t.element.offsetTop+a);var o=Math.max(a,Math.min(i,n.element.clientHeight-21))+"px";n.popover.style.top=o,n.selectPopover.style.top=o}}),this.element.addEventListener("paste",function(i){er(t,i,{pasteCode:function(a){var o=(0,I.zh)(t),d=document.createElement("template");d.innerHTML=a,o.insertNode(d.content.cloneNode(!0));var w=(0,f.a1)(o.startContainer,"data-block","0");w?w.outerHTML=t.lute.SpinVditorDOM(w.outerHTML):t.wysiwyg.element.innerHTML=t.lute.SpinVditorDOM(t.wysiwyg.element.innerHTML),(0,I.ib)(t.wysiwyg.element,o)}})}),this.element.addEventListener("compositionstart",function(){n.composingLock=!0}),this.element.addEventListener("compositionend",function(i){var a=(0,R.W)(getSelection().getRangeAt(0).startContainer);if(a&&a.textContent===""){dt(t);return}(0,c.vU)()||Zn(t,getSelection().getRangeAt(0).cloneRange(),i),n.composingLock=!1}),this.element.addEventListener("input",function(i){if(!(i.inputType==="deleteByDrag"||i.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,ae(t);return}if(n.composingLock||i.data==="‘"||i.data==="“"||i.data==="《"){ae(t);return}var a=getSelection().getRangeAt(0),o=(0,f.F9)(a.startContainer);if(o||(ot(t,a),o=(0,f.F9)(a.startContainer)),!!o){for(var d=(0,I.im)(o,t.wysiwyg.element,a).start,w=!0,C=d-1;C>o.textContent.substr(0,d).lastIndexOf(`
`);C--)if(o.textContent.charAt(C)!==" "&&o.textContent.charAt(C)!=="	"){w=!1;break}d===0&&(w=!1);for(var v=!0,C=d-1;C<o.textContent.length;C++)if(o.textContent.charAt(C)!==" "&&o.textContent.charAt(C)!==`
`){v=!1;break}v&&/^#{1,6} $/.test(o.textContent)&&(v=!1);var H=(0,R.W)(getSelection().getRangeAt(0).startContainer);if(H&&H.textContent===""&&(dt(t),H.remove()),w&&o.getAttribute("data-type")!=="code-block"||v||Ur(o.innerHTML)||Br(o.innerHTML)&&o.previousElementSibling){typeof t.options.input=="function"&&t.options.input(D(t));return}i.inputType==="insertParagraph"&&n.element.innerHTML==="<p><br></p><p><br></p>"&&o.previousElementSibling.remove(),Zn(t,a,i)}}}),this.element.addEventListener("click",function(i){if(i.target.tagName==="INPUT"){var a=i.target;a.checked?a.setAttribute("checked","checked"):a.removeAttribute("checked"),n.preventInput=!0,getSelection().rangeCount>0&&(0,I.Hc)(getSelection().getRangeAt(0)),ae(t);return}if(i.target.tagName==="IMG"&&!i.target.parentElement.classList.contains("vditor-wysiwyg__preview")){i.target.getAttribute("data-type")==="link-ref"?Bi(t,i.target):Ys(i,t);return}var o=(0,f.lG)(i.target,"A");if(o){t.options.link.click?t.options.link.click(o):t.options.link.isOpen&&window.open(o.getAttribute("href")),i.preventDefault();return}var d=(0,I.zh)(t);if(i.target.isEqualNode(n.element)&&n.element.lastElementChild&&d.collapsed){var w=n.element.lastElementChild.getBoundingClientRect();i.y>w.top+w.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(m.g.ZWSP,"")===""?(d.selectNodeContents(n.element.lastElementChild),d.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(m.g.ZWSP,"<wbr></p>")),(0,I.ib)(n.element,d)))}Mt(t);var C=(0,f.fb)(i.target,"vditor-wysiwyg__preview");C||(C=(0,f.fb)((0,I.zh)(t).startContainer,"vditor-wysiwyg__preview")),C&&qe(C,t),rn(i,t)}),this.element.addEventListener("keyup",function(i){if(!(i.isComposing||(0,c.yl)(i))){i.key==="Enter"&&Ve(t),(i.key==="Backspace"||i.key==="Delete")&&t.wysiwyg.element.innerHTML!==""&&t.wysiwyg.element.childNodes.length===1&&t.wysiwyg.element.firstElementChild&&t.wysiwyg.element.firstElementChild.tagName==="P"&&t.wysiwyg.element.firstElementChild.childElementCount===0&&(t.wysiwyg.element.textContent===""||t.wysiwyg.element.textContent===`
`)&&(t.wysiwyg.element.innerHTML="");var a=(0,I.zh)(t);if(i.key==="Backspace"&&(0,c.vU)()&&a.startContainer.textContent===`
`&&a.startOffset===1&&(a.startContainer.textContent=""),ot(t,a),Mt(t),!(i.key!=="ArrowDown"&&i.key!=="ArrowRight"&&i.key!=="Backspace"&&i.key!=="ArrowLeft"&&i.key!=="ArrowUp")){(i.key==="ArrowLeft"||i.key==="ArrowRight")&&t.hint.render(t);var o=(0,f.fb)(a.startContainer,"vditor-wysiwyg__preview");if(!o&&a.startContainer.nodeType!==3&&a.startOffset>0){var d=a.startContainer;d.classList.contains("vditor-wysiwyg__block")&&(o=d.lastElementChild)}if(o){var w=o.previousElementSibling;if(w.style.display==="none"){i.key==="ArrowDown"||i.key==="ArrowRight"?qe(o,t):qe(o,t,!1);return}var C=o.previousElementSibling;if(C.tagName==="PRE"&&(C=C.firstElementChild),i.key==="ArrowDown"||i.key==="ArrowRight"){var d=o.parentElement,v=Qe(d);if(v&&v.nodeType!==3){var H=v.querySelector(".vditor-wysiwyg__preview");if(H){qe(H,t);return}}if(v.nodeType===3){for(;v.textContent.length===0&&v.nextSibling;)v=v.nextSibling;a.setStart(v,1)}else a.setStart(v.firstChild,0)}else a.selectNodeContents(C),a.collapse(!1)}}}})},e})(),Al=(function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}})(),kl=(function(e){Al(t,e);function t(n,i){var a=e.call(this)||this;if(a.isDestroyed=!1,a.version=m.H,typeof n=="string"){if(i?i.cache?i.cache.id||(i.cache.id="vditor".concat(n)):i.cache={id:"vditor".concat(n)}:i={cache:{id:"vditor".concat(n)}},!document.getElementById(n))return a.showErrorTip("Failed to get element by id: ".concat(n)),a;n=document.getElementById(n)}var o=new Ml(i),d=o.merge();if(d.i18n)window.VditorI18n=d.i18n,a.init(n,d);else if(["de_DE","en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(d.lang)){var w="vditorI18nScript",C=w+d.lang;document.querySelectorAll('head script[id^="'.concat(w,'"]')).forEach(function(v){v.id!==C&&document.head.removeChild(v)}),(0,k.G)("".concat(d.cdn,"/dist/js/i18n/").concat(d.lang,".js"),C).then(function(){a.init(n,d)}).catch(function(v){a.showErrorTip("GET ".concat(d.cdn,"/dist/js/i18n/").concat(d.lang,".js net::ERR_ABORTED 404 (Not Found)"))})}else throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");return a}return t.prototype.showErrorTip=function(n){var i=new ha;document.body.appendChild(i.element),i.show(n,0)},t.prototype.updateToolbarConfig=function(n){this.vditor.toolbar.updateConfig(this.vditor,n)},t.prototype.setTheme=function(n,i,a,o){this.vditor.options.theme=n,F(this.vditor),i&&(this.vditor.options.preview.theme.current=i,(0,U.Z)(i,o||this.vditor.options.preview.theme.path)),a&&(this.vditor.options.preview.hljs.style=a,(0,ma.Y)(a,this.vditor.options.cdn))},t.prototype.getValue=function(){return D(this.vditor)},t.prototype.getCurrentMode=function(){return this.vditor.currentMode},t.prototype.focus=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.focus():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.focus():this.vditor.currentMode==="ir"&&this.vditor.ir.element.focus()},t.prototype.blur=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.blur():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.blur():this.vditor.currentMode==="ir"&&this.vditor.ir.element.blur()},t.prototype.disabled=function(){A(this.vditor,["subToolbar","hint","popover"]),y(this.vditor.toolbar.elements,m.g.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","false")},t.prototype.enable=function(){E(this.vditor.toolbar.elements,m.g.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor.undo.resetIcon(this.vditor),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","true")},t.prototype.getSelection=function(){if(this.vditor.currentMode==="wysiwyg")return Sn(this.vditor.wysiwyg.element);if(this.vditor.currentMode==="sv")return Sn(this.vditor.sv.element);if(this.vditor.currentMode==="ir")return Sn(this.vditor.ir.element)},t.prototype.renderPreview=function(n){this.vditor.preview.render(this.vditor,n)},t.prototype.getCursorPosition=function(){return(0,I.Ny)(this.vditor[this.vditor.currentMode].element)},t.prototype.isUploading=function(){return this.vditor.upload.isUploading},t.prototype.clearCache=function(){this.vditor.options.cache.enable&&(0,c.pK)()&&localStorage.removeItem(this.vditor.options.cache.id)},t.prototype.disabledCache=function(){this.vditor.options.cache.enable=!1},t.prototype.enableCache=function(){if(!this.vditor.options.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");this.vditor.options.cache.enable=!0},t.prototype.html2md=function(n){return this.vditor.lute.HTML2Md(n)},t.prototype.exportJSON=function(n){return this.vditor.lute.RenderJSON(n)},t.prototype.getHTML=function(){return da(this.vditor)},t.prototype.tip=function(n,i){this.vditor.tip.show(n,i)},t.prototype.setPreviewMode=function(n){Fr(n,this.vditor)},t.prototype.deleteValue=function(){window.getSelection().isCollapsed||document.execCommand("delete",!1)},t.prototype.updateValue=function(n){document.execCommand("insertHTML",!1,n)},t.prototype.insertValue=function(n,i){i===void 0&&(i=!0);var a=(0,I.zh)(this.vditor);a.collapse(!0);var o=document.createElement("template");o.innerHTML=n,a.insertNode(o.content.cloneNode(!0)),a.collapse(!1),this.vditor.currentMode==="sv"?(this.vditor.sv.preventInput=!0,i&&B(this.vditor)):this.vditor.currentMode==="wysiwyg"?i&&Zn(this.vditor,getSelection().getRangeAt(0)):this.vditor.currentMode==="ir"&&(this.vditor.ir.preventInput=!0,i&&st(this.vditor,getSelection().getRangeAt(0),!0))},t.prototype.insertMD=function(n){this.vditor.currentMode==="ir"?(0,I.oC)(this.vditor.lute.Md2VditorIRDOM(n),this.vditor):this.vditor.currentMode==="wysiwyg"?(0,I.oC)(this.vditor.lute.Md2VditorDOM(n),this.vditor):Ir(this.vditor,n),this.vditor.outline.render(this.vditor),Ee(this.vditor)},t.prototype.setValue=function(n,i){var a=this;i===void 0&&(i=!1),this.vditor.currentMode==="sv"?(this.vditor.sv.element.innerHTML="<div data-block='0'>".concat(this.vditor.lute.SpinVditorSVDOM(n),"</div>"),ze(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})):this.vditor.currentMode==="wysiwyg"?Ui(this.vditor,n,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}):(this.vditor.ir.element.innerHTML=this.vditor.lute.Md2VditorIRDOM(n),this.vditor.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(o){Ne(o,a.vditor)}),Rt(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})),this.vditor.outline.render(this.vditor),n||(A(this.vditor,["emoji","headings","submenu","hint"]),this.vditor.wysiwyg.popover&&(this.vditor.wysiwyg.popover.style.display="none"),this.clearCache()),i&&this.clearStack()},t.prototype.insertEmptyBlock=function(n){Cn(this.vditor,n)},t.prototype.clearStack=function(){this.vditor.undo.clearStack(this.vditor),this.vditor.undo.addToUndoStack(this.vditor)},t.prototype.destroy=function(){this.vditor.element.innerHTML=this.vditor.originalInnerHTML,this.vditor.element.classList.remove("vditor"),this.vditor.element.removeAttribute("style");var n=document.getElementById("vditorIconScript");n&&n.remove(),this.clearCache(),he(),this.vditor.wysiwyg.unbindListener(),this.vditor.options.after=void 0,this.isDestroyed=!0},t.prototype.getCommentIds=function(){return this.vditor.currentMode!=="wysiwyg"?[]:this.vditor.wysiwyg.getComments(this.vditor,!0)},t.prototype.hlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var i=function(a){a.classList.remove("vditor-comment--hover"),n.forEach(function(o){a.getAttribute("data-cmtids").indexOf(o)>-1&&a.classList.add("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(a){i(a)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(a){i(a)})}},t.prototype.unHlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var i=function(a){n.forEach(function(o){a.getAttribute("data-cmtids").indexOf(o)>-1&&a.classList.remove("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(a){i(a)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(a){i(a)})}},t.prototype.removeCommentIds=function(n){var i=this;if(this.vditor.currentMode==="wysiwyg"){var a=function(o,d){var w=o.getAttribute("data-cmtids").split(" ");w.find(function(C,v){if(C===d)return w.splice(v,1),!0}),w.length===0?(o.outerHTML=o.innerHTML,(0,I.zh)(i.vditor).collapse(!0)):o.setAttribute("data-cmtids",w.join(" "))};n.forEach(function(o){i.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(d){a(d,o)}),i.vditor.preview.element.style.display!=="none"&&i.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(d){a(d,o)})}),ae(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})}},t.prototype.init=function(n,i){var a=this;this.isDestroyed||(this.vditor={currentMode:i.mode,element:n,hint:new wo(i.hint.extend),lute:void 0,options:i,originalInnerHTML:n.innerHTML,outline:new _o(window.VditorI18n.outline),tip:new ha},this.vditor.sv=new Mo(this.vditor),this.vditor.undo=new Cl,this.vditor.wysiwyg=new Ll(this.vditor),this.vditor.ir=new vo(this.vditor),this.vditor.toolbar=new _l(this.vditor),i.resize.enable&&(this.vditor.resize=new To(this.vditor)),this.vditor.toolbar.elements.devtools&&(this.vditor.devtools=new u),(i.upload.url||i.upload.handler)&&(this.vditor.upload=new oo),(0,k.G)(i._lutePath||"".concat(i.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){a.vditor.lute=(0,Eo.X)({autoSpace:a.vditor.options.preview.markdown.autoSpace,gfmAutoLink:a.vditor.options.preview.markdown.gfmAutoLink,codeBlockPreview:a.vditor.options.preview.markdown.codeBlockPreview,emojiSite:a.vditor.options.hint.emojiPath,emojis:a.vditor.options.hint.emoji,fixTermTypo:a.vditor.options.preview.markdown.fixTermTypo,footnotes:a.vditor.options.preview.markdown.footnotes,headingAnchor:!1,inlineMathDigit:a.vditor.options.preview.math.inlineDigit,linkBase:a.vditor.options.preview.markdown.linkBase,linkPrefix:a.vditor.options.preview.markdown.linkPrefix,listStyle:a.vditor.options.preview.markdown.listStyle,mark:a.vditor.options.preview.markdown.mark,mathBlockPreview:a.vditor.options.preview.markdown.mathBlockPreview,paragraphBeginningSpace:a.vditor.options.preview.markdown.paragraphBeginningSpace,sanitize:a.vditor.options.preview.markdown.sanitize,toc:a.vditor.options.preview.markdown.toc}),a.vditor.preview=new Co(a.vditor),ee(a.vditor),i.after&&i.after(),i.icon&&(0,k.J)("".concat(i.cdn,"/dist/js/icons/").concat(i.icon,".js"),"vditorIconScript")}))},t})(M.default);const xl=kl})(),S=S.default,S})()})})(fr)),fr.exports}var uf=cf();const ff=of(uf),df=["id"],pf=_s({__name:"Vditor",props:Wc({disabled:{type:Boolean,default:!1},height:{default:300},mode:{default:"wysiwyg"},placeholder:{default:"请输入"}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(r){Vu(M=>({"2ef04085":s.height+"px"}));const s=r,l=Xr(),p=du(r,"modelValue"),h=Xr(!1),S=Xr(`vditor-${Math.random().toString(36).substr(2,9)}`);return Un(()=>p.value,M=>{l.value&&h.value&&M!==l.value.getValue()&&l.value.setValue(M||"")},{deep:!0}),xi(()=>{l.value=new ff(S.value,{cdn:"",height:s.height,width:"100%",mode:s.mode,cache:{enable:!1},minHeight:s.height,toolbar:["headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","table","quote","line","code","inline-code","|","upload","edit-mode","both","emoji","|","undo","redo"],preview:{markdown:{autoSpace:!0,fixTermTypo:!0,paragraphBeginningSpace:!0,sanitize:!0,listStyle:!0,mark:!0},hljs:{style:"agate"},math:{engine:"KaTeX"}},hint:{extend:[]},after:()=>{h.value=!0,p.value&&l.value?.setValue(p.value)},input:M=>{p.value=M},placeholder:s.placeholder}),s.disabled&&l.value.disabled()}),(M,m)=>(Ks(),qs("div",{id:S.value,class:"vditor-container"},null,8,df))}}),hf=(r,s)=>{const l=r.__vccOpts||r;for(const[p,h]of s)l[p]=h;return l},mf=hf(pf,[["__scopeId","data-v-3f6a6c79"]]),gf=_s({__name:"App",setup(r){return(s,l)=>(Ks(),qs(Et,null,[l[0]||(l[0]=Ni("p",null,"编辑器",-1)),Jt(mf)],64))}});rf(gf).mount("#app");
