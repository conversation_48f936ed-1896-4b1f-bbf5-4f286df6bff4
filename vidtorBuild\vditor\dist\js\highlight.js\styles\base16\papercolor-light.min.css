/*!
  Theme: PaperColor Light
  Author: <PERSON> (http://github.com/j<PERSON><PERSON>) based on PaperColor Theme (https://github.com/NLKNguyen/papercolor-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#444;background:#eee}.hljs ::selection,.hljs::selection{background-color:#008700;color:#444}.hljs-comment{color:#5f8700}.hljs-tag{color:#0087af}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#444}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#bcbcbc}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#d70000}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#d70087}.hljs-strong{font-weight:700;color:#d70087}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#8700af}.hljs-attribute,.hljs-built_in,.hljs-doctag,.hljs-function .hljs-title,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#d75f00}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#005faf}.hljs-emphasis{color:#005faf;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#005f87}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
