pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}/*!
 * Theme: FelipeC
 * Author: (c) 2021 <PERSON> <<EMAIL>>
 * Website: https://github.com/felipec/vim-felipec
 *
 * Autogenerated with vim-felipec's generator.
*/.hljs{color:#dddde1;background:#1e1e22}.hljs ::selection,.hljs::selection{color:#1e1e22;background:#bf8fef}.hljs-code,.hljs-comment,.hljs-quote{color:#888896}.hljs-deletion,.hljs-literal,.hljs-number{color:#ef8f8f}.hljs-doctag,.hljs-meta,.hljs-operator,.hljs-punctuation,.hljs-selector-attr,.hljs-subst,.hljs-template-variable{color:#efbf8f}.hljs-type{color:#efef8f}.hljs-selector-class,.hljs-selector-id,.hljs-tag,.hljs-title{color:#bfef8f}.hljs-addition,.hljs-regexp,.hljs-string{color:#8fef8f}.hljs-class,.hljs-property{color:#8fefbf}.hljs-name,.hljs-selector-tag{color:#8fefef}.hljs-built_in,.hljs-keyword{color:#8fbfef}.hljs-bullet,.hljs-section{color:#8f8fef}.hljs-selector-pseudo{color:#bf8fef}.hljs-attr,.hljs-attribute,.hljs-params,.hljs-variable{color:#ef8fef}.hljs-link,.hljs-symbol{color:#ef8fbf}.hljs-literal,.hljs-strong,.hljs-title{font-weight:700}.hljs-emphasis{font-style:italic}
